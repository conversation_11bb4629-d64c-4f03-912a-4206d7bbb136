"""
Enterprise-grade Qt data models for chart visualization.
Implements proper Model/View separation with zero computation in UI layer.
"""

from PyQt6.QtCore import QAbstractTableModel, QModelIndex, Qt, pyqtSignal, QObject
from PyQt6.QtGui import QColor
from typing import Dict, Any, List, Optional, Tuple
import logging


class ChartDataModel(QObject):
    """
    Enterprise-grade data model for chart visualization.
    All data processing is done in backend - UI only renders pre-computed data.
    """

    # Signals for data updates
    data_updated = pyqtSignal()
    bounds_updated = pyqtSignal(dict)  # Emits pre-computed bounds

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()
        self._raw_data: Dict[str, Any] = {}
        self._render_data: Dict[str, Any] = {}
        self._bounds: Dict[str, float] = {}

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for data model."""
        logger = logging.getLogger("ChartDataModel")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def update_data(self, market_data: Dict[str, Any]):
        """
        Update model with pre-processed data from backend.
        No computation performed - only data storage and signal emission.
        """
        try:
            self._raw_data = market_data.copy()
            self._prepare_render_data()
            self.data_updated.emit()
            self.logger.debug("Chart data model updated successfully")
        except Exception as e:
            self.logger.error(f"Error updating chart data model: {e}")

    def _prepare_render_data(self):
        """
        Prepare render-optimized data structures from backend data.
        No mathematical computations - only data restructuring for UI efficiency.
        """
        self._render_data = {}

        # Extract pre-computed candlestick data
        if "candlestick_data" in self._raw_data:
            self._render_data["candlesticks"] = self._raw_data["candlestick_data"]

        # Extract pre-computed rebased candlestick data
        if "rebased_candlestick_data" in self._raw_data:
            self._render_data["rebased_candlesticks"] = self._raw_data[
                "rebased_candlestick_data"
            ]

        # Extract pre-computed bounds (no UI calculation needed)
        if "chart_bounds" in self._raw_data:
            self._bounds = self._raw_data["chart_bounds"]
            self.bounds_updated.emit(self._bounds)

        # Extract pre-computed visualization elements
        for key in [
            "donchian_stairstep_data",
            "vector_plot_data",
            "peak_trough_display_data",
            "stdev_infinity_lines",
            "mean_peak_trough_levels",
            "predictive_cycle_lines",
        ]:
            if key in self._raw_data:
                self._render_data[key] = self._raw_data[key]

    def get_candlestick_data(self) -> List[Dict[str, Any]]:
        """Get pre-computed candlestick render data."""
        return self._render_data.get("candlesticks", [])

    def get_rebased_candlestick_data(self) -> List[Dict[str, Any]]:
        """Get pre-computed rebased candlestick render data."""
        return self._render_data.get("rebased_candlesticks", [])

    def get_bounds(self) -> Dict[str, float]:
        """Get pre-computed chart bounds."""
        return self._bounds.copy()

    def get_donchian_data(self) -> Dict[str, Any]:
        """Get pre-computed Donchian visualization data."""
        return self._render_data.get("donchian_stairstep_data", {})

    def get_vector_data(self) -> List[Dict[str, float]]:
        """Get pre-computed vector plot data."""
        return self._render_data.get("vector_plot_data", [])

    def get_peak_trough_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get pre-computed peak/trough display data."""
        return self._render_data.get(
            "peak_trough_display_data", {"peaks": [], "troughs": []}
        )

    def get_stdev_lines_data(self) -> Dict[str, Any]:
        """Get pre-computed standard deviation lines data."""
        return self._render_data.get("stdev_infinity_lines", {})

    def get_mean_levels_data(self) -> Dict[str, float]:
        """Get pre-computed mean peak/trough levels."""
        return self._render_data.get("mean_peak_trough_levels", {})

    def get_predictive_cycle_data(self) -> Dict[str, Any]:
        """Get pre-computed predictive cycle lines data."""
        return self._render_data.get("predictive_cycle_lines", {})

    def get_crosshair_info(self, candle_index: int) -> Dict[str, Any]:
        """Get pre-computed crosshair information for given candle index."""
        crosshair_lookup = self._raw_data.get("crosshair_info_lookup", {})
        return crosshair_lookup.get(str(candle_index), {})

    def has_data(self) -> bool:
        """Check if model contains valid data."""
        return bool(self._raw_data)

    def clear(self):
        """Clear all data from the model."""
        self._raw_data.clear()
        self._render_data.clear()
        self._bounds.clear()
        self.data_updated.emit()


class CandlestickRenderItem:
    """
    Pure rendering item for candlesticks with pre-computed data.
    No mathematical operations - only rendering pre-computed values.
    """

    def __init__(self, render_data: List[Dict[str, Any]]):
        self.render_data = render_data
        self._bounds = self._extract_bounds()

    def _extract_bounds(self) -> Dict[str, float]:
        """Extract pre-computed bounds from render data."""
        if not self.render_data:
            return {"x_min": 0, "x_max": 1, "y_min": 0, "y_max": 1}

        # Bounds should be pre-computed in backend
        # This is just extracting the first and last values
        first_item = self.render_data[0]
        last_item = self.render_data[-1]

        return {
            "x_min": first_item.get("x", 0) - 1,
            "x_max": last_item.get("x", 1) + 1,
            "y_min": min(
                item.get("low", 0)
                for item in self.render_data
                if item.get("is_valid", True)
            ),
            "y_max": max(
                item.get("high", 1)
                for item in self.render_data
                if item.get("is_valid", True)
            ),
        }

    def get_bounds(self) -> Dict[str, float]:
        """Get pre-computed bounds."""
        return self._bounds

    def get_render_data(self) -> List[Dict[str, Any]]:
        """Get render data for painting."""
        return self.render_data


class BandwidthChartModel(QObject):
    """
    Data model for bandwidth chart with pre-computed data.
    Eliminates all UI-side calculations.
    """

    data_updated = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self._bandwidth_data: List[float] = []
        self._timestamps: List[float] = []
        self._strip_segments: List[Dict[str, Any]] = []
        self._percentage_levels: Dict[str, Any] = {}
        self._has_movement = False
        self._strip_position = 0.0

    def update_data(
        self,
        timestamps: List[float],
        bandwidth_values: List[float],
        strip_segments: List[Dict[str, Any]],
        percentage_levels: Dict[str, Any],
    ):
        """Update with pre-processed data from backend."""
        self._timestamps = timestamps
        self._bandwidth_data = bandwidth_values
        self._strip_segments = strip_segments
        self._percentage_levels = percentage_levels

        # Movement detection should be pre-computed in backend
        self._has_movement = percentage_levels.get("has_movement", True)
        self._strip_position = percentage_levels.get("strip_position", 0.0)

        self.data_updated.emit()

    def get_bandwidth_data(self) -> Tuple[List[float], List[float]]:
        """Get bandwidth plot data."""
        return self._timestamps, self._bandwidth_data

    def get_strip_segments(self) -> List[Dict[str, Any]]:
        """Get Donchian strip segments."""
        return self._strip_segments

    def get_percentage_levels(self) -> Dict[str, Any]:
        """Get percentage levels data."""
        return self._percentage_levels

    def has_movement(self) -> bool:
        """Check if bandwidth has significant movement."""
        return self._has_movement

    def get_strip_position(self) -> float:
        """Get pre-computed strip position."""
        return self._strip_position

    def has_data(self) -> bool:
        """Check if model has valid data."""
        return bool(self._bandwidth_data)

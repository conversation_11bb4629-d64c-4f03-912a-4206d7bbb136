name: Release Automation

on:
  push:
    tags:
      - 'v*'  # Trigger on version tags (v1.0.0, v1.2.3, etc.)

env:
  PYTHON_VERSION: '3.11'
  
jobs:
  # ============================================================================
  # Validate Release
  # ============================================================================
  validate:
    name: Validate Release
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      is_prerelease: ${{ steps.version.outputs.prerelease }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Validate tag format
        id: version
        run: |
          TAG=${GITHUB_REF#refs/tags/}
          echo "Tag: $TAG"
          
          # Validate semantic version format
          if [[ ! $TAG =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9.-]+)?$ ]]; then
            echo "❌ Invalid tag format: $TAG"
            echo "Expected format: v1.2.3 or v1.2.3-alpha.1"
            exit 1
          fi
          
          VERSION=${TAG#v}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          
          # Check if this is a prerelease
          if [[ $TAG =~ -[a-zA-Z] ]]; then
            echo "prerelease=true" >> $GITHUB_OUTPUT
            echo "📦 Prerelease detected: $TAG"
          else
            echo "prerelease=false" >> $GITHUB_OUTPUT
            echo "🚀 Stable release detected: $TAG"
          fi
      
      - name: Check changelog entry
        run: |
          VERSION=${{ steps.version.outputs.version }}
          if ! grep -q "## \[$VERSION\]" CHANGELOG.md; then
            echo "❌ No changelog entry found for version $VERSION"
            echo "Please add a changelog entry before releasing"
            exit 1
          fi
          echo "✅ Changelog entry found for version $VERSION"

  # ============================================================================
  # Build and Test
  # ============================================================================
  build-and-test:
    name: Build and Test
    needs: validate
    uses: ./.github/workflows/ci.yml
    secrets: inherit

  # ============================================================================
  # Build Release Artifacts
  # ============================================================================
  build-artifacts:
    name: Build Release Artifacts
    runs-on: ${{ matrix.os }}
    needs: [validate, build-and-test]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
      
      - name: Install build dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build twine cibuildwheel
      
      - name: Build source distribution (Ubuntu only)
        if: matrix.os == 'ubuntu-latest'
        run: |
          python -m build --sdist
          echo "✅ Source distribution built"
      
      - name: Build wheels
        uses: pypa/cibuildwheel@v2.16.2
        env:
          # Build for Python 3.9-3.12
          CIBW_BUILD: cp39-* cp310-* cp311-* cp312-*
          # Skip 32-bit and PyPy
          CIBW_SKIP: "*-win32 *-manylinux_i686 pp*"
          # Install build dependencies
          CIBW_BEFORE_BUILD: pip install pybind11 cmake ninja
          # Test wheels
          CIBW_TEST_REQUIRES: pytest
          CIBW_TEST_COMMAND: pytest {project}/tests/ -v --tb=short -x
          # Build settings
          CIBW_BUILD_VERBOSITY: 1
          # Environment
          CIBW_ENVIRONMENT: CMAKE_BUILD_TYPE=Release ENABLE_SIMD=ON
      
      - name: Verify artifacts
        run: |
          ls -la dist/
          python -m twine check dist/*
          echo "✅ All artifacts verified"
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: release-artifacts-${{ matrix.os }}
          path: dist/

  # ============================================================================
  # Security Scan
  # ============================================================================
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-artifacts
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          name: release-artifacts-ubuntu-latest
          path: dist/
      
      - name: Security scan with Trivy
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # ============================================================================
  # Create GitHub Release
  # ============================================================================
  github-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    needs: [validate, build-artifacts, security-scan]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          pattern: release-artifacts-*
          path: dist/
          merge-multiple: true
      
      - name: Generate release notes
        id: release_notes
        run: |
          VERSION=${{ needs.validate.outputs.version }}
          
          # Extract changelog section for this version
          awk "/## \[$VERSION\]/{flag=1; next} /## \[/{flag=0} flag" CHANGELOG.md > release_notes.md
          
          # Add download instructions
          cat >> release_notes.md << EOF
          
          ## Installation
          
          ### PyPI (Recommended)
          \`\`\`bash
          pip install datadriven-trading==$VERSION
          \`\`\`
          
          ### Manual Installation
          Download the appropriate wheel for your platform from the assets below.
          
          ### Verification
          \`\`\`bash
          python -c "import datadriven; print(datadriven.get_version())"
          \`\`\`
          
          ## What's Changed
          See [CHANGELOG.md](https://github.com/your-org/datadriven/blob/main/CHANGELOG.md) for complete details.
          EOF
          
          echo "Release notes generated for version $VERSION"
      
      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          name: "DataDriven Trading Platform v${{ needs.validate.outputs.version }}"
          body_path: release_notes.md
          files: |
            dist/*.whl
            dist/*.tar.gz
          prerelease: ${{ needs.validate.outputs.is_prerelease == 'true' }}
          generate_release_notes: true
          make_latest: ${{ needs.validate.outputs.is_prerelease == 'false' }}

  # ============================================================================
  # Publish to PyPI
  # ============================================================================
  pypi-publish:
    name: Publish to PyPI
    runs-on: ubuntu-latest
    needs: [validate, github-release]
    if: needs.validate.outputs.is_prerelease == 'false'  # Only stable releases
    environment: 
      name: pypi
      url: https://pypi.org/p/datadriven-trading
    
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          pattern: release-artifacts-*
          path: dist/
          merge-multiple: true
      
      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: dist/
          verify_metadata: true
          skip_existing: false
          verbose: true

  # ============================================================================
  # Publish to Test PyPI (for prereleases)
  # ============================================================================
  test-pypi-publish:
    name: Publish to Test PyPI
    runs-on: ubuntu-latest
    needs: [validate, github-release]
    if: needs.validate.outputs.is_prerelease == 'true'  # Only prereleases
    environment:
      name: testpypi
      url: https://test.pypi.org/p/datadriven-trading
    
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v3
        with:
          pattern: release-artifacts-*
          path: dist/
          merge-multiple: true
      
      - name: Publish to Test PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.TEST_PYPI_API_TOKEN }}
          repository_url: https://test.pypi.org/legacy/
          packages_dir: dist/
          verify_metadata: true
          skip_existing: true
          verbose: true

  # ============================================================================
  # Post-Release Tasks
  # ============================================================================
  post-release:
    name: Post-Release Tasks
    runs-on: ubuntu-latest
    needs: [validate, pypi-publish]
    if: always() && needs.validate.outputs.is_prerelease == 'false'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Update development version
        run: |
          VERSION=${{ needs.validate.outputs.version }}
          
          # Parse version components
          IFS='.' read -r MAJOR MINOR PATCH <<< "$VERSION"
          
          # Increment minor version for next development cycle
          NEXT_MINOR=$((MINOR + 1))
          NEXT_VERSION="$MAJOR.$NEXT_MINOR.0"
          
          echo "Current version: $VERSION"
          echo "Next development version: $NEXT_VERSION"
          
          # Update pyproject.toml
          sed -i "s/version = \"$VERSION\"/version = \"$NEXT_VERSION-dev\"/" pyproject.toml
          
          # Update CHANGELOG.md
          sed -i "/## \[Unreleased\]/a\\
          \\
          ### Added\\
          \\
          ### Changed\\
          \\
          ### Fixed\\
          " CHANGELOG.md
      
      - name: Create development PR
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: bump version to ${{ needs.validate.outputs.version }}-dev"
          title: "chore: prepare for next development cycle"
          body: |
            Automated post-release version bump.
            
            - Updated version to next development cycle
            - Prepared CHANGELOG.md for next release
          branch: chore/post-release-v${{ needs.validate.outputs.version }}
          delete-branch: true

# -*- coding: utf-8 -*-

# Resource object code (Python)
# Created by: compile_resources_python.py
# Source: app_resources.qrc

from PyQt6 import QtCore

qt_resource_data = {
    0: b"""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""",
    1: b"""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""",
}

qt_resource_name = {
    "/styles/dark_theme.qss": 0,
    "/icons/icons/settings.svg": 1,
}

def qInitResources():
    """Initialize Qt resources."""
    try:
        for path, file_id in qt_resource_name.items():
            data = base64.b64decode(qt_resource_data[file_id])
            QtCore.qRegisterResourceData(0x03, data, path.encode())
        return True
    except Exception as e:
        print(f"Failed to initialize resources: {e}")
        return False

def qCleanupResources():
    """Cleanup Qt resources."""
    try:
        for path in qt_resource_name.keys():
            QtCore.qUnregisterResourceData(0x03, path.encode())
    except Exception:
        pass

# Auto-initialize resources when module is imported
import base64
qInitResources()
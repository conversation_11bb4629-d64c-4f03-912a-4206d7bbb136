#!/usr/bin/env python3
"""
Python-based resource compilation for Qt resources.
Alternative to rcc/pyrcc6 when Qt development tools are not available.
"""

import sys
import base64
from pathlib import Path
import xml.etree.ElementTree as ET


def compile_qrc_to_python(qrc_file: Path, output_file: Path):
    """Compile QRC file to Python module."""

    try:
        # Parse QRC file
        tree = ET.parse(qrc_file)
        root = tree.getroot()

        # Start building Python code
        python_code = [
            "# -*- coding: utf-8 -*-",
            "",
            "# Resource object code (Python)",
            f"# Created by: compile_resources_python.py",
            f"# Source: {qrc_file.name}",
            "",
            "from PyQt6 import QtCore",
            "",
            "qt_resource_data = {",
        ]

        resource_map = {}
        file_id = 0

        # Process each resource
        for qresource in root.findall("qresource"):
            prefix = qresource.get("prefix", "")

            for file_elem in qresource.findall("file"):
                file_path = file_elem.text
                resource_path = f"{prefix}/{file_path}" if prefix else file_path

                # Read file content
                actual_file = qrc_file.parent / file_path
                if actual_file.exists():
                    try:
                        with open(actual_file, "rb") as f:
                            file_data = f.read()

                        # Encode as base64 for embedding
                        encoded_data = base64.b64encode(file_data).decode("ascii")

                        # Add to Python code
                        python_code.append(f'    {file_id}: b"""{encoded_data}""",')
                        resource_map[resource_path] = file_id
                        file_id += 1

                        print(f"  ✓ Processed: {file_path}")

                    except Exception as e:
                        print(f"  ✗ Failed to read {file_path}: {e}")
                else:
                    print(f"  ✗ File not found: {actual_file}")

        # Close data dictionary
        python_code.append("}")
        python_code.append("")

        # Add resource map
        python_code.append("qt_resource_name = {")
        for resource_path, file_id in resource_map.items():
            python_code.append(f'    "{resource_path}": {file_id},')
        python_code.append("}")
        python_code.append("")

        # Add resource registration function
        python_code.extend(
            [
                "def qInitResources():",
                '    """Initialize Qt resources."""',
                "    try:",
                "        for path, file_id in qt_resource_name.items():",
                "            data = base64.b64decode(qt_resource_data[file_id])",
                "            QtCore.qRegisterResourceData(0x03, data, path.encode())",
                "        return True",
                "    except Exception as e:",
                '        print(f"Failed to initialize resources: {e}")',
                "        return False",
                "",
                "def qCleanupResources():",
                '    """Cleanup Qt resources."""',
                "    try:",
                "        for path in qt_resource_name.keys():",
                "            QtCore.qUnregisterResourceData(0x03, path.encode())",
                "    except Exception:",
                "        pass",
                "",
                "# Auto-initialize resources when module is imported",
                "import base64",
                "qInitResources()",
            ]
        )

        # Write Python file
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("\n".join(python_code))

        print(f"  ✓ Generated Python resource file: {output_file}")
        return True

    except Exception as e:
        print(f"  ✗ Failed to compile {qrc_file}: {e}")
        return False


def create_simple_resource_loader():
    """Create a simple resource loader for missing resources."""

    resources_dir = Path("assets/resources")

    # Create a simple Python resource file
    simple_resources = resources_dir / "simple_resources.py"

    content = '''# -*- coding: utf-8 -*-
"""
Simple resource loader for DataDriven Trading Platform.
Provides fallback resource loading when Qt resources are not available.
"""

from PyQt6.QtCore import QFile, QIODevice
from PyQt6.QtGui import QPixmap, QFont, QFontDatabase
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def load_stylesheet(name: str = "dark_theme.qss") -> str:
    """Load stylesheet from resources directory."""
    try:
        stylesheet_path = Path(__file__).parent / name
        if stylesheet_path.exists():
            with open(stylesheet_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            logger.warning(f"Stylesheet not found: {stylesheet_path}")
            return ""
    except Exception as e:
        logger.error(f"Failed to load stylesheet {name}: {e}")
        return ""

def load_icon(name: str) -> QPixmap:
    """Load icon from resources directory."""
    try:
        icon_path = Path(__file__).parent / "icons" / name
        if icon_path.exists():
            return QPixmap(str(icon_path))
        else:
            logger.warning(f"Icon not found: {icon_path}")
            return QPixmap()
    except Exception as e:
        logger.error(f"Failed to load icon {name}: {e}")
        return QPixmap()

def get_fallback_font(size: int = 9) -> QFont:
    """Get fallback system font."""
    # Try preferred fonts in order
    preferred_fonts = ["Segoe UI", "Source Sans Pro", "Inter", "Arial", "Helvetica"]
    
    for font_name in preferred_fonts:
        if QFontDatabase().hasFamily(font_name):
            logger.debug(f"Using system font: {font_name}")
            return QFont(font_name, size)
    
    # Final fallback
    logger.debug("Using default system font")
    return QFont("", size)

# Initialize simple resources
def initialize_simple_resources():
    """Initialize simple resource system."""
    logger.info("Simple resource system initialized")
    return True

# Auto-initialize
initialize_simple_resources()
'''

    with open(simple_resources, "w", encoding="utf-8") as f:
        f.write(content)

    print(f"✓ Created simple resource loader: {simple_resources}")
    return True


def main():
    """Main resource compilation function."""
    print("Python Resource Compilation")
    print("=" * 30)

    resources_dir = Path("assets/resources")
    if not resources_dir.exists():
        print("✗ Resources directory not found!")
        return False

    # Find QRC files
    qrc_files = list(resources_dir.glob("*.qrc"))

    if not qrc_files:
        print("No QRC files found, creating simple resource loader...")
        return create_simple_resource_loader()

    success = True

    for qrc_file in qrc_files:
        print(f"Compiling {qrc_file.name}...")

        # Generate Python resource file
        output_file = qrc_file.parent / f"{qrc_file.stem}_rc.py"

        if compile_qrc_to_python(qrc_file, output_file):
            print(f"  ✓ Successfully compiled {qrc_file.name}")
        else:
            print(f"  ✗ Failed to compile {qrc_file.name}")
            success = False

    # Also create simple resource loader as fallback
    create_simple_resource_loader()

    if success:
        print("\n✓ Resource compilation completed successfully!")
        print("\nTo use the resources in your application:")
        print("  import resources.app_resources_rc  # Auto-initializes resources")
        print("  # or")
        print(
            "  from resources.simple_resources import load_stylesheet, get_fallback_font"
        )
    else:
        print("\n✗ Some resources failed to compile!")

    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

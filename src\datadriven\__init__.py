"""
DataDriven Trading Platform

Enterprise-grade trading analysis platform with PyQt6 frontend and C++ compute kernels.
"""

__version__ = "1.0.0"
__author__ = "DataDriven Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Import version from setuptools_scm if available
try:
    from ._version import version as __version__
except ImportError:
    # Fallback version if setuptools_scm is not available
    pass

# Package metadata
__all__ = [
    "__version__",
    "__author__",
    "__email__",
    "__license__",
]


# Lazy imports for better startup performance
def _get_backend():
    """Lazy import of backend services."""
    from backend.data_service import DataService
    from backend.shared_memory_manager import SharedMemoryManager
    from backend.observability import CorrelationManager, MetricsCollector

    return {
        "DataService": DataService,
        "SharedMemoryManager": SharedMemoryManager,
        "CorrelationManager": CorrelationManager,
        "MetricsCollector": MetricsCollector,
    }


def _get_frontend():
    """Lazy import of frontend components."""
    from frontend.main_window import MainWindow
    from frontend.market_odds import MarketOddsTab

    return {
        "MainWindow": MainWindow,
        "MarketOddsTab": MarketOddsTab,
    }


def _get_compute():
    """Lazy import of compute kernels."""
    try:
        import datadriven.indicators as indicators

        return {"indicators": indicators}
    except ImportError:
        # C++ extensions not available
        return {}


# Expose main components through lazy loading
class _LazyModule:
    """Lazy module loader for better import performance."""

    def __init__(self):
        self._backend = None
        self._frontend = None
        self._compute = None

    @property
    def backend(self):
        if self._backend is None:
            self._backend = _get_backend()
        return self._backend

    @property
    def frontend(self):
        if self._frontend is None:
            self._frontend = _get_frontend()
        return self._frontend

    @property
    def compute(self):
        if self._compute is None:
            self._compute = _get_compute()
        return self._compute


# Create lazy module instance
_lazy = _LazyModule()


# Expose through module attributes
def __getattr__(name):
    """Dynamic attribute access for lazy loading."""
    if name == "backend":
        return _lazy.backend
    elif name == "frontend":
        return _lazy.frontend
    elif name == "compute":
        return _lazy.compute
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")


# Package information
def get_version():
    """Get package version."""
    return __version__


def get_build_info():
    """Get build information."""
    info = {
        "version": __version__,
        "python_version": None,
        "build_type": None,
        "simd_enabled": False,
        "qt_version": None,
        "compute_available": False,
    }

    try:
        import sys

        info["python_version"] = (
            f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        )
    except:
        pass

    try:
        from PyQt6.QtCore import QT_VERSION_STR

        info["qt_version"] = QT_VERSION_STR
    except ImportError:
        pass

    try:
        import datadriven.indicators

        info["compute_available"] = True
        # Try to get build info from C++ module
        if hasattr(datadriven.indicators, "get_build_info"):
            cpp_info = datadriven.indicators.get_build_info()
            info.update(cpp_info)
    except ImportError:
        pass

    return info


def check_dependencies():
    """Check if all dependencies are available."""
    missing = []
    optional_missing = []

    # Required dependencies
    required = [
        ("PyQt6", "PyQt6"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("aiohttp", "aiohttp"),
    ]

    # Optional dependencies
    optional = [
        ("yfinance", "yfinance"),
        ("uvloop", "uvloop"),
        ("prometheus_client", "prometheus_client"),
    ]

    for name, module in required:
        try:
            __import__(module)
        except ImportError:
            missing.append(name)

    for name, module in optional:
        try:
            __import__(module)
        except ImportError:
            optional_missing.append(name)

    return {
        "missing_required": missing,
        "missing_optional": optional_missing,
        "all_available": len(missing) == 0,
    }


# CLI entry point
def main():
    """Main entry point for the application."""
    import sys
    import os

    # Add current directory to path for development
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)

    # Import and run main application
    try:
        from main import TradingApplication

        app = TradingApplication()
        return app.run()
    except ImportError as e:
        print(f"Failed to import main application: {e}")
        print("Make sure you're running from the correct directory.")
        return 1
    except Exception as e:
        print(f"Application error: {e}")
        return 1


if __name__ == "__main__":
    import sys

    sys.exit(main())

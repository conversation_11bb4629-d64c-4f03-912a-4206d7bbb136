#!/usr/bin/env python3
"""
Development Environment Verification Script

Comprehensive verification of development environment setup for DataDriven Trading Platform.
Checks all dependencies, build tools, and configurations to ensure everything is working correctly.
"""

import sys
import os
import subprocess
import importlib
import platform
from pathlib import Path
from typing import List, Tuple, Dict, Optional
import json


# ANSI color codes for terminal output
class Colors:
    GREEN = "\033[92m"
    RED = "\033[91m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    BOLD = "\033[1m"
    END = "\033[0m"


def print_header(text: str):
    """Print a formatted header."""
    print(f"\n{Colors.BOLD}{Colors.BLUE}{'=' * 60}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{text.center(60)}{Colors.END}")
    print(f"{Colors.BOLD}{Colors.BLUE}{'=' * 60}{Colors.END}")


def print_success(text: str):
    """Print success message."""
    print(f"{Colors.GREEN}✅ {text}{Colors.END}")


def print_error(text: str):
    """Print error message."""
    print(f"{Colors.RED}❌ {text}{Colors.END}")


def print_warning(text: str):
    """Print warning message."""
    print(f"{Colors.YELLOW}⚠️  {text}{Colors.END}")


def print_info(text: str):
    """Print info message."""
    print(f"{Colors.BLUE}ℹ️  {text}{Colors.END}")


def run_command(cmd: str, capture_output: bool = True) -> Tuple[bool, str]:
    """Run a command and return success status and output."""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=capture_output, text=True, timeout=30
        )
        return result.returncode == 0, result.stdout.strip()
    except subprocess.TimeoutExpired:
        return False, "Command timed out"
    except Exception as e:
        return False, str(e)


def check_python_version() -> bool:
    """Check Python version compatibility."""
    print_header("Python Environment")

    version = sys.version_info
    version_str = f"{version.major}.{version.minor}.{version.micro}"

    if version >= (3, 9):
        print_success(f"Python {version_str} (compatible)")
        return True
    else:
        print_error(f"Python {version_str} (requires 3.9+)")
        return False


def check_virtual_environment() -> bool:
    """Check if running in virtual environment."""
    in_venv = hasattr(sys, "real_prefix") or (
        hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
    )

    if in_venv:
        print_success("Running in virtual environment")
        return True
    else:
        print_warning("Not running in virtual environment (recommended)")
        return False


def check_required_packages() -> bool:
    """Check if required Python packages are installed."""
    print_header("Python Dependencies")

    required_packages = [
        ("PyQt6", "PyQt6"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("aiohttp", "aiohttp"),
        ("structlog", "structlog"),
        ("pytest", "pytest"),
        ("black", "black"),
        ("isort", "isort"),
        ("flake8", "flake8"),
        ("mypy", "mypy"),
    ]

    all_good = True
    for display_name, import_name in required_packages:
        try:
            module = importlib.import_module(import_name)
            version = getattr(module, "__version__", "unknown")
            print_success(f"{display_name} {version}")
        except ImportError:
            print_error(f"{display_name} not installed")
            all_good = False

    return all_good


def check_build_tools() -> bool:
    """Check if build tools are available."""
    print_header("Build Tools")

    tools = [
        ("cmake", "cmake --version"),
        ("git", "git --version"),
    ]

    # Platform-specific compiler checks
    if platform.system() == "Windows":
        tools.append(("MSVC", "cl"))
    else:
        tools.extend(
            [
                ("gcc", "gcc --version"),
                ("g++", "g++ --version"),
            ]
        )

    all_good = True
    for tool_name, command in tools:
        success, output = run_command(command)
        if success:
            version = output.split("\n")[0] if output else "available"
            print_success(f"{tool_name}: {version}")
        else:
            print_error(f"{tool_name} not found")
            all_good = False

    return all_good


def check_project_structure() -> bool:
    """Check if project structure is correct."""
    print_header("Project Structure")

    required_files = [
        "main.py",
        "pyproject.toml",
        "requirements.txt",
        "src/backend/__init__.py",
        "src/frontend/__init__.py",
        "src/compute/indicators.cpp",
        "tools/tests/conftest.py",
        ".github/workflows/ci.yml",
        "tools/compile/compile_schemas.py",
        "tools/compile/compile_resources_python.py",
    ]

    required_dirs = [
        "src/backend",
        "src/frontend",
        "src/compute",
        "tools/tests",
        "tools/scripts",
        "tools/compile",
        "assets/config",
        ".github/workflows",
    ]

    all_good = True

    # Check files
    for file_path in required_files:
        if Path(file_path).exists():
            print_success(f"File: {file_path}")
        else:
            print_error(f"Missing file: {file_path}")
            all_good = False

    # Check directories
    for dir_path in required_dirs:
        if Path(dir_path).is_dir():
            print_success(f"Directory: {dir_path}")
        else:
            print_error(f"Missing directory: {dir_path}")
            all_good = False

    return all_good


def check_cpp_extensions() -> bool:
    """Check if C++ extensions can be imported."""
    print_header("C++ Extensions")

    try:
        import datadriven.indicators

        print_success("C++ indicators module imported successfully")

        # Test a simple function call
        try:
            test_data = [1.0, 2.0, 3.0, 4.0, 5.0]
            result = datadriven.indicators.calculate_donchian_midpoint(test_data, 3)
            print_success("C++ function call successful")
            return True
        except Exception as e:
            print_error(f"C++ function call failed: {e}")
            return False

    except ImportError as e:
        print_warning(f"C++ extensions not available: {e}")
        print_info("Run 'pip install -e .' to build C++ extensions")
        return False


def check_application_startup() -> bool:
    """Check if the application can start."""
    print_header("Application Startup Test")

    # Add current directory to Python path for imports
    import sys
    from pathlib import Path

    project_root = Path(__file__).parent.parent.parent  # Go up from tools/scripts to project root
    src_root = project_root / "src"
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
    if str(src_root) not in sys.path:
        sys.path.insert(0, str(src_root))

    # Test import of main modules
    try:
        from backend.data_service import DataService

        print_success("Backend modules import successfully")
    except ImportError as e:
        print_error(f"Backend import failed: {e}")
        return False

    try:
        from frontend.main_window import MainWindow

        print_success("Frontend modules import successfully")
    except ImportError as e:
        print_error(f"Frontend import failed: {e}")
        return False

    # Test configuration loading
    try:
        import yaml

        config_path = Path("assets/config/app.yaml")
        if config_path.exists():
            with open(config_path) as f:
                config = yaml.safe_load(f)
            print_success("Configuration file loaded successfully")
        else:
            print_warning("Configuration file not found (using defaults)")
    except Exception as e:
        print_warning(f"Configuration loading failed: {e}")

    return True


def check_development_tools() -> bool:
    """Check development tools configuration."""
    print_header("Development Tools")

    # Check pre-commit
    success, _ = run_command("pre-commit --version")
    if success:
        print_success("pre-commit available")

        # Check if hooks are installed
        if Path(".git/hooks/pre-commit").exists():
            print_success("pre-commit hooks installed")
        else:
            print_warning("pre-commit hooks not installed (run 'pre-commit install')")
    else:
        print_warning("pre-commit not available")

    # Check code formatting tools
    tools = ["black", "isort", "flake8", "mypy"]
    for tool in tools:
        success, output = run_command(f"{tool} --version")
        if success:
            version = output.split()[0] if output else "available"
            print_success(f"{tool} {version}")
        else:
            print_error(f"{tool} not available")

    return True


def run_quick_tests() -> bool:
    """Run a quick test suite to verify functionality."""
    print_header("Quick Functionality Tests")

    # Test pytest
    success, output = run_command(
        "pytest tests/test_backend.py::TestDataService::test_data_service_initialization -v"
    )
    if success:
        print_success("Basic backend test passed")
    else:
        print_error("Basic backend test failed")
        print_info(f"Output: {output}")
        return False

    # Test code quality
    success, _ = run_command("black --check backend/ frontend/ --quiet")
    if success:
        print_success("Code formatting check passed")
    else:
        print_warning("Code formatting issues found (run 'black .')")

    success, _ = run_command("flake8 backend/ frontend/ --count --select=E9,F63,F7,F82")
    if success:
        print_success("Critical linting check passed")
    else:
        print_error("Critical linting issues found")
        return False

    return True


def generate_report() -> Dict:
    """Generate a comprehensive setup report."""
    import datetime

    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "platform": platform.platform(),
        "python_version": sys.version,
        "checks": {},
    }

    checks = [
        ("python_version", check_python_version),
        ("virtual_environment", check_virtual_environment),
        ("required_packages", check_required_packages),
        ("build_tools", check_build_tools),
        ("project_structure", check_project_structure),
        ("cpp_extensions", check_cpp_extensions),
        ("application_startup", check_application_startup),
        ("development_tools", check_development_tools),
        ("quick_tests", run_quick_tests),
    ]

    all_passed = True
    for check_name, check_func in checks:
        try:
            result = check_func()
            report["checks"][check_name] = result
            if not result:
                all_passed = False
        except Exception as e:
            print_error(f"Check {check_name} failed with exception: {e}")
            report["checks"][check_name] = False
            all_passed = False

    report["overall_status"] = all_passed
    return report


def main():
    """Main verification function."""
    print_header("DataDriven Trading Platform - Setup Verification")
    print_info(f"Platform: {platform.platform()}")
    print_info(f"Python: {sys.version}")

    # Generate comprehensive report
    report = generate_report()

    # Print summary
    print_header("Verification Summary")

    passed = sum(1 for result in report["checks"].values() if result)
    total = len(report["checks"])

    if report["overall_status"]:
        print_success(f"All checks passed! ({passed}/{total})")
        print_info("Your development environment is ready! 🚀")
        print_info("Next steps:")
        print_info("  1. Run 'python main.py' to start the application")
        print_info("  2. Run 'pytest tests/ -v' to run the full test suite")
        print_info("  3. Check out CONTRIBUTING.md for development guidelines")
    else:
        print_error(f"Some checks failed ({passed}/{total})")
        print_info("Please address the issues above before proceeding")
        print_info("See README.md for detailed setup instructions")

    # Save report
    report_path = Path("setup_verification_report.json")
    with open(report_path, "w") as f:
        json.dump(report, f, indent=2)
    print_info(f"Detailed report saved to: {report_path}")

    return 0 if report["overall_status"] else 1


if __name__ == "__main__":
    sys.exit(main())

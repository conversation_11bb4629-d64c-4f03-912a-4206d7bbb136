"""
Visualization Worker for ProcessPoolExecutor
Standalone functions that can be properly pickled and executed in separate processes.
"""

import sys
from pathlib import Path
from typing import Dict, Any, List
import numpy as np

# Add compute directory to path for C++ kernels
compute_dir = Path(__file__).parent.parent / "compute"
if compute_dir.exists() and str(compute_dir) not in sys.path:
    sys.path.insert(0, str(compute_dir))

try:
    import indicators

    CPP_KERNELS_AVAILABLE = True
except ImportError:
    CPP_KERNELS_AVAILABLE = False


def prepare_visualization_data_worker(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Worker function for preparing visualization data in ProcessPoolExecutor.
    This function must be picklable and cannot contain any locks or complex objects.
    """
    if not CPP_KERNELS_AVAILABLE:
        return {}

    try:
        # Extract serializable data from market_data
        timestamps = market_data.get("timestamp", [])
        opens = market_data.get("open", [])
        highs = market_data.get("high", [])
        lows = market_data.get("low", [])
        closes = market_data.get("close", [])
        rebased_data = market_data.get("rebased_data", [])
        rebased_vector = market_data.get("rebased_vector", [])
        peaks = market_data.get("peaks", [])
        troughs = market_data.get("troughs", [])
        donchian_midpoint = market_data.get("donchian_midpoint", [])
        actual_dates = market_data.get("actual_dates", [])

        # Convert numpy arrays to lists for serialization
        if hasattr(opens, "tolist"):
            opens = opens.tolist()
        if hasattr(highs, "tolist"):
            highs = highs.tolist()
        if hasattr(lows, "tolist"):
            lows = lows.tolist()
        if hasattr(closes, "tolist"):
            closes = closes.tolist()
        if hasattr(donchian_midpoint, "tolist"):
            donchian_midpoint = donchian_midpoint.tolist()

        # Prepare visualization data using C++ functions
        visualization_data = {}

        # Prepare candlestick data
        try:
            result = indicators.prepare_candlestick_render_data(
                timestamps, opens, highs, lows, closes
            )
            if result.success:
                visualization_data["candlestick_data"] = [
                    {
                        "x": candle.x,
                        "open": candle.open,
                        "high": candle.high,
                        "low": candle.low,
                        "close": candle.close,
                        "body_color": candle.body_color,
                        "wick_color": candle.wick_color,
                        "is_valid": candle.is_valid,
                    }
                    for candle in result.data
                ]
        except Exception:
            visualization_data["candlestick_data"] = []

        # Prepare rebased candlestick data
        try:
            if rebased_data:
                # Convert rebased data to simple format for C++
                rebased_simple = []
                for item in rebased_data:
                    if len(item) >= 5:
                        rebased_simple.append(
                            {
                                "index": item[0],
                                "open_pct": item[1],
                                "high_pct": item[2],
                                "low_pct": item[3],
                                "close_pct": item[4],
                            }
                        )

                visualization_data["rebased_candlestick_data"] = rebased_simple
        except Exception:
            visualization_data["rebased_candlestick_data"] = []

        # Calculate standard deviations based on peaks and troughs
        try:
            if peaks and troughs:
                result = indicators.calculate_separated_standard_deviations_from_peaks_troughs(
                    peaks, troughs
                )
                if result.success:
                    visualization_data["standard_deviations"] = {
                        "positive_std": result.data.positive_std,
                        "negative_std": result.data.negative_std,
                        "positive_count": result.data.positive_count,
                        "negative_count": result.data.negative_count,
                    }
        except Exception:
            visualization_data["standard_deviations"] = {
                "positive_std": 0.0,
                "negative_std": 0.0,
                "positive_count": 0,
                "negative_count": 0,
            }

        # Prepare Donchian stairstep data
        try:
            if timestamps and donchian_midpoint:
                result = indicators.prepare_donchian_stairstep_data(
                    timestamps, donchian_midpoint
                )
                if result.success:
                    visualization_data["donchian_stairstep_data"] = {
                        "x_values": result.data.x_values,
                        "y_values": result.data.y_values,
                        "is_valid": result.data.is_valid,
                    }
        except Exception:
            visualization_data["donchian_stairstep_data"] = {
                "x_values": [],
                "y_values": [],
                "is_valid": False,
            }

        # Prepare vector plot data
        try:
            if rebased_vector:
                indices = list(range(len(rebased_vector)))
                visualization_data["vector_plot_data"] = [
                    {"x": i, "y": val} for i, val in enumerate(rebased_vector)
                ]
        except Exception:
            visualization_data["vector_plot_data"] = []

        # Determine Donchian color
        try:
            if donchian_midpoint and closes:
                result = indicators.determine_donchian_color(donchian_midpoint, closes)
                if result.success:
                    visualization_data["donchian_color"] = result.data
                else:
                    visualization_data["donchian_color"] = "#CCCCCC"
        except Exception:
            visualization_data["donchian_color"] = "#CCCCCC"

        # Calculate mean levels
        try:
            if peaks and troughs:
                result = indicators.calculate_mean_peak_trough_levels(peaks, troughs)
                if result.success:
                    visualization_data["mean_levels"] = {
                        "mean_peak_level": result.data.mean_peak_level,
                        "mean_trough_level": result.data.mean_trough_level,
                        "peak_count": result.data.peak_count,
                        "trough_count": result.data.trough_count,
                    }
        except Exception:
            visualization_data["mean_levels"] = {
                "mean_peak_level": 0.0,
                "mean_trough_level": 0.0,
                "peak_count": 0,
                "trough_count": 0,
            }

        # Prepare peak/trough display data
        try:
            peak_display_data = []
            for peak in peaks:
                peak_display_data.append(
                    {
                        "index": peak.index,
                        "level": peak.level,
                        "ray_end": peak.ray_end,
                        "label": peak.label,
                        "position": peak.position,
                        "is_closed": peak.is_closed,
                        "cycle_type": peak.cycle_type,
                        "color": "g",
                    }
                )

            trough_display_data = []
            for trough in troughs:
                trough_display_data.append(
                    {
                        "index": trough.index,
                        "level": trough.level,
                        "ray_end": trough.ray_end,
                        "label": trough.label,
                        "position": trough.position,
                        "is_closed": trough.is_closed,
                        "cycle_type": trough.cycle_type,
                        "color": "r",
                    }
                )

            visualization_data["peak_trough_display_data"] = {
                "peaks": peak_display_data,
                "troughs": trough_display_data,
            }
        except Exception:
            visualization_data["peak_trough_display_data"] = {
                "peaks": [],
                "troughs": [],
            }

        # Prepare crosshair lookup data
        visualization_data["crosshair_lookup_data"] = {
            "rebased_data": rebased_data,
            "actual_dates": actual_dates,
            "closes": closes,
        }

        # Calculate chart bounds to eliminate UI computation
        try:
            if timestamps and rebased_data:
                # Pre-compute bounds for main chart
                valid_rebased = [item for item in rebased_data if len(item) >= 5]
                if valid_rebased:
                    highs = [item[2] for item in valid_rebased]  # high_pct
                    lows = [item[3] for item in valid_rebased]  # low_pct

                    visualization_data["chart_bounds"] = {
                        "x_min": float(timestamps[0]) - 1 if timestamps else 0,
                        "x_max": float(timestamps[-1]) + 1 if timestamps else 1,
                        "y_min": min(lows) if lows else 0,
                        "y_max": max(highs) if highs else 1,
                    }
        except Exception:
            visualization_data["chart_bounds"] = {
                "x_min": 0,
                "x_max": 1,
                "y_min": 0,
                "y_max": 1,
            }

        # Pre-compute bandwidth chart data
        try:
            if "bollinger_bandwidth" in market_data:
                bandwidth_values = market_data["bollinger_bandwidth"]
                if bandwidth_values and timestamps:
                    # Filter valid bandwidth data
                    valid_bandwidth_data = []
                    valid_timestamps_bw = []

                    for i, bw_val in enumerate(bandwidth_values):
                        if i < len(timestamps) and bw_val == bw_val:  # Not NaN
                            valid_bandwidth_data.append(float(bw_val))
                            valid_timestamps_bw.append(float(timestamps[i]))

                    # Determine if there's significant movement
                    has_movement = True
                    strip_position = 0.0

                    if len(valid_bandwidth_data) > 1:
                        bw_min = min(valid_bandwidth_data)
                        bw_max = max(valid_bandwidth_data)
                        bw_range = bw_max - bw_min

                        if bw_range < 0.001:  # Threshold for "no movement"
                            has_movement = False
                            strip_position = 0.0
                        else:
                            strip_position = bw_max * 1.1  # 10% above highest point

                    visualization_data["bandwidth_chart_data"] = {
                        "timestamps": valid_timestamps_bw,
                        "bandwidth_values": valid_bandwidth_data,
                        "has_movement": has_movement,
                        "strip_position": strip_position,
                    }
        except Exception:
            visualization_data["bandwidth_chart_data"] = {
                "timestamps": [],
                "bandwidth_values": [],
                "has_movement": False,
                "strip_position": 0.0,
            }

        return visualization_data

    except Exception as e:
        # Return empty data on any error to avoid breaking the application
        return {
            "candlestick_data": [],
            "rebased_candlestick_data": [],
            "standard_deviations": {
                "positive_std": 0.0,
                "negative_std": 0.0,
                "positive_count": 0,
                "negative_count": 0,
            },
            "donchian_stairstep_data": {
                "x_values": [],
                "y_values": [],
                "is_valid": False,
            },
            "vector_plot_data": [],
            "donchian_color": "#CCCCCC",
            "mean_levels": {
                "mean_peak_level": 0.0,
                "mean_trough_level": 0.0,
                "peak_count": 0,
                "trough_count": 0,
            },
            "peak_trough_display_data": {"peaks": [], "troughs": []},
            "crosshair_lookup_data": {
                "rebased_data": [],
                "actual_dates": [],
                "closes": [],
            },
        }

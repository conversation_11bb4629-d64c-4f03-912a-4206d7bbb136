import sys
import os

# Add the Debug build directory to Python path
debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
sys.path.insert(0, debug_path)

import indicators

print("🎉 DataDriven Trading Platform - Python Module Test")
print("=" * 60)

# Get build information
print("🔧 Build Information:")
build_info = indicators.get_build_info()
for key, value in build_info.items():
    print(f"  {key}: {value}")

print(f"\n📋 Available Functions ({len([x for x in dir(indicators) if not x.startswith('_')])} total):")

# List all available functions
functions = [x for x in dir(indicators) if not x.startswith('_')]
for i, func in enumerate(functions, 1):
    print(f"  {i:2d}. {func}")

print("\n🧪 Testing Core Functions:")

# Test simple moving average
print("\n1. Testing simple_moving_average:")
test_data = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0]
sma_result = indicators.simple_moving_average(test_data, 3)
print(f"   Input: {test_data}")
print(f"   SMA(3): {sma_result}")

# Test rolling max
print("\n2. Testing rolling_max:")
max_result = indicators.rolling_max(test_data, 3)
print(f"   Input: {test_data}")
print(f"   Rolling Max(3): {max_result}")

# Test rolling min
print("\n3. Testing rolling_min:")
min_result = indicators.rolling_min(test_data, 3)
print(f"   Input: {test_data}")
print(f"   Rolling Min(3): {min_result}")

# Test Donchian midpoint
print("\n4. Testing calculate_donchian_midpoint:")
donchian_result = indicators.calculate_donchian_midpoint(test_data, 3)
print(f"   Input: {test_data}")
print(f"   Donchian Midpoint(3): {donchian_result}")

print("\n✅ All tests completed successfully!")
print("🚀 The indicators module is fully functional and ready for use!")

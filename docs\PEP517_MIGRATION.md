# PEP 517 Migration Guide

This document explains the migration from legacy `setup.py` to modern PEP 517 packaging with `pyproject.toml` and `scikit-build-core`.

## 🎯 Overview

DataDriven Trading Platform now uses **PEP 517/518 compliant packaging** with:
- **`pyproject.toml`**: Single configuration file for all build settings
- **`scikit-build-core`**: Modern CMake-based build backend
- **Build isolation**: Automatic dependency management
- **Cross-platform wheels**: Automated with `cibuildwheel`

## 📋 Migration Summary

### Before (Legacy)
```bash
# Old way - manual setup.py
cd compute
python setup.py build_ext --inplace
cd ..
pip install -e .
```

### After (Modern PEP 517)
```bash
# New way - automatic PEP 517
pip install -e .
# or
python -m build
```

## 🔧 Key Changes

### 1. Build System Declaration

**Old (`setup.py`):**
```python
from setuptools import setup
from pybind11.setup_helpers import Pybind11Extension

setup(
    ext_modules=[
        Pybind11Extension("indicators", ["indicators.cpp"]),
    ],
    # ... many configuration options
)
```

**New (`pyproject.toml`):**
```toml
[build-system]
requires = [
    "scikit-build-core>=0.7.0",
    "pybind11>=2.11.0", 
    "cmake>=3.18",
]
build-backend = "scikit_build_core.build"
```

### 2. Project Metadata

**Old (scattered across `setup.py`, `setup.cfg`, `MANIFEST.in`):**
```python
setup(
    name="datadriven-trading",
    version="1.0.0",
    description="Trading platform",
    # ... scattered configuration
)
```

**New (centralized in `pyproject.toml`):**
```toml
[project]
name = "datadriven-trading"
version = "1.0.0"
description = "Enterprise-grade trading analysis platform"
dependencies = ["PyQt6>=6.5.0", "numpy>=1.21.0"]
```

### 3. Build Configuration

**Old (hardcoded in `setup.py`):**
```python
if platform.system() == "Windows":
    extra_compile_args = ['/O2', '/arch:AVX2']
else:
    extra_compile_args = ['-O3', '-march=native']
```

**New (declarative in `pyproject.toml`):**
```toml
[tool.scikit-build.cmake.define]
CMAKE_BUILD_TYPE = {env="CMAKE_BUILD_TYPE", default="Release"}
ENABLE_SIMD = {env="SKBUILD_ENABLE_SIMD", default="ON"}
```

## 🚀 Usage Examples

### Development Installation

```bash
# Install in editable mode with all dependencies
pip install -e ".[dev]"

# With specific build options
CMAKE_BUILD_TYPE=Debug pip install -e .
SKBUILD_ENABLE_SIMD=OFF pip install -e .
```

### Building Distributions

```bash
# Build both source and wheel distributions
python -m build

# Build only wheel
python -m build --wheel

# Build only source distribution
python -m build --sdist
```

### Using the Modern Build Script

```bash
# Clean build artifacts
python scripts/build.py clean

# Build wheel with debug symbols
python scripts/build.py wheel --build-type Debug

# Install in editable mode with tests
python scripts/build.py install --enable-tests

# Get build environment info
python scripts/build.py info
```

### Environment Variables

Control build behavior with environment variables:

```bash
# CMake build type
export CMAKE_BUILD_TYPE=Release          # Release, Debug, RelWithDebInfo

# Feature toggles
export SKBUILD_ENABLE_SIMD=ON           # Enable SIMD optimizations
export SKBUILD_ENABLE_OPENMP=OFF        # Enable OpenMP parallelization
export SKBUILD_BUILD_TESTS=ON           # Build C++ tests
export SKBUILD_BUILD_BENCHMARKS=ON      # Build benchmarks

# Optimization settings
export SKBUILD_LTO=ON                   # Link-time optimization
export SKBUILD_CXX_FLAGS="-O3 -march=native"  # Custom compiler flags
```

## 🔄 Backward Compatibility

### Legacy Support

The legacy `compute/setup.py` is still available for compatibility:

```bash
# Still works (legacy mode)
cd compute
python setup.py build_ext --inplace
cd ..
```

However, **we recommend migrating to the modern approach** for:
- Better dependency management
- Automatic build isolation
- Cross-platform compatibility
- CI/CD integration

### Migration Path

1. **Phase 1**: Use both systems in parallel
   ```bash
   # Try modern first
   pip install -e . || {
       # Fallback to legacy
       cd compute && python setup.py build_ext --inplace && cd ..
   }
   ```

2. **Phase 2**: Migrate CI/CD to modern system
   ```yaml
   # GitHub Actions
   - name: Build package
     run: python -m build
   ```

3. **Phase 3**: Remove legacy `setup.py` (future release)

## 🛠️ Troubleshooting

### Common Issues

**Issue**: `ModuleNotFoundError: No module named 'scikit_build_core'`
```bash
# Solution: Install build dependencies
pip install build
```

**Issue**: `CMake not found`
```bash
# Solution: Install CMake
pip install cmake
# or system package manager
```

**Issue**: `Build fails with SIMD errors`
```bash
# Solution: Disable SIMD for compatibility
SKBUILD_ENABLE_SIMD=OFF pip install -e .
```

**Issue**: `Permission denied on Windows`
```bash
# Solution: Run as administrator or use virtual environment
python -m venv venv
venv\Scripts\activate
pip install -e .
```

### Debug Build Issues

Enable verbose output for debugging:

```bash
# Verbose CMake output
CMAKE_VERBOSE_MAKEFILE=ON pip install -e . -v

# Verbose scikit-build-core output
pip install -e . -v --config-setting=cmake.verbose=true

# Debug build type
CMAKE_BUILD_TYPE=Debug pip install -e . -v
```

### Verify Installation

```bash
# Check if package is installed correctly
python -c "import datadriven; print(datadriven.get_version())"

# Check C++ extensions
python -c "import datadriven.indicators; print('C++ extensions working!')"

# Run verification script
python scripts/verify_setup.py
```

## 📊 Benefits of PEP 517

### For Developers

- **Simplified workflow**: Single `pip install -e .` command
- **Better dependency management**: Automatic resolution
- **Consistent builds**: Build isolation prevents conflicts
- **IDE integration**: Better support in modern IDEs

### For CI/CD

- **Faster builds**: Cached build dependencies
- **Cross-platform**: Unified build process
- **Reproducible**: Locked dependency versions
- **Standard compliance**: Works with all PEP 517 tools

### For Users

- **Easy installation**: `pip install datadriven-trading`
- **Reliable wheels**: Pre-built for all platforms
- **Automatic dependencies**: No manual setup required
- **Version management**: Proper semantic versioning

## 🔮 Future Roadmap

### Planned Improvements

1. **Enhanced CMake integration**: Better feature detection
2. **Conda packaging**: Native conda-forge support
3. **WebAssembly builds**: Browser-based indicators
4. **GPU acceleration**: CUDA/OpenCL support

### Deprecation Timeline

- **v1.0**: Both legacy and modern systems supported
- **v1.1**: Modern system recommended, legacy deprecated
- **v2.0**: Legacy system removed, modern only

## 📚 References

- [PEP 517 - A build-system independent format for source trees](https://peps.python.org/pep-0517/)
- [PEP 518 - Specifying Minimum Build System Requirements](https://peps.python.org/pep-0518/)
- [scikit-build-core documentation](https://scikit-build-core.readthedocs.io/)
- [Python Packaging User Guide](https://packaging.python.org/)

## 💬 Support

If you encounter issues with the migration:

1. Check this guide and troubleshooting section
2. Run `python scripts/verify_setup.py` for diagnostics
3. Open an issue on GitHub with build logs
4. Join our discussions for community support

The modern PEP 517 system provides a much better developer experience while maintaining full compatibility with the existing codebase. We encourage all contributors to adopt the new workflow!

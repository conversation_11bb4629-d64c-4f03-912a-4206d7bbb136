"""
Optimized chart widget using QGraphicsView for efficient painting.
Implements dirty region tracking and minimizes full-viewport repaints.
"""

import pyqtgraph as pg
from PyQt6.QtCore import QRectF, QTimer, pyqtSignal, QObject
from PyQt6.QtGui import QPainter, QPixmap, QColor
from PyQt6.QtWidgets import QGraphicsView, QGraphicsScene, QWidget
from typing import Dict, Any, List, Set, Optional
import logging


class DirtyRegionManager:
    """
    Manages dirty regions for efficient repainting.
    Tracks only changed areas to minimize full-viewport repaints.
    """

    def __init__(self):
        self.logger = self._setup_logging()
        self._dirty_regions: Set[QRectF] = set()
        self._viewport_rect = QRectF()
        self._merge_threshold = 10  # Merge regions if they're close

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging."""
        logger = logging.getLogger("DirtyRegionManager")
        logger.setLevel(logging.DEBUG)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def add_dirty_region(self, rect: QRectF):
        """Add a dirty region, merging with nearby regions if beneficial."""
        if not rect.isValid() or rect.isEmpty():
            return

        # Only track regions within viewport
        if not self._viewport_rect.intersects(rect):
            return

        # Check if we should merge with existing regions
        merged = False
        regions_to_remove = []

        for existing_rect in self._dirty_regions:
            # Calculate distance between regions
            distance = self._calculate_region_distance(rect, existing_rect)

            if distance < self._merge_threshold:
                # Merge regions
                merged_rect = rect.united(existing_rect)
                regions_to_remove.append(existing_rect)
                rect = merged_rect
                merged = True

        # Remove merged regions
        for region in regions_to_remove:
            self._dirty_regions.discard(region)

        # Add the (possibly merged) region
        self._dirty_regions.add(rect)

        self.logger.debug(
            f"Added dirty region: {rect}, total regions: {len(self._dirty_regions)}"
        )

    def _calculate_region_distance(self, rect1: QRectF, rect2: QRectF) -> float:
        """Calculate distance between two rectangles."""
        if rect1.intersects(rect2):
            return 0.0

        # Calculate minimum distance between rectangles
        x_distance = max(
            0, max(rect1.left() - rect2.right(), rect2.left() - rect1.right())
        )
        y_distance = max(
            0, max(rect1.top() - rect2.bottom(), rect2.top() - rect1.bottom())
        )

        return (x_distance**2 + y_distance**2) ** 0.5

    def get_dirty_regions(self) -> List[QRectF]:
        """Get all dirty regions for repainting."""
        return list(self._dirty_regions)

    def clear_dirty_regions(self):
        """Clear all dirty regions after repainting."""
        self._dirty_regions.clear()
        self.logger.debug("Cleared all dirty regions")

    def set_viewport(self, rect: QRectF):
        """Set viewport rectangle for intersection testing."""
        self._viewport_rect = rect

    def needs_full_repaint(self) -> bool:
        """Check if full repaint is more efficient than partial updates."""
        if len(self._dirty_regions) > 5:  # Too many regions
            return True

        # Calculate total dirty area
        total_dirty_area = sum(
            rect.width() * rect.height() for rect in self._dirty_regions
        )
        viewport_area = self._viewport_rect.width() * self._viewport_rect.height()

        # If dirty area is > 50% of viewport, do full repaint
        return total_dirty_area > (viewport_area * 0.5)


class OptimizedChartWidget(pg.PlotWidget):
    """
    Optimized chart widget with efficient painting and dirty region tracking.
    Minimizes full-viewport repaints for better performance.
    """

    # Signals for performance monitoring
    repaint_started = pyqtSignal()
    repaint_completed = pyqtSignal(float, bool)  # time_ms, was_full_repaint

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()

        # Dirty region management
        self.dirty_manager = DirtyRegionManager()

        # Performance optimization
        self._last_viewport_rect = QRectF()
        self._repaint_timer = QTimer()
        self._repaint_timer.setSingleShot(True)
        self._repaint_timer.timeout.connect(self._perform_optimized_repaint)

        # Performance monitoring
        self._repaint_stats = {
            "total_repaints": 0,
            "full_repaints": 0,
            "partial_repaints": 0,
            "avg_repaint_time": 0.0,
        }

        # Setup optimizations
        self._setup_optimizations()

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging."""
        logger = logging.getLogger("OptimizedChartWidget")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _setup_optimizations(self):
        """Setup chart optimizations."""
        # Enable OpenGL if available for better performance
        try:
            self.setUseOpenGL(True)
            self.logger.info("OpenGL acceleration enabled")
        except Exception as e:
            self.logger.warning(f"OpenGL not available: {e}")

        # Optimize rendering
        plot_item = self.getPlotItem()
        plot_item.setClipToView(True)  # Only render visible items
        plot_item.setDownsampling(auto=True)  # Auto-downsample for performance

        # Setup viewport tracking
        plot_item.sigRangeChanged.connect(self._on_viewport_changed)

    def _on_viewport_changed(self):
        """Handle viewport changes."""
        view_box = self.getViewBox()
        if view_box:
            viewport_rect = view_box.viewRect()
            self.dirty_manager.set_viewport(viewport_rect)

            # If viewport changed significantly, mark for full repaint
            if not self._last_viewport_rect.isValid() or not self._viewport_similar(
                viewport_rect, self._last_viewport_rect
            ):
                self.dirty_manager.add_dirty_region(viewport_rect)
                self._last_viewport_rect = viewport_rect

    def _viewport_similar(
        self, rect1: QRectF, rect2: QRectF, threshold: float = 0.1
    ) -> bool:
        """Check if two viewport rectangles are similar enough to avoid full repaint."""
        if not rect1.isValid() or not rect2.isValid():
            return False

        # Check if size change is within threshold
        size_change = (
            abs(rect1.width() - rect2.width()) / rect1.width()
            + abs(rect1.height() - rect2.height()) / rect1.height()
        )

        return size_change < threshold

    def add_dirty_region(self, rect: QRectF):
        """Add a dirty region for efficient repainting."""
        self.dirty_manager.add_dirty_region(rect)
        self._schedule_repaint()

    def add_dirty_item(self, item):
        """Add a dirty item by calculating its bounding rectangle."""
        if hasattr(item, "boundingRect"):
            bounds = item.boundingRect()
            if bounds.isValid():
                self.add_dirty_region(bounds)

    def _schedule_repaint(self):
        """Schedule optimized repaint with debouncing."""
        # Debounce rapid updates
        self._repaint_timer.start(16)  # ~60 FPS max

    def _perform_optimized_repaint(self):
        """Perform optimized repaint using dirty regions."""
        try:
            import time

            start_time = time.perf_counter()

            self.repaint_started.emit()

            # Check if full repaint is more efficient
            needs_full = self.dirty_manager.needs_full_repaint()

            if needs_full:
                # Perform full repaint
                self.update()
                self._repaint_stats["full_repaints"] += 1
                self.logger.debug("Performed full repaint")
            else:
                # Perform partial repaint of dirty regions
                dirty_regions = self.dirty_manager.get_dirty_regions()
                for region in dirty_regions:
                    # Convert to widget coordinates and update
                    widget_rect = self._scene_to_widget_rect(region)
                    if widget_rect.isValid():
                        self.update(widget_rect.toRect())

                self._repaint_stats["partial_repaints"] += 1
                self.logger.debug(
                    f"Performed partial repaint of {len(dirty_regions)} regions"
                )

            # Clear dirty regions
            self.dirty_manager.clear_dirty_regions()

            # Update performance stats
            repaint_time = (time.perf_counter() - start_time) * 1000
            self._update_repaint_stats(repaint_time)

            self.repaint_completed.emit(repaint_time, needs_full)

        except Exception as e:
            self.logger.error(f"Error during optimized repaint: {e}")

    def _scene_to_widget_rect(self, scene_rect: QRectF) -> QRectF:
        """Convert scene rectangle to widget coordinates."""
        try:
            view_box = self.getViewBox()
            if view_box:
                # Transform scene coordinates to view coordinates
                top_left = view_box.mapToView(scene_rect.topLeft())
                bottom_right = view_box.mapToView(scene_rect.bottomRight())
                return QRectF(top_left, bottom_right)
        except Exception as e:
            self.logger.warning(f"Error converting scene to widget rect: {e}")

        return QRectF()

    def _update_repaint_stats(self, repaint_time: float):
        """Update repaint performance statistics."""
        self._repaint_stats["total_repaints"] += 1

        # Calculate rolling average
        alpha = 0.1  # Smoothing factor
        self._repaint_stats["avg_repaint_time"] = (
            alpha * repaint_time + (1 - alpha) * self._repaint_stats["avg_repaint_time"]
        )

    def get_repaint_stats(self) -> Dict[str, Any]:
        """Get repaint performance statistics."""
        total = self._repaint_stats["total_repaints"]
        if total > 0:
            full_percentage = (self._repaint_stats["full_repaints"] / total) * 100
            partial_percentage = (self._repaint_stats["partial_repaints"] / total) * 100
        else:
            full_percentage = partial_percentage = 0

        return {
            **self._repaint_stats,
            "full_repaint_percentage": full_percentage,
            "partial_repaint_percentage": partial_percentage,
        }

    def force_full_repaint(self):
        """Force a complete repaint of the entire widget."""
        view_box = self.getViewBox()
        if view_box:
            viewport_rect = view_box.viewRect()
            self.dirty_manager.add_dirty_region(viewport_rect)
        self._schedule_repaint()
        self.logger.info("Forced full repaint")

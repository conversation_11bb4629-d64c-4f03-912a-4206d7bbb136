#!/usr/bin/env python3
"""
Schema compilation script for Protocol Buffers.
Generates Python and C++ bindings from .proto files.
"""

import subprocess
import sys
from pathlib import Path
import shutil


def check_protoc_available():
    """Check if protoc compiler is available."""
    try:
        result = subprocess.run(["protoc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Found protoc: {result.stdout.strip()}")
            return True
        else:
            print("✗ protoc not working properly")
            return False
    except FileNotFoundError:
        print("✗ protoc not found in PATH")
        return False


def install_protobuf_python():
    """Install protobuf Python package if not available."""
    try:
        import google.protobuf

        print(f"✓ protobuf Python package available: {google.protobuf.__version__}")
        return True
    except ImportError:
        print("Installing protobuf Python package...")
        try:
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "protobuf"], check=True
            )
            print("✓ protobuf Python package installed")
            return True
        except subprocess.CalledProcessError:
            print("✗ Failed to install protobuf Python package")
            return False


def compile_proto_files():
    """Compile .proto files to Python and C++ bindings."""
    schemas_dir = Path("schemas")
    if not schemas_dir.exists():
        print("✗ schemas directory not found")
        return False

    # Create output directories
    python_out = Path("backend/generated")
    cpp_out = Path("compute/generated")

    python_out.mkdir(exist_ok=True)
    cpp_out.mkdir(exist_ok=True)

    # Create __init__.py for Python package
    (python_out / "__init__.py").touch()

    # Find all .proto files
    proto_files = list(schemas_dir.glob("*.proto"))
    if not proto_files:
        print("✗ No .proto files found in schemas directory")
        return False

    success = True

    for proto_file in proto_files:
        print(f"Compiling {proto_file.name}...")

        # Compile Python bindings
        try:
            result = subprocess.run(
                [
                    "protoc",
                    f"--python_out={python_out}",
                    f"--proto_path={schemas_dir}",
                    str(proto_file),
                ],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print(f"  ✓ Python bindings generated")
            else:
                print(f"  ✗ Python compilation failed: {result.stderr}")
                success = False
        except Exception as e:
            print(f"  ✗ Python compilation error: {e}")
            success = False

        # Compile C++ bindings
        try:
            result = subprocess.run(
                [
                    "protoc",
                    f"--cpp_out={cpp_out}",
                    f"--proto_path={schemas_dir}",
                    str(proto_file),
                ],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print(f"  ✓ C++ bindings generated")
            else:
                print(f"  ✗ C++ compilation failed: {result.stderr}")
                success = False
        except Exception as e:
            print(f"  ✗ C++ compilation error: {e}")
            success = False

    return success


def create_schema_version_file():
    """Create schema version file for runtime version checking."""
    version_content = '''"""
Schema version information for Protocol Buffer compatibility.
"""

SCHEMA_VERSION = {
    "major": 1,
    "minor": 0,
    "patch": 0
}

def get_schema_version():
    """Get current schema version."""
    return SCHEMA_VERSION

def is_compatible(other_version):
    """Check if other version is compatible with current schema."""
    # Major version must match for compatibility
    return other_version.get("major") == SCHEMA_VERSION["major"]
'''

    version_file = Path("backend/generated/schema_version.py")
    version_file.write_text(version_content)
    print("✓ Schema version file created")


def create_message_factory():
    """Create message factory for easy message creation."""
    factory_content = '''"""
Message factory for creating Protocol Buffer messages with proper versioning.
"""

from typing import Dict, Any, Optional
from .schema_version import get_schema_version
from . import market_data_pb2


class MessageFactory:
    """Factory for creating versioned Protocol Buffer messages."""
    
    def __init__(self):
        self.schema_version = get_schema_version()
    
    def create_schema_version(self):
        """Create schema version message."""
        version = market_data_pb2.SchemaVersion()
        version.major = self.schema_version["major"]
        version.minor = self.schema_version["minor"]
        version.patch = self.schema_version["patch"]
        return version
    
    def create_correlation_context(self, correlation_id: str, request_id: str, 
                                 operation: str, user_id: Optional[str] = None):
        """Create correlation context message."""
        context = market_data_pb2.CorrelationContext()
        context.correlation_id = correlation_id
        context.request_id = request_id
        context.operation = operation
        if user_id:
            context.user_id = user_id
        return context
    
    def create_market_data_request(self, ticker: str, timeframe: str, dtl: int, 
                                 length: int, context: market_data_pb2.CorrelationContext):
        """Create market data request message."""
        request = market_data_pb2.MarketDataRequest()
        request.schema_version.CopyFrom(self.create_schema_version())
        request.context.CopyFrom(context)
        request.ticker = ticker
        request.timeframe = timeframe
        request.dtl = dtl
        request.length = length
        return request
    
    def create_market_data_response(self, context: market_data_pb2.CorrelationContext,
                                  status: market_data_pb2.MarketDataResponse.Status = 
                                  market_data_pb2.MarketDataResponse.SUCCESS):
        """Create market data response message."""
        response = market_data_pb2.MarketDataResponse()
        response.schema_version.CopyFrom(self.create_schema_version())
        response.context.CopyFrom(context)
        response.status = status
        return response
    
    def create_error_message(self, context: market_data_pb2.CorrelationContext,
                           error_code: market_data_pb2.ErrorMessage.ErrorCode,
                           error_message: str):
        """Create error message."""
        error = market_data_pb2.ErrorMessage()
        error.schema_version.CopyFrom(self.create_schema_version())
        error.context.CopyFrom(context)
        error.error_code = error_code
        error.error_message = error_message
        return error


# Global message factory instance
message_factory = MessageFactory()
'''

    factory_file = Path("backend/generated/message_factory.py")
    factory_file.write_text(factory_content)
    print("✓ Message factory created")


def main():
    """Main compilation function."""
    print("Protocol Buffer Schema Compilation")
    print("=" * 40)

    # Check prerequisites
    if not check_protoc_available():
        print("\nInstallation instructions:")
        print("1. Install Protocol Buffers compiler:")
        print(
            "   - Windows: Download from https://github.com/protocolbuffers/protobuf/releases"
        )
        print("   - macOS: brew install protobuf")
        print("   - Ubuntu: sudo apt install protobuf-compiler")
        return False

    if not install_protobuf_python():
        return False

    # Compile schemas
    print("\nCompiling Protocol Buffer schemas...")
    if not compile_proto_files():
        print("\n✗ Schema compilation failed!")
        return False

    # Create additional files
    create_schema_version_file()
    create_message_factory()

    print("\n✓ Schema compilation completed successfully!")
    print("\nGenerated files:")
    print("  - backend/generated/market_data_pb2.py")
    print("  - backend/generated/schema_version.py")
    print("  - backend/generated/message_factory.py")
    print("  - compute/generated/market_data.pb.h")
    print("  - compute/generated/market_data.pb.cc")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

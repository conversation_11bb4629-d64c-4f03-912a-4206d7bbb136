/**
 * High-Performance Technical Indicators
 * C++ compute kernels for ultra-low-latency calculations
 */

#include <vector>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <limits>
#include <string>
#include <exception>
#include <type_traits>
#include <regex>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>

// Include enterprise exception handling
#include "exception_handling.h"

// SIMD intrinsics for vectorization
#ifdef _MSC_VER
#include <intrin.h>
#else
#include <x86intrin.h>
#endif

// Enable SIMD optimizations
#ifdef __AVX2__
#define SIMD_ENABLED 1
#include <immintrin.h>
#else
#define SIMD_ENABLED 0
#endif

namespace py = pybind11;

/**
 * Simple memory pool allocator for better performance
 */
class MemoryPool {
private:
    static constexpr size_t POOL_SIZE = 64 * 1024 * 1024; // 64MB pool (restored generous pool)
    static constexpr size_t MAX_HEAP_ALLOCATIONS = 100000; // Increased limit to handle large datasets safely
    static constexpr size_t ALIGNMENT = 32; // AVX2 alignment

    alignas(ALIGNMENT) char pool[POOL_SIZE];
    size_t offset = 0;
    size_t heap_allocation_count = 0;

public:
    void* allocate(size_t size) {
        // Align size to ALIGNMENT boundary
        size = (size + ALIGNMENT - 1) & ~(ALIGNMENT - 1);

        if (offset + size > POOL_SIZE) {
            // Pool exhausted, check heap allocation limit
            if (heap_allocation_count >= MAX_HEAP_ALLOCATIONS) {
                std::cerr << "Error: Memory pool exhausted and heap allocation limit reached. "
                          << "Dataset too large for safe processing." << std::endl;
                throw std::bad_alloc();  // Throw exception instead of returning nullptr to prevent crashes
            }

            heap_allocation_count++;
            // Fall back to heap allocation
#ifdef _MSC_VER
            void* ptr = _aligned_malloc(size, ALIGNMENT);
            if (!ptr) throw std::bad_alloc();
            return ptr;
#else
            void* ptr = std::aligned_alloc(ALIGNMENT, size);
            if (!ptr) throw std::bad_alloc();
            return ptr;
#endif
        }

        void* ptr = pool + offset;
        offset += size;
        return ptr;
    }

    void reset() {
        offset = 0;
        heap_allocation_count = 0;  // Reset heap allocation counter
    }

    size_t get_heap_allocation_count() const {
        return heap_allocation_count;
    }

    size_t get_pool_usage_bytes() const {
        return offset;
    }

    double get_pool_usage_percentage() const {
        return (static_cast<double>(offset) / POOL_SIZE) * 100.0;
    }

    void log_memory_usage() const {
        std::cout << "Memory Pool Usage: " << get_pool_usage_bytes() << " / " << POOL_SIZE
                  << " bytes (" << std::fixed << std::setprecision(1) << get_pool_usage_percentage()
                  << "%), Heap allocations: " << heap_allocation_count << std::endl;
    }

    bool is_pool_memory(void* ptr) const {
        return ptr >= pool && ptr < pool + POOL_SIZE;
    }

    void deallocate(void* ptr) {
        if (!is_pool_memory(ptr)) {
#ifdef _MSC_VER
            _aligned_free(ptr);
#else
            std::free(ptr);
#endif
        }
        // Pool memory is automatically reclaimed on reset
    }
};

// Thread-local memory pool for better performance
thread_local MemoryPool g_memory_pool;

/**
 * Custom allocator using memory pool
 */
template<typename T>
class PoolAllocator {
public:
    using value_type = T;
    using pointer = T*;
    using const_pointer = const T*;
    using reference = T&;
    using const_reference = const T&;
    using size_type = std::size_t;
    using difference_type = std::ptrdiff_t;

    template<typename U>
    struct rebind {
        using other = PoolAllocator<U>;
    };

    PoolAllocator() = default;

    template<typename U>
    PoolAllocator(const PoolAllocator<U>&) {}

    pointer allocate(size_type n) {
        return static_cast<pointer>(g_memory_pool.allocate(n * sizeof(T)));
    }

    void deallocate(pointer p, size_type) {
        g_memory_pool.deallocate(p);
    }

    template<typename U>
    bool operator==(const PoolAllocator<U>&) const { return true; }

    template<typename U>
    bool operator!=(const PoolAllocator<U>&) const { return false; }
};

// Type aliases for pool-allocated containers
template<typename T>
using PoolVector = std::vector<T, PoolAllocator<T>>;

/**
 * SIMD-optimized min/max finding for Donchian calculations
 */
inline void find_min_max_simd(const double* data, size_t length, double& min_val, double& max_val) {
#if SIMD_ENABLED && defined(__AVX2__)
    if (length >= 4) {
        __m256d min_vec = _mm256_set1_pd(std::numeric_limits<double>::max());
        __m256d max_vec = _mm256_set1_pd(std::numeric_limits<double>::lowest());

        size_t simd_end = (length / 4) * 4;

        // Process 4 doubles at a time with AVX2
        #pragma omp simd aligned(data:32)
        for (size_t i = 0; i < simd_end; i += 4) {
            __m256d values = _mm256_loadu_pd(&data[i]);
            min_vec = _mm256_min_pd(min_vec, values);
            max_vec = _mm256_max_pd(max_vec, values);
        }

        // Extract min/max from vectors
        alignas(32) double min_array[4], max_array[4];
        _mm256_store_pd(min_array, min_vec);
        _mm256_store_pd(max_array, max_vec);

        min_val = *std::min_element(min_array, min_array + 4);
        max_val = *std::max_element(max_array, max_array + 4);

        // Handle remaining elements
        for (size_t i = simd_end; i < length; ++i) {
            min_val = std::min(min_val, data[i]);
            max_val = std::max(max_val, data[i]);
        }
    } else {
        // Fallback for small arrays
        min_val = *std::min_element(data, data + length);
        max_val = *std::max_element(data, data + length);
    }
#else
    // Scalar fallback with compiler auto-vectorization hints
    min_val = std::numeric_limits<double>::max();
    max_val = std::numeric_limits<double>::lowest();

    #pragma omp simd reduction(min:min_val) reduction(max:max_val)
    for (size_t i = 0; i < length; ++i) {
        min_val = std::min(min_val, data[i]);
        max_val = std::max(max_val, data[i]);
    }
#endif
}

/**
 * SIMD-optimized standard deviation calculation
 */
inline double calculate_std_dev_simd(const double* data, size_t length, double mean) {
#if SIMD_ENABLED && defined(__AVX2__)
    if (length >= 4) {
        __m256d sum_sq = _mm256_setzero_pd();
        __m256d mean_vec = _mm256_set1_pd(mean);

        size_t simd_end = (length / 4) * 4;

        // Process 4 doubles at a time
        #pragma omp simd aligned(data:32)
        for (size_t i = 0; i < simd_end; i += 4) {
            __m256d values = _mm256_loadu_pd(&data[i]);
            __m256d diff = _mm256_sub_pd(values, mean_vec);
            __m256d sq_diff = _mm256_mul_pd(diff, diff);
            sum_sq = _mm256_add_pd(sum_sq, sq_diff);
        }

        // Extract sum from vector
        alignas(32) double sum_array[4];
        _mm256_store_pd(sum_array, sum_sq);
        double total_sum = sum_array[0] + sum_array[1] + sum_array[2] + sum_array[3];

        // Handle remaining elements
        for (size_t i = simd_end; i < length; ++i) {
            double diff = data[i] - mean;
            total_sum += diff * diff;
        }

        return length > 1 ? std::sqrt(total_sum / (length - 1)) : 0.0;
    } else {
        // Fallback for small arrays
        double sum_sq = 0.0;
        for (size_t i = 0; i < length; ++i) {
            double diff = data[i] - mean;
            sum_sq += diff * diff;
        }
        return length > 1 ? std::sqrt(sum_sq / (length - 1)) : 0.0;
    }
#else
    // Scalar fallback with compiler hints
    double sum_sq = 0.0;
    #pragma omp simd reduction(+:sum_sq)
    for (size_t i = 0; i < length; ++i) {
        double diff = data[i] - mean;
        sum_sq += diff * diff;
    }
    return length > 1 ? std::sqrt(sum_sq / (length - 1)) : 0.0;
#endif
}

/**
 * Result structure for error handling across ABI boundaries
 */
template<typename T>
struct Result {
    T data;
    bool success;
    std::string error_message;

    Result(T&& d) : data(std::move(d)), success(true) {}
    Result(const T& d) : data(d), success(true) {}
    Result(const std::string& error) : success(false), error_message(error) {}
};

// Specialization for std::string to avoid constructor ambiguity
template<>
struct Result<std::string> {
    std::string data;
    bool success;
    std::string error_message;

    Result(const std::string& d) : data(d), success(true) {}
    Result(std::string&& d) : data(std::move(d)), success(true) {}

    // Static factory methods to avoid ambiguity
    static Result<std::string> success_result(const std::string& d) {
        Result<std::string> r;
        r.data = d;
        r.success = true;
        return r;
    }

    static Result<std::string> error_result(const std::string& error) {
        Result<std::string> r;
        r.success = false;
        r.error_message = error;
        return r;
    }

private:
    Result() = default;
};

/**
 * High-performance Donchian midpoint calculation with SIMD optimization
 */
Result<std::vector<double>> calculate_donchian_midpoint_simd(
    const std::vector<double>& closes, 
    int length
) {
    try {
        if (closes.empty()) {
            return Result<std::vector<double>>("Input data is empty");
        }
        
        if (length <= 0 || length > static_cast<int>(closes.size())) {
            return Result<std::vector<double>>("Invalid length parameter");
        }
        
        // Use memory pool for better performance
        g_memory_pool.reset();
        if (closes.size() > 1000) {
            std::cout << "Donchian calculation - Memory pool reset for dataset size: " << closes.size() << std::endl;
        }
        PoolVector<double> result(closes.size(), std::numeric_limits<double>::quiet_NaN());
        
        // Process data in chunks for better cache locality
        const size_t chunk_size = 64;  // Cache line size
        
        // Use chunked processing for large datasets to prevent memory exhaustion
        const size_t CHUNK_SIZE = 10000;  // Process in 10k chunks
        const size_t PARALLEL_THRESHOLD = 1000;

        if (closes.size() > CHUNK_SIZE) {
            // Process in chunks to prevent memory issues
            for (size_t chunk_start = length - 1; chunk_start < closes.size(); chunk_start += CHUNK_SIZE) {
                size_t chunk_end = std::min(chunk_start + CHUNK_SIZE, closes.size());

                #pragma omp parallel for if(chunk_end - chunk_start > PARALLEL_THRESHOLD)
                for (int i = static_cast<int>(chunk_start); i < static_cast<int>(chunk_end); ++i) {
                    double min_val, max_val;
                    find_min_max_simd(&closes[i - length + 1], length, min_val, max_val);
                    result[i] = (max_val + min_val) * 0.5;
                }

                // Reset memory pool between chunks to prevent memory buildup
                if (chunk_start % (CHUNK_SIZE * 5) == 0) {
                    if (closes.size() > 10000) {
                        g_memory_pool.log_memory_usage();
                    }
                    g_memory_pool.reset();
                }
            }
        } else {
            // Use SIMD-optimized calculation with parallel processing for smaller datasets
            #pragma omp parallel for if(closes.size() > PARALLEL_THRESHOLD)
            for (int i = static_cast<int>(length - 1); i < static_cast<int>(closes.size()); ++i) {
                double min_val, max_val;
                find_min_max_simd(&closes[i - length + 1], length, min_val, max_val);
                result[i] = (max_val + min_val) * 0.5;
            }
        }
        
        // Convert back to std::vector for return
        return Result<std::vector<double>>(std::vector<double>(result.begin(), result.end()));
        
    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Optimized rolling maximum calculation
 */
Result<std::vector<double>> rolling_max(
    const std::vector<double>& data, 
    int window
) {
    try {
        if (data.empty() || window <= 0) {
            return Result<std::vector<double>>("Invalid parameters");
        }
        
        std::vector<double> result(data.size(), std::numeric_limits<double>::quiet_NaN());
        
        for (size_t i = window - 1; i < data.size(); ++i) {
            double max_val = data[i - window + 1];
            for (int j = 1; j < window; ++j) {
                max_val = std::max(max_val, data[i - window + 1 + j]);
            }
            result[i] = max_val;
        }
        
        return Result<std::vector<double>>(std::move(result));
        
    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Optimized rolling minimum calculation
 */
Result<std::vector<double>> rolling_min(
    const std::vector<double>& data, 
    int window
) {
    try {
        if (data.empty() || window <= 0) {
            return Result<std::vector<double>>("Invalid parameters");
        }
        
        std::vector<double> result(data.size(), std::numeric_limits<double>::quiet_NaN());
        
        for (size_t i = window - 1; i < data.size(); ++i) {
            double min_val = data[i - window + 1];
            for (int j = 1; j < window; ++j) {
                min_val = std::min(min_val, data[i - window + 1 + j]);
            }
            result[i] = min_val;
        }
        
        return Result<std::vector<double>>(std::move(result));
        
    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Simple moving average with SIMD optimization
 */
Result<std::vector<double>> simple_moving_average(
    const std::vector<double>& data, 
    int window
) {
    try {
        if (data.empty() || window <= 0) {
            return Result<std::vector<double>>("Invalid parameters");
        }
        
        std::vector<double> result(data.size(), std::numeric_limits<double>::quiet_NaN());
        
        // Calculate first window sum
        if (data.size() >= static_cast<size_t>(window)) {
            double sum = 0.0;
            for (int i = 0; i < window; ++i) {
                sum += data[i];
            }
            result[window - 1] = sum / window;
            
            // Rolling calculation for efficiency
            for (size_t i = window; i < data.size(); ++i) {
                sum = sum - data[i - window] + data[i];
                result[i] = sum / window;
            }
        }
        
        return Result<std::vector<double>>(std::move(result));
        
    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Structure for vector direction change tracking
 */
struct VectorDirectionChange {
    std::string cycle_type;      // "bullish" or "bearish"
    bool direction_changed;      // Whether direction changed at this index
    double pivot_price;          // Pivot price (NaN if no change)
    std::string current_direction; // "up" or "down"

    VectorDirectionChange(const std::string& cycle, bool changed, double pivot, const std::string& direction)
        : cycle_type(cycle), direction_changed(changed), pivot_price(pivot), current_direction(direction) {}
};

/**
 * Structure for rebased OHLC data
 */
struct RebasedOHLC {
    int index;
    double open_pct;
    double high_pct;
    double low_pct;
    double close_pct;

    RebasedOHLC(int idx, double o, double h, double l, double c)
        : index(idx), open_pct(o), high_pct(h), low_pct(l), close_pct(c) {}
};

/**
 * High-performance vector direction tracking for rebasing
 */
Result<std::vector<VectorDirectionChange>> track_vector_direction_changes(
    const std::vector<double>& vector
) {
    try {
        if (vector.empty()) {
            return Result<std::vector<VectorDirectionChange>>("Vector data is empty");
        }

        std::vector<VectorDirectionChange> cycles;
        cycles.reserve(vector.size());

        std::string current_cycle = "bullish";
        std::string vector_direction = "up";
        double prev_vector_value = std::numeric_limits<double>::quiet_NaN();

        for (size_t i = 0; i < vector.size(); ++i) {
            if (std::isnan(vector[i])) {
                // Skip NaN values
                cycles.emplace_back(current_cycle, false, std::numeric_limits<double>::quiet_NaN(), vector_direction);
                continue;
            }

            if (i == 0) {
                // First candle - special handling
                std::string current_direction = "up";  // Default to up for first candle

                // Set the first candle as a pivot point with direction change
                double first_vector_value = vector[i];
                cycles.emplace_back("bullish", true, first_vector_value, current_direction);

                // Store the first vector value as the pivot price
                prev_vector_value = first_vector_value;
                vector_direction = current_direction;
            } else {
                // Determine vector direction
                double current_vector = vector[i];
                std::string current_direction;

                if (current_vector > prev_vector_value) {
                    // Vector moved higher - equivalent to green background in PineScript
                    current_direction = "up";
                } else if (current_vector < prev_vector_value) {
                    // Vector moved lower - equivalent to red background in PineScript
                    current_direction = "down";
                } else {
                    // Vector stayed the same - maintain previous direction
                    current_direction = vector_direction;
                }

                // Check if vector direction changed
                bool direction_changed = current_direction != vector_direction;

                // Set pivot price when vector direction changes
                double pivot_price = std::numeric_limits<double>::quiet_NaN();
                if (direction_changed) {
                    // Use the vector price right before the direction change as the pivot
                    pivot_price = prev_vector_value;
                    vector_direction = current_direction;
                }

                // Determine cycle type based on vector direction
                std::string new_cycle = (current_direction == "up") ? "bullish" : "bearish";
                current_cycle = new_cycle;

                // Store cycle information
                cycles.emplace_back(new_cycle, direction_changed, pivot_price, current_direction);
                prev_vector_value = current_vector;
            }
        }

        return Result<std::vector<VectorDirectionChange>>(std::move(cycles));

    } catch (const std::exception& e) {
        return Result<std::vector<VectorDirectionChange>>(std::string("Exception: ") + e.what());
    }
}

/**
 * High-performance rebased price calculation
 */
Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>> calculate_rebased_prices(
    const std::vector<double>& opens,
    const std::vector<double>& highs,
    const std::vector<double>& lows,
    const std::vector<double>& closes,
    const std::vector<double>& vector,
    const std::vector<VectorDirectionChange>& cycles
) {
    try {
        if (opens.size() != highs.size() || highs.size() != lows.size() ||
            lows.size() != closes.size() || closes.size() != vector.size()) {
            return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
                "Input arrays must have the same size");
        }

        // Memory safety check for rebased price calculation
        const size_t MAX_REBASED_SIZE = 200000;  // 200k elements max for rebasing
        if (opens.size() > MAX_REBASED_SIZE) {
            return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
                "Dataset too large for rebased price calculation. Maximum size: " +
                std::to_string(MAX_REBASED_SIZE) + " elements"
            );
        }

        std::vector<RebasedOHLC> rebased_data;
        std::vector<double> rebased_vector;

        // Initialize pivot for rebasing with the first available price
        double visualization_pivot = std::numeric_limits<double>::quiet_NaN();

        // Set initial pivot to the first available price to ensure we always have data
        if (!opens.empty()) {
            visualization_pivot = opens[0];  // Always start from index 0
        }

        // Ensure the first cycle is properly handled
        if (!cycles.empty() && cycles[0].direction_changed) {
            // Update the visualization pivot with the first vector value if available
            visualization_pivot = cycles[0].pivot_price;
        }

        for (size_t idx = 0; idx < opens.size(); ++idx) {  // Always start from index 0
            // Check if we need to rebase based on vector direction changes
            if (idx < cycles.size()) {
                // Get the cycle information for this candle
                bool direction_changed = cycles[idx].direction_changed;
                double pivot_price = cycles[idx].pivot_price;

                // ONLY rebase when the vector direction changes
                if (direction_changed && !std::isnan(pivot_price)) {
                    // Update the visualization pivot
                    visualization_pivot = pivot_price;
                }
            }

            // Calculate rebased prices using the current visualization pivot
            if (!std::isnan(visualization_pivot) && visualization_pivot != 0.0) {
                double open_price = ((opens[idx] / visualization_pivot) - 1.0) * 100.0;
                double high_price = ((highs[idx] / visualization_pivot) - 1.0) * 100.0;
                double low_price = ((lows[idx] / visualization_pivot) - 1.0) * 100.0;
                double close_price = ((closes[idx] / visualization_pivot) - 1.0) * 100.0;

                // Calculate rebased vector value
                double vector_price = 0.0;
                if (idx < vector.size() && !std::isnan(vector[idx])) {
                    vector_price = ((vector[idx] / visualization_pivot) - 1.0) * 100.0;
                }

                // Store rebased data
                rebased_data.emplace_back(static_cast<int>(idx), open_price, high_price, low_price, close_price);
                rebased_vector.push_back(vector_price);
            }
        }

        return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
            std::make_pair(std::move(rebased_data), std::move(rebased_vector)));

    } catch (const std::exception& e) {
        return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
            std::string("Exception: ") + e.what());
    }
}

/**
 * Structure for peak/trough annotation with ray information
 */
struct PeakTroughAnnotation {
    int index;           // Candle index
    double position;     // Y position for annotation (high + 0.05% or low - 0.05%)
    std::string type;    // "peak" or "trough"
    std::string label;   // "H (count)" or "L (count)"
    double level;        // Actual extreme level (without offset)
    int ray_end;         // End index of the ray
    bool is_closed;      // Whether the extreme has been touched/closed
    std::string cycle_type; // "bullish" or "bearish"

    PeakTroughAnnotation(int idx, double pos, const std::string& t, const std::string& lbl)
        : index(idx), position(pos), type(t), label(lbl), level(0.0), ray_end(idx), is_closed(false), cycle_type("") {}

    PeakTroughAnnotation(int idx, double pos, const std::string& t, const std::string& lbl,
                        double lvl, int end, bool closed, const std::string& cycle)
        : index(idx), position(pos), type(t), label(lbl), level(lvl), ray_end(end), is_closed(closed), cycle_type(cycle) {}
};

/**
 * Ray information structure for peaks and troughs
 */
struct RayInfo {
    int start_index;
    double level;
    int end_index;
    std::string cycle_type;
    bool is_closed;
    int candle_count;

    RayInfo(int start, double lvl, int end, const std::string& type, bool closed, int count)
        : start_index(start), level(lvl), end_index(end), cycle_type(type), is_closed(closed), candle_count(count) {}
};

/**
 * High-performance peak and trough detection with rays using the exact same method as market_odds_example.py
 *
 * Algorithm:
 * - Determine cycles based on close price: bullish (close >= 0), bearish (close < 0)
 * - Track extremes within each cycle
 * - Mark extremes when cycle changes
 * - Count candles in each cycle for annotation
 * - Detect open/closed states based on subsequent price action
 * - Generate ray information for visualization
 */
Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>> calculate_peaks_and_troughs(
    const std::vector<int>& indices,
    const std::vector<double>& opens,
    const std::vector<double>& highs,
    const std::vector<double>& lows,
    const std::vector<double>& closes
) {
    try {
        if (indices.size() != opens.size() || opens.size() != highs.size() ||
            highs.size() != lows.size() || lows.size() != closes.size()) {
            return Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>(
                "Input arrays must have the same size");
        }

        std::vector<PeakTroughAnnotation> peaks;
        std::vector<PeakTroughAnnotation> troughs;
        std::vector<RayInfo> rays_info;

        if (indices.empty()) {
            return Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>(
                std::make_pair(std::move(peaks), std::move(troughs)));
        }

        // State tracking for cycle-based detection (same as market_odds_example.py)
        std::string current_cycle = "";
        double extreme_value = 0.0;
        int extreme_index = -1;
        int cycle_start_index = -1;

        for (size_t i = 0; i < indices.size(); ++i) {
            double current_close = closes[i];
            double current_high = highs[i];
            double current_low = lows[i];
            int current_index = indices[i];

            // Determine cycle based on close price (same as market_odds_example.py)
            std::string cycle = (current_close >= 0.0) ? "bullish" : "bearish";

            if (current_cycle.empty()) {
                // First candle
                current_cycle = cycle;
                extreme_index = current_index;
                extreme_value = (cycle == "bullish") ? current_high : current_low;
                cycle_start_index = current_index;
            } else {
                if (cycle == current_cycle) {
                    // Same cycle, update extreme if needed
                    if (current_cycle == "bullish" && current_high > extreme_value) {
                        extreme_value = current_high;
                        extreme_index = current_index;
                    } else if (current_cycle == "bearish" && current_low < extreme_value) {
                        extreme_value = current_low;
                        extreme_index = current_index;
                    }
                } else {
                    // Cycle changed, mark the extreme from the previous cycle
                    int candle_count = extreme_index - cycle_start_index + 1;

                    // Check if the extreme is closed (same logic as market_odds_example.py)
                    bool is_closed = false;
                    int ray_end = indices.back(); // Default to last candle
                    double tolerance = 0.0001; // 0.01% tolerance

                    // Find the extreme in the data array
                    size_t extreme_data_index = 0;
                    for (size_t j = 0; j < indices.size(); ++j) {
                        if (indices[j] == extreme_index) {
                            extreme_data_index = j;
                            break;
                        }
                    }

                    // Check subsequent candles for closure
                    for (size_t j = extreme_data_index + 1; j < indices.size(); ++j) {
                        if (current_cycle == "bullish") {
                            // For peaks, close when any subsequent high equals or exceeds the peak
                            if (highs[j] >= (extreme_value - tolerance)) {
                                ray_end = indices[j];
                                is_closed = true;
                                break;
                            }
                        } else {
                            // For troughs, close when any subsequent low equals or goes below the trough
                            if (lows[j] <= (extreme_value + tolerance)) {
                                ray_end = indices[j];
                                is_closed = true;
                                break;
                            }
                        }
                    }

                    // Store ray information
                    rays_info.emplace_back(extreme_index, extreme_value, ray_end, current_cycle, is_closed, candle_count);

                    // Create annotation with ray information
                    double offset = std::abs(extreme_value) * 0.001; // 0.1% offset
                    std::string label = (current_cycle == "bullish" ? "H (" : "L (") + std::to_string(candle_count) + ")";

                    if (current_cycle == "bullish") {
                        double peak_position = extreme_value + offset;
                        peaks.emplace_back(extreme_index, peak_position, "peak", label, extreme_value, ray_end, is_closed, current_cycle);
                    } else {
                        double trough_position = extreme_value - offset;
                        troughs.emplace_back(extreme_index, trough_position, "trough", label, extreme_value, ray_end, is_closed, current_cycle);
                    }

                    // Start new cycle
                    current_cycle = cycle;
                    extreme_index = current_index;
                    extreme_value = (cycle == "bullish") ? current_high : current_low;
                    cycle_start_index = current_index;
                }
            }
        }

        // Mark the final extreme if we have one
        if (!current_cycle.empty() && extreme_index != -1) {
            int candle_count = extreme_index - cycle_start_index + 1;

            // Check if final extreme is closed
            bool is_closed = false;
            int ray_end = indices.back();
            double tolerance = 0.0001;

            size_t extreme_data_index = 0;
            for (size_t j = 0; j < indices.size(); ++j) {
                if (indices[j] == extreme_index) {
                    extreme_data_index = j;
                    break;
                }
            }

            for (size_t j = extreme_data_index + 1; j < indices.size(); ++j) {
                if (current_cycle == "bullish") {
                    if (highs[j] >= (extreme_value - tolerance)) {
                        ray_end = indices[j];
                        is_closed = true;
                        break;
                    }
                } else {
                    if (lows[j] <= (extreme_value + tolerance)) {
                        ray_end = indices[j];
                        is_closed = true;
                        break;
                    }
                }
            }

            // Store final ray information
            rays_info.emplace_back(extreme_index, extreme_value, ray_end, current_cycle, is_closed, candle_count);

            // Create final annotation with ray information
            double offset = std::abs(extreme_value) * 0.001;
            std::string label = (current_cycle == "bullish" ? "H (" : "L (") + std::to_string(candle_count) + ")";

            if (current_cycle == "bullish") {
                double peak_position = extreme_value + offset;
                peaks.emplace_back(extreme_index, peak_position, "peak", label, extreme_value, ray_end, is_closed, current_cycle);
            } else {
                double trough_position = extreme_value - offset;
                troughs.emplace_back(extreme_index, trough_position, "trough", label, extreme_value, ray_end, is_closed, current_cycle);
            }
        }

        return Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>(
            std::make_pair(std::move(peaks), std::move(troughs)));

    } catch (const std::exception& e) {
        return Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>(
            std::string("Exception: ") + e.what());
    }
}

/**
 * Structure for Donchian indicator strip segment
 */
struct DonchianStripSegment {
    int start_idx;
    int end_idx;
    std::string color;  // "#00ff00" for green, "#ff0000" for red

    DonchianStripSegment(int start, int end, const std::string& c)
        : start_idx(start), end_idx(end), color(c) {}
};

/**
 * Calculate Donchian indicator strip segments
 * Compares closes with donchian_midpoint_50 and creates colored segments
 */
Result<std::vector<DonchianStripSegment>> calculate_donchian_strip_segments(
    const std::vector<double>& closes,
    const std::vector<double>& donchian_midpoint_50
) {
    try {
        if (closes.empty() || donchian_midpoint_50.empty()) {
            return Result<std::vector<DonchianStripSegment>>("Input vectors are empty");
        }

        if (closes.size() != donchian_midpoint_50.size()) {
            return Result<std::vector<DonchianStripSegment>>("Input vectors must have the same size");
        }

        std::vector<DonchianStripSegment> segments;
        std::string current_color;
        int segment_start = -1;

        for (size_t i = 0; i < closes.size(); ++i) {
            // Skip NaN values
            if (std::isnan(closes[i]) || std::isnan(donchian_midpoint_50[i])) {
                continue;
            }

            // Determine color: green if price >= midpoint, red if below
            std::string color = (closes[i] >= donchian_midpoint_50[i]) ? "#00ff00" : "#ff0000";

            // If color changed or this is the last point, finish current segment
            if (current_color != color || i == closes.size() - 1) {
                if (!current_color.empty() && segment_start != -1) {
                    // Add segment from segment_start to current position
                    int end_idx = (i == closes.size() - 1) ? static_cast<int>(i) : static_cast<int>(i) - 1;
                    if (end_idx >= segment_start) {
                        segments.emplace_back(segment_start, end_idx, current_color);
                    }
                }

                // Start new segment
                segment_start = static_cast<int>(i);
                current_color = color;
            }
        }

        return Result<std::vector<DonchianStripSegment>>(std::move(segments));

    } catch (const std::exception& e) {
        return Result<std::vector<DonchianStripSegment>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate data length for array operations
 */
Result<int> calculate_data_length(const std::vector<double>& data) {
    try {
        return Result<int>(static_cast<int>(data.size()));
    } catch (const std::exception& e) {
        return Result<int>(std::string("Exception: ") + e.what());
    }
}

/**
 * Generate sequential timestamps array as numpy array
 */
Result<py::array_t<double>> generate_timestamps(int length) {
    try {
        // Create numpy array directly for C++ compatibility
        auto timestamps = py::array_t<double>(length);
        auto buf = timestamps.request();
        double* ptr = static_cast<double*>(buf.ptr);

        for (int i = 0; i < length; ++i) {
            ptr[i] = static_cast<double>(i);
        }

        return Result<py::array_t<double>>(std::move(timestamps));
    } catch (const std::exception& e) {
        return Result<py::array_t<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate total candles needed for timeframe
 */
Result<int> calculate_total_candles_needed(int dtl, int candles_per_day) {
    try {
        if (candles_per_day <= 0) {
            return Result<int>("Invalid candles_per_day parameter");
        }
        int total = dtl * candles_per_day;
        return Result<int>(total);
    } catch (const std::exception& e) {
        return Result<int>(std::string("Exception: ") + e.what());
    }
}

/**
 * Apply skip logic to array - set first skip_count elements to NaN
 */
Result<std::vector<double>> apply_skip_logic_to_array(
    const std::vector<double>& input_array,
    int skip_count
) {
    try {
        std::vector<double> result = input_array;

        if (skip_count > 0 && skip_count < static_cast<int>(result.size())) {
            for (int i = 0; i < skip_count; ++i) {
                result[i] = std::numeric_limits<double>::quiet_NaN();
            }
        }

        return Result<std::vector<double>>(std::move(result));
    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate actual price from pivot and percentage level
 */
Result<double> calculate_actual_price(double current_pivot, double level_percentage) {
    try {
        if (current_pivot == 0.0) {
            return Result<double>("Invalid pivot price (zero)");
        }

        double actual_price = current_pivot * (1.0 + level_percentage / 100.0);
        return Result<double>(actual_price);
    } catch (const std::exception& e) {
        return Result<double>(std::string("Exception: ") + e.what());
    }
}

/**
 * Visualization Data Structures
 */
struct CandlestickRenderData {
    double x, open, high, low, close;
    std::string body_color, wick_color;
    bool is_valid;
};

struct StandardDeviationResult {
    double positive_std;
    double negative_std;
    int positive_count;
    int negative_count;
};

struct MeanLevelsResult {
    double mean_peak_level;
    double mean_trough_level;
    int peak_count;
    int trough_count;
};

struct StairstepData {
    std::vector<double> x_values;
    std::vector<double> y_values;
    bool is_valid;
};

/**
 * Prepare candlestick rendering data - moves computation from UI to backend
 */
Result<std::vector<CandlestickRenderData>> prepare_candlestick_render_data(
    const std::vector<double>& timestamps,
    const std::vector<double>& opens,
    const std::vector<double>& highs,
    const std::vector<double>& lows,
    const std::vector<double>& closes
) {
    try {
        if (timestamps.size() != opens.size() || opens.size() != highs.size() ||
            highs.size() != lows.size() || lows.size() != closes.size()) {
            return Result<std::vector<CandlestickRenderData>>("Mismatched array sizes");
        }

        std::vector<CandlestickRenderData> render_data;
        render_data.reserve(timestamps.size());

        for (size_t i = 0; i < timestamps.size(); ++i) {
            CandlestickRenderData candle;
            candle.x = timestamps[i];
            candle.open = opens[i];
            candle.high = highs[i];
            candle.low = lows[i];
            candle.close = closes[i];

            // Check for NaN values
            candle.is_valid = !std::isnan(candle.open) && !std::isnan(candle.high) &&
                             !std::isnan(candle.low) && !std::isnan(candle.close);

            if (candle.is_valid) {
                // Determine colors
                if (candle.close >= candle.open) {
                    candle.body_color = "#00ff00";  // Green
                    candle.wick_color = "#00ff00";
                } else {
                    candle.body_color = "#ff0000";  // Red
                    candle.wick_color = "#ff0000";
                }
            }

            render_data.push_back(candle);
        }

        return Result<std::vector<CandlestickRenderData>>(std::move(render_data));

    } catch (const std::exception& e) {
        return Result<std::vector<CandlestickRenderData>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate separated standard deviations for positive and negative percentages
 */
Result<StandardDeviationResult> calculate_separated_standard_deviations(
    const std::vector<double>& percentage_closes
) {
    try {
        std::vector<double> positive_values;
        std::vector<double> negative_values;

        // Separate positive and negative values
        for (double value : percentage_closes) {
            if (!std::isnan(value)) {
                if (value > 0.0) {
                    positive_values.push_back(value);
                } else if (value < 0.0) {
                    negative_values.push_back(value);
                }
            }
        }

        StandardDeviationResult result;
        result.positive_count = static_cast<int>(positive_values.size());
        result.negative_count = static_cast<int>(negative_values.size());

        // Calculate positive standard deviation
        if (positive_values.size() > 1) {
            double sum = std::accumulate(positive_values.begin(), positive_values.end(), 0.0);
            double mean = sum / positive_values.size();

            double sq_sum = 0.0;
            for (double value : positive_values) {
                sq_sum += (value - mean) * (value - mean);
            }
            result.positive_std = std::sqrt(sq_sum / (positive_values.size() - 1));
        } else {
            result.positive_std = 0.0;
        }

        // Calculate negative standard deviation
        if (negative_values.size() > 1) {
            double sum = std::accumulate(negative_values.begin(), negative_values.end(), 0.0);
            double mean = sum / negative_values.size();

            double sq_sum = 0.0;
            for (double value : negative_values) {
                sq_sum += (value - mean) * (value - mean);
            }
            result.negative_std = std::sqrt(sq_sum / (negative_values.size() - 1));
        } else {
            result.negative_std = 0.0;
        }

        return Result<StandardDeviationResult>(result);

    } catch (const std::exception& e) {
        return Result<StandardDeviationResult>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate percentile levels based on peaks and troughs
 */
Result<StandardDeviationResult> calculate_separated_standard_deviations_from_peaks_troughs(
    const std::vector<PeakTroughAnnotation>& peaks,
    const std::vector<PeakTroughAnnotation>& troughs
) {
    try {
        std::vector<double> peak_levels;    // Peak levels (positive percentages)
        std::vector<double> trough_levels;  // Trough levels (negative percentages)

        // Extract peak levels (positive values)
        for (const auto& peak : peaks) {
            if (!std::isnan(peak.level) && peak.level > 0.0) {
                peak_levels.push_back(peak.level);
            }
        }

        // Extract trough levels (negative values)
        for (const auto& trough : troughs) {
            if (!std::isnan(trough.level) && trough.level < 0.0) {
                trough_levels.push_back(trough.level);
            }
        }

        StandardDeviationResult result;
        result.positive_count = static_cast<int>(peak_levels.size());
        result.negative_count = static_cast<int>(trough_levels.size());

        // For compatibility, we'll store percentile ranges in the std fields
        // This is a bit of a hack but maintains the existing interface
        if (peak_levels.size() > 0) {
            std::sort(peak_levels.begin(), peak_levels.end());
            double min_peak = peak_levels.front();
            double max_peak = peak_levels.back();
            result.positive_std = max_peak - min_peak;  // Range of peaks
        } else {
            result.positive_std = 0.0;
        }

        if (trough_levels.size() > 0) {
            std::sort(trough_levels.begin(), trough_levels.end());
            double min_trough = trough_levels.front();
            double max_trough = trough_levels.back();
            result.negative_std = max_trough - min_trough;  // Range of troughs
        } else {
            result.negative_std = 0.0;
        }

        return Result<StandardDeviationResult>(result);

    } catch (const std::exception& e) {
        return Result<StandardDeviationResult>(std::string("Exception: ") + e.what());
    }
}

/**
 * Determine Donchian color based on price position
 */
Result<std::string> determine_donchian_color(
    const std::vector<double>& donchian_midpoint,
    const std::vector<double>& closes
) {
    try {
        if (donchian_midpoint.empty() || closes.empty()) {
            return Result<std::string>("#CCCCCC");  // Gray fallback
        }

        // Get last valid values
        double last_donchian = std::numeric_limits<double>::quiet_NaN();
        double last_close = std::numeric_limits<double>::quiet_NaN();

        for (int i = static_cast<int>(donchian_midpoint.size()) - 1; i >= 0; --i) {
            if (!std::isnan(donchian_midpoint[i])) {
                last_donchian = donchian_midpoint[i];
                break;
            }
        }

        for (int i = static_cast<int>(closes.size()) - 1; i >= 0; --i) {
            if (!std::isnan(closes[i])) {
                last_close = closes[i];
                break;
            }
        }

        if (std::isnan(last_donchian) || std::isnan(last_close)) {
            return Result<std::string>::success_result("#CCCCCC");  // Gray fallback
        }

        // Determine color based on position
        if (last_close > last_donchian) {
            return Result<std::string>::success_result("#00FF00");  // Green - above Donchian
        } else {
            return Result<std::string>::success_result("#FF0000");  // Red - below Donchian
        }

    } catch (const std::exception& e) {
        return Result<std::string>::success_result("#CCCCCC");  // Gray fallback on error
    }
}

/**
 * Prepare Donchian stairstep data for UI rendering
 */
Result<StairstepData> prepare_donchian_stairstep_data(
    const std::vector<double>& timestamps,
    const std::vector<double>& donchian_values
) {
    try {
        if (timestamps.size() != donchian_values.size()) {
            return Result<StairstepData>("Mismatched array sizes");
        }

        StairstepData stairstep;
        stairstep.x_values.reserve(timestamps.size() * 2);
        stairstep.y_values.reserve(donchian_values.size() * 2);

        double last_valid_value = std::numeric_limits<double>::quiet_NaN();

        for (size_t i = 0; i < timestamps.size(); ++i) {
            double current_value = donchian_values[i];

            if (!std::isnan(current_value)) {
                if (!std::isnan(last_valid_value)) {
                    // Create horizontal line from previous point
                    stairstep.x_values.push_back(timestamps[i]);
                    stairstep.y_values.push_back(last_valid_value);
                }

                // Add current point
                stairstep.x_values.push_back(timestamps[i]);
                stairstep.y_values.push_back(current_value);

                last_valid_value = current_value;
            }
        }

        stairstep.is_valid = !stairstep.x_values.empty();
        return Result<StairstepData>(std::move(stairstep));

    } catch (const std::exception& e) {
        return Result<StairstepData>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate mean peak and trough levels
 */
Result<MeanLevelsResult> calculate_mean_peak_trough_levels(
    const std::vector<PeakTroughAnnotation>& peaks,
    const std::vector<PeakTroughAnnotation>& troughs
) {
    try {
        MeanLevelsResult result;
        result.peak_count = static_cast<int>(peaks.size());
        result.trough_count = static_cast<int>(troughs.size());

        // Calculate mean peak level
        if (!peaks.empty()) {
            double sum = 0.0;
            int valid_count = 0;
            for (const auto& peak : peaks) {
                if (std::isfinite(peak.level)) {
                    sum += peak.level;
                    valid_count++;
                }
            }
            if (valid_count > 0) {
                result.mean_peak_level = sum / valid_count;
            } else {
                result.mean_peak_level = 0.0;
            }
        } else {
            result.mean_peak_level = 0.0;
        }

        // Calculate mean trough level
        if (!troughs.empty()) {
            double sum = 0.0;
            int valid_count = 0;
            for (const auto& trough : troughs) {
                if (std::isfinite(trough.level)) {
                    sum += trough.level;
                    valid_count++;
                }
            }
            if (valid_count > 0) {
                result.mean_trough_level = sum / valid_count;
            } else {
                result.mean_trough_level = 0.0;
            }
        } else {
            result.mean_trough_level = 0.0;
        }

        return Result<MeanLevelsResult>(result);

    } catch (const std::exception& e) {
        return Result<MeanLevelsResult>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate Bollinger Bandwidth
 * Bandwidth = (Upper Band - Lower Band) / Middle Band
 * Where bands are calculated using standard deviation
 */
Result<std::vector<double>> calculate_bollinger_bandwidth(
    const std::vector<double>& closes,
    int length,
    double std_dev_multiplier = 2.0
) {
    try {
        if (closes.empty()) {
            return Result<std::vector<double>>("Input vector is empty");
        }

        if (length <= 0 || length > static_cast<int>(closes.size())) {
            return Result<std::vector<double>>("Invalid length parameter");
        }

        // Memory safety check for large datasets
        const size_t MAX_SAFE_SIZE = 500000;  // 500k elements max
        if (closes.size() > MAX_SAFE_SIZE) {
            return Result<std::vector<double>>(
                "Dataset too large for Donchian calculation. Maximum size: " +
                std::to_string(MAX_SAFE_SIZE) + " elements"
            );
        }

        // Use memory pool for better performance
        g_memory_pool.reset();
        if (closes.size() > 1000) {
            std::cout << "Bollinger Bandwidth calculation - Memory pool reset for dataset size: " << closes.size() << std::endl;
        }
        PoolVector<double> bandwidth(closes.size(), std::numeric_limits<double>::quiet_NaN());

        // Use SIMD-optimized calculation with parallel processing
        #pragma omp parallel for if(closes.size() > 1000)
        for (int i = static_cast<int>(length - 1); i < static_cast<int>(closes.size()); ++i) {
            const double* window_data = &closes[i - length + 1];

            // Calculate simple moving average using SIMD
            double sma = 0.0;
            #pragma omp simd reduction(+:sma)
            for (int j = 0; j < length; ++j) {
                sma += window_data[j];
            }
            sma /= length;

            // Calculate standard deviation using SIMD
            double std_dev = calculate_std_dev_simd(window_data, length, sma);

            // Calculate Bollinger Bands
            double upper_band = sma + (std_dev_multiplier * std_dev);
            double lower_band = sma - (std_dev_multiplier * std_dev);

            // Calculate bandwidth (handle length=1 case)
            if (sma != 0.0 && length > 1) {
                bandwidth[i] = (upper_band - lower_band) / sma;
            } else {
                bandwidth[i] = 0.0;  // No bandwidth for length=1
            }
        }

        // Convert back to std::vector for return
        return Result<std::vector<double>>(std::vector<double>(bandwidth.begin(), bandwidth.end()));

    } catch (const std::exception& e) {
        return Result<std::vector<double>>(std::string("Exception: ") + e.what());
    }
}

/**
 * Extract peak and trough strengths from labels - pure C++ computation
 */
Result<py::dict> extract_peak_trough_strengths(const std::vector<PeakTroughAnnotation>& peaks, const std::vector<PeakTroughAnnotation>& troughs) {
    try {
        std::vector<double> peak_strengths;
        std::vector<double> trough_strengths;

        // Extract peak strengths from labels using regex pattern "H (number)"
        std::regex peak_pattern(R"(H \((\d+)\))");
        std::smatch match;

        for (const auto& peak : peaks) {
            if (!peak.label.empty()) {
                if (std::regex_search(peak.label, match, peak_pattern)) {
                    int strength = std::stoi(match[1].str());
                    if (strength >= 0) {  // Allow zero values
                        peak_strengths.push_back(static_cast<double>(strength > 0 ? strength : 1)); // Use 1 as minimum
                    }
                }
            } else {
                // If no label, use a default strength value
                // This is a fallback when labels are not available
                peak_strengths.push_back(10.0); // Default peak strength
            }
        }

        // Extract trough strengths from labels using regex pattern "L (number)"
        std::regex trough_pattern(R"(L \((\d+)\))");

        for (const auto& trough : troughs) {
            if (!trough.label.empty()) {
                if (std::regex_search(trough.label, match, trough_pattern)) {
                    int strength = std::stoi(match[1].str());
                    if (strength >= 0) {  // Allow zero values
                        trough_strengths.push_back(static_cast<double>(strength > 0 ? strength : 1)); // Use 1 as minimum
                    }
                }
            } else {
                // If no label, use a default strength value
                // This is a fallback when labels are not available
                trough_strengths.push_back(8.0); // Default trough strength
            }
        }

        py::dict result;
        result["peak_strengths"] = peak_strengths;
        result["trough_strengths"] = trough_strengths;

        return Result<py::dict>(result);
    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate percentile levels from peaks and troughs - pure C++ computation
 */
Result<py::dict> calculate_percentile_levels_from_peaks_troughs(
    const std::vector<PeakTroughAnnotation>& peaks,
    const std::vector<PeakTroughAnnotation>& troughs,
    double zero_percent_price
) {
    try {
        std::vector<double> peak_levels;    // Peak levels (positive percentages)
        std::vector<double> trough_levels;  // Trough levels (negative percentages)

        // Extract peak levels (positive values)
        for (const auto& peak : peaks) {
            if (!std::isnan(peak.level) && peak.level > 0.0) {
                peak_levels.push_back(peak.level);
            }
        }

        // Extract trough levels (negative values)
        for (const auto& trough : troughs) {
            if (!std::isnan(trough.level) && trough.level < 0.0) {
                trough_levels.push_back(trough.level);
            }
        }



        if (peak_levels.empty() && trough_levels.empty()) {
            py::dict result;
            result["is_valid"] = false;
            result["stdev_levels"] = py::list();
            return Result<py::dict>(result);
        }

        // Sort the levels for percentile calculation
        std::sort(peak_levels.begin(), peak_levels.end());
        std::sort(trough_levels.begin(), trough_levels.end());

        // Percentile levels we want to calculate
        std::vector<double> percentiles = {2.0, 5.0, 10.0, 20.0, 50.0, 80.0};

        py::list stdev_levels;

        // Calculate peak percentiles (higher percentiles = higher peak levels)
        if (!peak_levels.empty()) {
            for (double percentile : percentiles) {
                // For peaks: 98th percentile means 98% of peaks are below this level
                double target_percentile = 100.0 - percentile;  // Convert to "below" percentile
                size_t index = static_cast<size_t>((target_percentile / 100.0) * (peak_levels.size() - 1));
                index = std::min(index, peak_levels.size() - 1);

                double level_value = peak_levels[index];

                py::dict level_data;
                level_data["level_value"] = level_value;
                level_data["percentile_label"] = std::to_string(static_cast<int>(percentile)) + "%";
                level_data["multiplier"] = percentile;  // Use percentile as multiplier for color coding

                // Calculate label with 2 decimal places
                if (zero_percent_price > 0) {
                    double actual_price = zero_percent_price * (1 + level_value / 100);
                    std::ostringstream price_stream;
                    price_stream << std::fixed << std::setprecision(2) << actual_price;
                    level_data["label"] = std::to_string(static_cast<int>(percentile)) + "%: $" + price_stream.str();
                } else {
                    std::ostringstream value_stream;
                    value_stream << std::fixed << std::setprecision(2) << level_value;
                    level_data["label"] = std::to_string(static_cast<int>(percentile)) + "%: " + value_stream.str() + "%";
                }

                stdev_levels.append(level_data);
            }
        }

        // Calculate trough percentiles (lower percentiles = lower trough levels)
        if (!trough_levels.empty()) {
            for (double percentile : percentiles) {
                // For troughs: 5th percentile means 5% of troughs are below this level (95% above)
                size_t index = static_cast<size_t>((percentile / 100.0) * (trough_levels.size() - 1));
                index = std::min(index, trough_levels.size() - 1);

                double level_value = trough_levels[index];

                py::dict level_data;
                level_data["level_value"] = level_value;
                level_data["percentile_label"] = std::to_string(static_cast<int>(percentile)) + "%";
                level_data["multiplier"] = -percentile;  // Negative for troughs, use for color coding

                // Calculate label with 2 decimal places
                if (zero_percent_price > 0) {
                    double actual_price = zero_percent_price * (1 + level_value / 100);
                    std::ostringstream price_stream;
                    price_stream << std::fixed << std::setprecision(2) << actual_price;
                    level_data["label"] = std::to_string(static_cast<int>(percentile)) + "%: $" + price_stream.str();
                } else {
                    std::ostringstream value_stream;
                    value_stream << std::fixed << std::setprecision(2) << level_value;
                    level_data["label"] = std::to_string(static_cast<int>(percentile)) + "%: " + value_stream.str() + "%";
                }

                stdev_levels.append(level_data);
            }
        }

        py::dict result;
        result["is_valid"] = true;
        result["stdev_levels"] = stdev_levels;
        return Result<py::dict>(result);

    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("Exception: ") + e.what());
    }
}

/**
 * Calculate standard deviation infinity lines - pure C++ computation (DEPRECATED - use percentile version)
 */
Result<py::dict> calculate_stdev_infinity_lines(const py::dict& standard_deviations, double zero_percent_price) {
    try {
        double positive_std = standard_deviations["positive_std"].cast<double>();
        double negative_std = standard_deviations["negative_std"].cast<double>();
        int positive_count = standard_deviations["positive_count"].cast<int>();
        int negative_count = standard_deviations["negative_count"].cast<int>();

        if (positive_count < 1 && negative_count < 1) {
            py::dict result;
            result["is_valid"] = false;
            result["stdev_levels"] = py::list();
            return Result<py::dict>(result);
        }

        // Standard deviation multipliers
        std::vector<double> positive_multipliers = {2.326, 1.960, 1.6449, 1.2816, 0.6745, 0.2533};
        std::vector<double> negative_multipliers = {-0.2533, -0.6745, -1.2816, -1.6449, -1.960, -2.326};

        // Percentile labels
        std::map<double, std::string> multiplier_to_percentile = {
            {2.326, "2%"}, {1.960, "5%"}, {1.6449, "10%"}, {1.2816, "20%"}, {0.6745, "50%"}, {0.2533, "80%"},
            {-0.2533, "80%"}, {-0.6745, "50%"}, {-1.2816, "20%"}, {-1.6449, "10%"}, {-1.960, "5%"}, {-2.326, "2%"}
        };

        py::list stdev_levels;

        // Add positive levels
        if (positive_std > 0) {
            for (double multiplier : positive_multipliers) {
                double level_value = multiplier * positive_std;
                if (std::isfinite(level_value) && std::abs(level_value) < 1000 && level_value > 0) {
                    py::dict level_data;
                    level_data["level_value"] = level_value;
                    level_data["percentile_label"] = multiplier_to_percentile[multiplier];
                    level_data["multiplier"] = multiplier;

                    // Calculate label with 2 decimal places
                    if (zero_percent_price > 0) {
                        double actual_price = zero_percent_price * (1 + level_value / 100);
                        std::ostringstream price_stream;
                        price_stream << std::fixed << std::setprecision(2) << actual_price;
                        level_data["label"] = multiplier_to_percentile[multiplier] + ": $" + price_stream.str();
                    } else {
                        std::ostringstream value_stream;
                        value_stream << std::fixed << std::setprecision(2) << level_value;
                        level_data["label"] = multiplier_to_percentile[multiplier] + ": " + value_stream.str() + "%";
                    }

                    stdev_levels.append(level_data);
                }
            }
        }

        // Add negative levels
        if (negative_std > 0) {
            for (double multiplier : negative_multipliers) {
                double level_value = multiplier * negative_std;
                if (std::isfinite(level_value) && std::abs(level_value) < 1000 && level_value < 0) {
                    py::dict level_data;
                    level_data["level_value"] = level_value;
                    level_data["percentile_label"] = multiplier_to_percentile[multiplier];
                    level_data["multiplier"] = multiplier;

                    // Calculate label with 2 decimal places
                    if (zero_percent_price > 0) {
                        double actual_price = zero_percent_price * (1 + level_value / 100);
                        std::ostringstream price_stream;
                        price_stream << std::fixed << std::setprecision(2) << actual_price;
                        level_data["label"] = multiplier_to_percentile[multiplier] + ": $" + price_stream.str();
                    } else {
                        std::ostringstream value_stream;
                        value_stream << std::fixed << std::setprecision(2) << level_value;
                        level_data["label"] = multiplier_to_percentile[multiplier] + ": " + value_stream.str() + "%";
                    }

                    stdev_levels.append(level_data);
                }
            }
        }

        py::dict result;
        result["is_valid"] = true;
        result["stdev_levels"] = stdev_levels;

        return Result<py::dict>(result);
    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate percentage levels for bandwidth chart - pure C++ computation
 */
Result<py::dict> calculate_percentage_levels(const std::vector<double>& bandwidth_values) {
    try {
        if (bandwidth_values.empty()) {
            py::dict result;
            result["is_valid"] = false;
            result["levels"] = py::list();
            return Result<py::dict>(result);
        }

        // Find min and max of valid values
        std::vector<double> valid_values;
        for (double val : bandwidth_values) {
            if (std::isfinite(val)) {
                valid_values.push_back(val);
            }
        }

        if (valid_values.size() < 2) {
            py::dict result;
            result["is_valid"] = false;
            result["levels"] = py::list();
            return Result<py::dict>(result);
        }

        double min_val = *std::min_element(valid_values.begin(), valid_values.end());
        double max_val = *std::max_element(valid_values.begin(), valid_values.end());

        if (std::abs(max_val - min_val) < 0.001) {
            py::dict result;
            result["is_valid"] = false;
            result["levels"] = py::list();
            return Result<py::dict>(result);
        }

        // Calculate percentage levels
        std::vector<int> percentages = {10, 25, 50, 75, 90};
        py::list levels;

        for (int pct : percentages) {
            double level_value = min_val + (max_val - min_val) * (pct / 100.0);

            py::dict level_data;
            level_data["percentage"] = pct;
            level_data["level_value"] = level_value;
            levels.append(level_data);
        }

        py::dict result;
        result["is_valid"] = true;
        result["levels"] = levels;

        return Result<py::dict>(result);
    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate cycle position - pure C++ computation
 * Uses the same strength calculation logic as peak/trough detection but applied to every candle
 */
Result<std::string> calculate_cycle_position(int candle_index, const std::vector<VectorDirectionChange>& cycles) {
    try {
        if (cycles.empty() || candle_index >= static_cast<int>(cycles.size())) {
            return Result<std::string>::success_result("--");
        }

        const auto& current_cycle = cycles[candle_index];
        std::string cycle_type = current_cycle.cycle_type;

        // Calculate strength based on cycle length (same logic as peak/trough detection)
        // Find the start of the current cycle by looking backwards for a direction change
        int cycle_start_index = 0;
        for (int i = candle_index; i >= 0; --i) {
            if (i < static_cast<int>(cycles.size()) && cycles[i].direction_changed) {
                cycle_start_index = i;
                break;
            }
        }

        // Calculate cycle length (strength) - this is the same as the strength calculation
        int cycle_length = candle_index - cycle_start_index + 1;

        // Use cycle length as strength (same as peak/trough strength calculation)
        if (cycle_type == "bullish") {
            return Result<std::string>::success_result("H" + std::to_string(cycle_length));
        } else if (cycle_type == "bearish") {
            return Result<std::string>::success_result("L" + std::to_string(cycle_length));
        } else {
            return Result<std::string>::success_result("--");
        }
    } catch (const std::exception& e) {
        return Result<std::string>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate crosshair info lookup table - pure C++ computation
 */
Result<py::dict> calculate_crosshair_info_lookup(const py::list& rebased_data, const std::vector<VectorDirectionChange>& cycles) {
    try {
        py::dict crosshair_lookup;

        for (size_t candle_index = 0; candle_index < rebased_data.size(); ++candle_index) {
            // Calculate cycle position
            auto cycle_result = calculate_cycle_position(static_cast<int>(candle_index), cycles);
            std::string category = cycle_result.success ? cycle_result.data : "--";

            // Get actual data index from rebased data
            py::tuple candle_data = rebased_data[candle_index];
            int actual_data_index = candle_data.size() > 0 ? candle_data[0].cast<int>() : static_cast<int>(candle_index);

            py::dict info;
            info["candle_index"] = static_cast<int>(candle_index);
            info["category"] = category;
            info["actual_data_index"] = actual_data_index;

            crosshair_lookup[std::to_string(candle_index).c_str()] = info;
        }

        return Result<py::dict>(crosshair_lookup);
    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate predictive cycle lines - pure C++ computation
 */
Result<py::dict> calculate_predictive_cycle_lines(const std::vector<PeakTroughAnnotation>& peaks, const std::vector<PeakTroughAnnotation>& troughs,
                                                 const py::dict& strengths_data, const MeanLevelsResult& levels_data) {
    try {
        // Get required data
        // Extract strengths from dict properly
        py::list peak_strengths_list = strengths_data["peak_strengths"];
        py::list trough_strengths_list = strengths_data["trough_strengths"];

        std::vector<double> peak_strengths;
        std::vector<double> trough_strengths;

        for (auto item : peak_strengths_list) {
            peak_strengths.push_back(item.cast<double>());
        }

        for (auto item : trough_strengths_list) {
            trough_strengths.push_back(item.cast<double>());
        }

        if (peak_strengths.empty() || trough_strengths.empty()) {
            py::dict result;
            result["is_valid"] = false;
            result["line_segments"] = py::list();
            return Result<py::dict>(result);
        }

        // Extract levels from MeanLevelsResult object
        double mean_peak_level = levels_data.mean_peak_level;
        double mean_trough_level = levels_data.mean_trough_level;

        // Calculate mean strengths
        double mean_h_strength = std::accumulate(peak_strengths.begin(), peak_strengths.end(), 0.0) / peak_strengths.size();
        double mean_l_strength = std::accumulate(trough_strengths.begin(), trough_strengths.end(), 0.0) / trough_strengths.size();

        // Find all points and sort by x position
        std::vector<std::tuple<std::string, double, double>> all_points;

        for (const auto& peak : peaks) {
            if (peak.index >= 0 && std::isfinite(peak.level)) {
                all_points.emplace_back("peak", static_cast<double>(peak.index), peak.level);
            }
        }

        for (const auto& trough : troughs) {
            if (trough.index >= 0 && std::isfinite(trough.level)) {
                all_points.emplace_back("trough", static_cast<double>(trough.index), trough.level);
            }
        }

        if (all_points.empty()) {
            py::dict result;
            result["is_valid"] = false;
            result["line_segments"] = py::list();
            return Result<py::dict>(result);
        }

        // Sort by x position
        std::sort(all_points.begin(), all_points.end(),
                 [](const auto& a, const auto& b) { return std::get<1>(a) < std::get<1>(b); });

        std::string last_type = std::get<0>(all_points.back());

        // Determine starting point and next targets
        double current_x, current_y, next_target_y, next_distance;
        std::string next_type;

        if (last_type == "peak") {
            // Find latest trough
            std::vector<std::pair<double, double>> trough_points;
            for (const auto& point : all_points) {
                if (std::get<0>(point) == "trough") {
                    trough_points.emplace_back(std::get<1>(point), std::get<2>(point));
                }
            }

            if (trough_points.empty()) {
                py::dict result;
                result["is_valid"] = false;
                result["line_segments"] = py::list();
                return Result<py::dict>(result);
            }

            std::sort(trough_points.begin(), trough_points.end());
            current_x = trough_points.back().first;
            current_y = trough_points.back().second;
            next_target_y = mean_peak_level;
            next_distance = mean_h_strength;
            next_type = "peak";
        } else {
            // Find latest peak
            std::vector<std::pair<double, double>> peak_points;
            for (const auto& point : all_points) {
                if (std::get<0>(point) == "peak") {
                    peak_points.emplace_back(std::get<1>(point), std::get<2>(point));
                }
            }

            if (peak_points.empty()) {
                py::dict result;
                result["is_valid"] = false;
                result["line_segments"] = py::list();
                return Result<py::dict>(result);
            }

            std::sort(peak_points.begin(), peak_points.end());
            current_x = peak_points.back().first;
            current_y = peak_points.back().second;
            next_target_y = mean_trough_level;
            next_distance = mean_l_strength;
            next_type = "trough";
        }

        // Calculate line segments
        py::list line_segments;

        // First segment
        double next_x = current_x + next_distance;
        py::dict segment1;
        segment1["x1"] = current_x;
        segment1["y1"] = current_y;
        segment1["x2"] = next_x;
        segment1["y2"] = next_target_y;
        line_segments.append(segment1);

        // Second segment
        current_x = next_x;
        current_y = next_target_y;

        if (next_type == "peak") {
            next_target_y = mean_trough_level;
            next_distance = mean_l_strength;
        } else {
            next_target_y = mean_peak_level;
            next_distance = mean_h_strength;
        }

        double final_x = current_x + next_distance;
        py::dict segment2;
        segment2["x1"] = current_x;
        segment2["y1"] = current_y;
        segment2["x2"] = final_x;
        segment2["y2"] = next_target_y;
        line_segments.append(segment2);

        py::dict result;
        result["is_valid"] = true;
        result["line_segments"] = line_segments;

        return Result<py::dict>(result);
    } catch (const std::exception& e) {
        return Result<py::dict>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Calculate zero percent price - pure C++ computation
 */
Result<double> calculate_zero_percent_price(const py::list& rebased_data, const py::array_t<double>& closes) {
    try {
        // Check if we have data
        if (rebased_data.size() == 0 || closes.size() == 0) {
            if (closes.size() > 0) {
                auto closes_ptr = closes.unchecked<1>();
                return Result<double>(closes_ptr(0));
            } else {
                return Result<double>(0.0);
            }
        }

        // Get the last candle from rebased data
        py::object last_candle_obj = rebased_data[rebased_data.size() - 1];

        // Handle both tuple and list formats
        double percentage_close = 0.0;
        if (py::isinstance<py::tuple>(last_candle_obj)) {
            py::tuple last_candle = last_candle_obj.cast<py::tuple>();
            if (last_candle.size() < 5) {
                if (closes.size() > 0) {
                    auto closes_ptr = closes.unchecked<1>();
                    return Result<double>(closes_ptr(0));
                } else {
                    return Result<double>(0.0);
                }
            }
            percentage_close = last_candle[4].cast<double>();
        } else if (py::isinstance<py::list>(last_candle_obj)) {
            py::list last_candle = last_candle_obj.cast<py::list>();
            if (last_candle.size() < 5) {
                if (closes.size() > 0) {
                    auto closes_ptr = closes.unchecked<1>();
                    return Result<double>(closes_ptr(0));
                } else {
                    return Result<double>(0.0);
                }
            }
            percentage_close = last_candle[4].cast<double>();
        } else {
            // Fallback - return first close price
            if (closes.size() > 0) {
                auto closes_ptr = closes.unchecked<1>();
                return Result<double>(closes_ptr(0));
            } else {
                return Result<double>(0.0);
            }
        }

        // Get the real close price (latest) - handle numpy array
        double real_close_price = 0.0;
        if (closes.size() > 0) {
            auto closes_ptr = closes.unchecked<1>();
            real_close_price = closes_ptr(closes.size() - 1);
        } else {
            return Result<double>(0.0);
        }

        // Calculate 0% price: real_close_price / (1 + percentage_close / 100)
        double zero_percent_price = real_close_price / (1.0 + percentage_close / 100.0);

        return Result<double>(zero_percent_price);

    } catch (const std::exception& e) {
        return Result<double>(std::string("C++ error: ") + e.what());
    }
}

/**
 * Prepare projected OHLC table rows - pure C++ computation
 * Calculates projected OHLC data based on next-candle changes
 */
py::list prepare_projected_ohlc_table_rows(
    const py::list& actual_dates,
    const py::array_t<double>& opens,
    const py::array_t<double>& highs,
    const py::array_t<double>& lows,
    const py::array_t<double>& closes,
    const py::dict& crosshair_info_lookup
) {
    try {
        auto opens_buf = opens.request();
        auto highs_buf = highs.request();
        auto lows_buf = lows.request();
        auto closes_buf = closes.request();

        const double* opens_ptr = static_cast<const double*>(opens_buf.ptr);
        const double* highs_ptr = static_cast<const double*>(highs_buf.ptr);
        const double* lows_ptr = static_cast<const double*>(lows_buf.ptr);
        const double* closes_ptr = static_cast<const double*>(closes_buf.ptr);

        size_t data_length = highs_buf.size;

        // Removed artificial size limits; rely on actual memory capacity
        if (data_length > 1000000) {
            std::cout << "Very large projected OHLC dataset: " << data_length << " rows" << std::endl;
        }

        // Add safety limit for projected OHLC to prevent crashes
        if (data_length > 10000) {
            std::cout << "Dataset too large for projected OHLC processing: " << data_length << " rows (max: 10000)" << std::endl;
            throw std::runtime_error("Dataset too large for projected OHLC processing. Maximum size: 10000 rows");
        }

        // Add logging for large datasets and implement chunked processing
        if (data_length > 1000) {
            std::cout << "Processing large projected OHLC dataset: " << data_length << " rows" << std::endl;
            // Reset memory pool for large datasets to prevent memory exhaustion
            g_memory_pool.reset();
            g_memory_pool.log_memory_usage();
        }

        // Keep sequential processing; kernel is optimized for contiguous arrays

        if (data_length == 0) {
            std::cout << "Warning: Empty dataset for projected OHLC" << std::endl;
            return py::list();
        }

        // Pre-allocate result list with defensive memory management
        py::list result;
        try {
            // Reserve space to reduce reallocations
            result = py::list();
        } catch (const std::bad_alloc& e) {
            std::cerr << "Failed to allocate result list for projected OHLC" << std::endl;
            g_memory_pool.reset();
            throw;
        }

        // Get latest closing price for projections with bounds checking
        double latest_close = 0.0;
        try {
            if (data_length > 0) {
                latest_close = closes_ptr[data_length - 1];
            } else {
                throw std::runtime_error("No data available for latest close price");
            }
        } catch (const std::exception& e) {
            std::cerr << "Error getting latest close price: " << e.what() << std::endl;
            throw;
        }

        // Helper function to get weekday from date string (YYYY-MM-DD format)
        auto get_weekday = [](const std::string& date_str) -> std::string {
            try {
                // Parse date string (YYYY-MM-DD)
                int year = std::stoi(date_str.substr(0, 4));
                int month = std::stoi(date_str.substr(5, 2));
                int day = std::stoi(date_str.substr(8, 2));

                // Zeller's congruence algorithm to get day of week
                if (month < 3) {
                    month += 12;
                    year--;
                }
                int k = year % 100;
                int j = year / 100;
                int h = (day + (13 * (month + 1)) / 5 + k + k / 4 + j / 4 - 2 * j) % 7;

                // Convert to weekday names (h=0 is Saturday)
                const std::string weekdays[] = {"Saturday", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday"};
                return weekdays[h];
            } catch (...) {
                return "Unknown";
            }
        };

        // Process data with enhanced error handling and memory management
        size_t processed_rows = 0;
        size_t failed_rows = 0;

        // Pre-allocate buffer for string formatting to reduce allocations
        char format_buffer[64];

        for (size_t i = 0; i < data_length; ++i) {  // Include all rows, including last row
            try {
                // For large datasets, reset memory pool very aggressively to prevent crashes
                if (data_length > 1000 && i % 50 == 0) {
                    g_memory_pool.reset();
                }

                // Check for memory pressure much more frequently for projected OHLC
                if (i % 100 == 0 && i > 0) {
                    // Log progress for large datasets
                    if (data_length > 10000 && i % 1000 == 0) {
                        std::cout << "Processed " << i << " of " << data_length << " rows..." << std::endl;
                    }
                    if (data_length > 1000 && i % 1000 == 0) {
                        std::cout << "Memory pool reset at row " << i << " (projected OHLC)" << std::endl;
                    }
                }

                py::list row;

            // Date
            std::string date_str = py::str(actual_dates[i]);
            row.append(date_str);

            // Weekday
            std::string weekday = get_weekday(date_str);
            row.append(weekday);

            // Category (extract from crosshair_info_lookup)
            std::string category = "";
            std::string lookup_key = std::to_string(i);
            if (crosshair_info_lookup.contains(lookup_key)) {
                py::dict info = crosshair_info_lookup[lookup_key.c_str()];
                if (info.contains("category")) {
                    std::string cat = py::str(info["category"]);
                    // Remove arrow symbol if present
                    if (cat.find("⇨") != std::string::npos) {
                        cat = cat.substr(0, cat.find("⇨"));
                    }
                    category = cat;
                }
            }
            row.append(category);



            if (i < data_length - 1) {
                // For all rows except the last one, calculate projected OHLC values
                double current_open = opens_ptr[i];
                double current_close = closes_ptr[i];
                double next_open = opens_ptr[i + 1];
                double next_high = highs_ptr[i + 1];
                double next_low = lows_ptr[i + 1];
                double next_close = closes_ptr[i + 1];

                // $Change High: next row high - current row close
                double dollar_change_high = next_high - current_close;

                // $Change Low: next row low - current row close
                double dollar_change_low = next_low - current_close;

                // %Change High: ($change high/current row close) * 100
                double percent_change_high = (dollar_change_high / current_close) * 100.0;

                // %Change Low: ($change low/current row close) * 100
                double percent_change_low = (dollar_change_low / current_close) * 100.0;

                // $Projected High Change: %change high * latest closing price / 100
                double projected_high_change = (percent_change_high * latest_close) / 100.0;

                // $Projected Low Change: %change low * latest closing price / 100
                double projected_low_change = (percent_change_low * latest_close) / 100.0;

                // Projected High: latest closing price + $projected high change
                double projected_high = latest_close + projected_high_change;

                // Projected Low: latest closing price + $projected low change
                double projected_low = latest_close + projected_low_change;

                // Open ratio: next row open / current row open
                double open_ratio = (current_open != 0.0) ? (next_open / current_open) : 0.0;

                // Close ratio: next row close / current row close
                double close_ratio = (current_close != 0.0) ? (next_close / current_close) : 0.0;

                // Format and append values using pre-allocated buffer to reduce memory allocations

                // $Change High
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", dollar_change_high);
                row.append(std::string(format_buffer));

                // $Change Low
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", dollar_change_low);
                row.append(std::string(format_buffer));

                // %Change High
                snprintf(format_buffer, sizeof(format_buffer), "%.2f%%", percent_change_high);
                row.append(std::string(format_buffer));

                // %Change Low
                snprintf(format_buffer, sizeof(format_buffer), "%.2f%%", percent_change_low);
                row.append(std::string(format_buffer));

                // $Projected High Change
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", projected_high_change);
                row.append(std::string(format_buffer));

                // $Projected Low Change
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", projected_low_change);
                row.append(std::string(format_buffer));

                // Projected High
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", projected_high);
                row.append(std::string(format_buffer));

                // Projected Low
                snprintf(format_buffer, sizeof(format_buffer), "%.4f", projected_low);
                row.append(std::string(format_buffer));

                // Open ratio
                snprintf(format_buffer, sizeof(format_buffer), "%.6f", open_ratio);
                row.append(std::string(format_buffer));

                // Close ratio
                snprintf(format_buffer, sizeof(format_buffer), "%.6f", close_ratio);
                row.append(std::string(format_buffer));
            } else {
                // For the last row, add empty values (no next row to calculate from)
                row.append("");  // $Change High
                row.append("");  // $Change Low
                row.append("");  // %Change High
                row.append("");  // %Change Low
                row.append("");  // $Projected High Change
                row.append("");  // $Projected Low Change
                row.append("");  // Projected High
                row.append("");  // Projected Low
                row.append("");  // Open ratio
                row.append("");  // Close ratio
            }

            // Add color columns with proper formatting (same logic as regular OHLC table)
            std::string bg_color = "";
            std::string fg_color = "#FFFFFF";  // Default white text for dark theme

            if (!category.empty()) {
                if (category[0] == 'H') {  // Peak - Green
                    bg_color = "#1B4D3E";  // Dark green background
                    fg_color = "#FFFFFF";  // White text
                } else if (category[0] == 'L') {  // Trough - Red
                    bg_color = "#4D1B1B";  // Dark red background
                    fg_color = "#FFFFFF";  // White text
                }
            }

            row.append(bg_color);  // bg_color
            row.append(fg_color);  // fg_color

            try {
                result.append(row);
                processed_rows++;
            } catch (const std::bad_alloc& e) {
                std::cerr << "Memory allocation failed when appending row " << i << " to result. Forcing memory pool reset." << std::endl;
                g_memory_pool.reset();
                failed_rows++;
                // Try to continue after memory reset
                try {
                    result.append(row);
                    processed_rows++;
                } catch (...) {
                    std::cerr << "Failed to append row " << i << " even after memory reset. Skipping row." << std::endl;
                    continue;
                }
            }

            } catch (const std::bad_alloc& e) {
                std::cerr << "Memory allocation failed at row " << i << " in projected OHLC. Forcing memory reset and continuing." << std::endl;
                g_memory_pool.reset();
                failed_rows++;
                continue;  // Continue processing after memory reset
            } catch (const std::exception& e) {
                std::cerr << "Error processing row " << i << " in projected OHLC: " << e.what() << std::endl;
                failed_rows++;
                // Continue processing other rows instead of failing completely
                continue;
            } catch (...) {
                std::cerr << "Unknown error processing row " << i << " in projected OHLC" << std::endl;
                failed_rows++;
                // Continue processing other rows instead of failing completely
                continue;
            }
        }

        // Log processing statistics
        if (failed_rows > 0) {
            std::cout << "Projected OHLC processing completed: " << processed_rows << " successful, "
                      << failed_rows << " failed out of " << data_length << " total rows" << std::endl;
        }



        return result;

    } catch (const std::exception& e) {
        throw std::runtime_error("Error in prepare_projected_ohlc_table_rows: " + std::string(e.what()));
    }
}

/**
 * Extract highs data from filtered high data for volatility calculations
 * Pure C++ computation for zero-computation UI
 */
py::dict extract_highs_data_cpp(const py::list& filtered_high_data) {
    try {
        std::vector<double> all_highs;
        double max_high = std::numeric_limits<double>::lowest();
        bool has_max = false;

        for (size_t i = 0; i < filtered_high_data.size(); ++i) {
            py::list row = filtered_high_data[i];

            if (row.size() > 6) {  // Projected High is at index 6
                try {
                    py::object projected_high_obj = row[6];
                    std::string projected_high_str = py::str(projected_high_obj);

                    if (!projected_high_str.empty() && projected_high_str != "None" && projected_high_str != "") {
                        double projected_high = std::stod(projected_high_str);
                        all_highs.push_back(projected_high);

                        if (!has_max || projected_high > max_high) {
                            max_high = projected_high;
                            has_max = true;
                        }
                    }
                } catch (const std::exception& e) {
                    // Skip invalid entries
                    continue;
                }
            }
        }

        py::dict result;
        py::dict data;

        // Convert to Python list
        py::list py_all_highs;
        for (double high : all_highs) {
            py_all_highs.append(high);
        }

        data["all_highs"] = py_all_highs;
        data["max_high"] = has_max ? py::cast(max_high) : py::none();
        data["has_max"] = has_max;

        result["success"] = true;
        result["data"] = data;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in extract_highs_data_cpp: ") + e.what();
        return result;
    }
}

/**
 * Extract lows data from filtered low data for volatility calculations
 * Pure C++ computation for zero-computation UI
 */
py::dict extract_lows_data_cpp(const py::list& filtered_low_data) {
    try {
        std::vector<double> all_lows;
        double max_low = std::numeric_limits<double>::max();  // For lows, we want the minimum value
        bool has_max = false;

        for (size_t i = 0; i < filtered_low_data.size(); ++i) {
            py::list row = filtered_low_data[i];

            if (row.size() > 6) {  // Projected Low is at index 6
                try {
                    py::object projected_low_obj = row[6];
                    std::string projected_low_str = py::str(projected_low_obj);

                    if (!projected_low_str.empty() && projected_low_str != "None" && projected_low_str != "") {
                        double projected_low = std::stod(projected_low_str);
                        all_lows.push_back(projected_low);

                        if (!has_max || projected_low < max_low) {  // For lows, we want the minimum
                            max_low = projected_low;
                            has_max = true;
                        }
                    }
                } catch (const std::exception& e) {
                    // Skip invalid entries
                    continue;
                }
            }
        }

        py::dict result;
        py::dict data;

        // Convert to Python list
        py::list py_all_lows;
        for (double low : all_lows) {
            py_all_lows.append(low);
        }

        data["all_lows"] = py_all_lows;
        data["max_low"] = has_max ? py::cast(max_low) : py::none();
        data["has_max"] = has_max;

        result["success"] = true;
        result["data"] = data;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in extract_lows_data_cpp: ") + e.what();
        return result;
    }
}

/**
 * Calculate statistical values for a list of values
 * Pure C++ computation for zero-computation UI
 */
py::dict calculate_statistics_cpp(const py::list& values) {
    try {
        std::vector<double> data;

        // Convert Python list to C++ vector
        for (size_t i = 0; i < values.size(); ++i) {
            try {
                double value = py::cast<double>(values[i]);
                data.push_back(value);
            } catch (const std::exception& e) {
                // Skip invalid values
                continue;
            }
        }

        if (data.empty()) {
            py::dict result;
            result["success"] = false;
            result["error_message"] = "No valid data points found";
            return result;
        }

        // Calculate statistics
        double sum = std::accumulate(data.begin(), data.end(), 0.0);
        double mean = sum / data.size();

        double variance = 0.0;
        for (double value : data) {
            variance += (value - mean) * (value - mean);
        }
        variance /= data.size();
        double std_dev = std::sqrt(variance);

        auto minmax = std::minmax_element(data.begin(), data.end());
        double min_val = *minmax.first;
        double max_val = *minmax.second;

        // Calculate median
        std::vector<double> sorted_data = data;
        std::sort(sorted_data.begin(), sorted_data.end());
        double median;
        size_t n = sorted_data.size();
        if (n % 2 == 0) {
            median = (sorted_data[n/2 - 1] + sorted_data[n/2]) / 2.0;
        } else {
            median = sorted_data[n/2];
        }

        // Calculate max_avg and min_avg for volatility chart compatibility
        double max_avg = 0.0;  // Average of highest 50% values
        double min_avg = 0.0;  // Average of lowest 50% values

        if (n >= 2) {
            size_t half_size = std::max(static_cast<size_t>(1), n / 2);

            // max_avg: average of highest 50% values (top half of sorted data)
            double max_sum = 0.0;
            for (size_t i = n - half_size; i < n; ++i) {
                max_sum += sorted_data[i];
            }
            max_avg = max_sum / half_size;

            // min_avg: average of lowest 50% values (bottom half of sorted data)
            double min_sum = 0.0;
            for (size_t i = 0; i < half_size; ++i) {
                min_sum += sorted_data[i];
            }
            min_avg = min_sum / half_size;
        } else {
            // For single value, both averages are the same
            max_avg = min_avg = mean;
        }

        py::dict result;
        py::dict stats;

        stats["count"] = static_cast<int>(data.size());
        stats["mean"] = mean;
        stats["average"] = mean;  // Alias for volatility chart compatibility
        stats["median"] = median;
        stats["std_dev"] = std_dev;
        stats["variance"] = variance;
        stats["min"] = min_val;
        stats["max"] = max_val;
        stats["range"] = max_val - min_val;
        stats["max_avg"] = max_avg;  // Average of highest 50% values
        stats["min_avg"] = min_avg;  // Average of lowest 50% values

        result["success"] = true;
        result["data"] = stats;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in calculate_statistics_cpp: ") + e.what();
        return result;
    }
}

/**
 * Calculate apex values and derived metrics for volatility calculations
 * Pure C++ computation for zero-computation UI
 */
py::dict calculate_apex_values_cpp(double max_high, double max_low) {
    try {
        py::dict result;
        py::dict data;

        // Calculate apex as midpoint between max_high and max_low
        double apex = (max_high + max_low) / 2.0;

        // Calculate apex_high_mean (midpoint between apex and max_high)
        double apex_high_mean = (apex + max_high) / 2.0;

        // Calculate apex_low_mean (midpoint between apex and max_low)
        double apex_low_mean = (apex + max_low) / 2.0;

        data["apex"] = apex;
        data["apex_high_mean"] = apex_high_mean;
        data["apex_low_mean"] = apex_low_mean;

        result["success"] = true;
        result["data"] = data;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in calculate_apex_values_cpp: ") + e.what();
        return result;
    }
}

/**
 * Calculate axis limits with padding for volatility charts
 * Pure C++ computation for zero-computation UI
 */
py::dict calculate_axis_limits_cpp(double max_high, double max_low) {
    try {
        py::dict result;
        py::dict data;

        // Calculate range and padding
        double range = max_high - max_low;
        double padding = range * 0.1;  // 10% padding

        // Calculate axis limits
        double y_min = max_low - padding;
        double y_max = max_high + padding;

        // Fixed x-axis limits for volatility chart
        double x_min = -4.0;
        double x_max = 4.0;

        data["x_min"] = x_min;
        data["x_max"] = x_max;
        data["y_min"] = y_min;
        data["y_max"] = y_max;

        result["success"] = true;
        result["data"] = data;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in calculate_axis_limits_cpp: ") + e.what();
        return result;
    }
}

/**
 * Calculate title position at 7.5% above max_high for volatility charts
 * Pure C++ computation for zero-computation UI
 */
py::dict calculate_title_position_cpp(double max_high, double max_low) {
    try {
        py::dict result;
        py::dict data;

        // Calculate range for relative positioning
        double range = max_high - max_low;

        // Position title at 7.5% above max_high
        double title_y = max_high + (range * 0.075);

        // Fixed x position for title
        double title_x = -3.75;

        data["x"] = title_x;
        data["y"] = title_y;

        result["success"] = true;
        result["data"] = data;
        return result;

    } catch (const std::exception& e) {
        py::dict result;
        result["success"] = false;
        result["error_message"] = std::string("Error in calculate_title_position_cpp: ") + e.what();
        return result;
    }
}

/**
 * Extract and format volatility statistics high/low data from projected OHLC rows
 * Pure C++ computation for zero-computation UI
 */
py::dict extract_volatility_statistics_data(
    const py::list& projected_ohlc_rows
) {
    try {
        py::list high_data;
        py::list low_data;

        for (size_t i = 0; i < projected_ohlc_rows.size(); ++i) {
            py::list row = projected_ohlc_rows[i];

            if (row.size() >= 11) {  // Ensure we have all required columns
                // Extract high data with proper column mapping
                // Projected OHLC structure: Date(0), Weekday(1), Category(2), $Change High(3), $Change Low(4), %Change High(5), %Change Low(6), $Projected High Change(7), $Projected Low Change(8), Projected High(9), Projected Low(10)
                // Output structure: Index, Weekday, Category, $Change High, %Change High, $Projected High Change, Projected High
                py::list high_row;
                high_row.append(static_cast<int>(i));  // Index: Original row index from main data table
                high_row.append(row[1]);  // Weekday
                high_row.append(row[2]);  // Category
                high_row.append(row[3]);  // $Change High
                high_row.append(row[5]);  // %Change High
                high_row.append(row[7]);  // $Projected High Change

                // Format Projected High (column 9) to 2 decimal places
                try {
                    // Check if the value is a number first
                    if (py::isinstance<py::float_>(row[9]) || py::isinstance<py::int_>(row[9])) {
                        double projected_high = py::cast<double>(row[9]);
                        char buffer[32];
                        snprintf(buffer, sizeof(buffer), "%.2f", projected_high);
                        high_row.append(std::string(buffer));
                    } else {
                        // Try string conversion with better error handling
                        try {
                            std::string projected_high_str = py::cast<std::string>(row[9]);
                            if (!projected_high_str.empty() && projected_high_str != "None") {
                                double projected_high = std::stod(projected_high_str);
                                char buffer[32];
                                snprintf(buffer, sizeof(buffer), "%.2f", projected_high);
                                high_row.append(std::string(buffer));
                            } else {
                                high_row.append(projected_high_str);
                            }
                        } catch (const std::exception& e) {
                            // Log the error and use a default value
                            std::cerr << "Error converting projected high value: " << e.what() << std::endl;
                            high_row.append("0.00");  // Default value
                        }
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Error formatting projected high: " << e.what() << std::endl;
                    high_row.append("0.00");  // Default value
                }

                high_data.append(high_row);

                // Extract low data with proper column mapping
                // Output structure: Index, Weekday, Category, $Change Low, %Change Low, $Projected Low Change, Projected Low
                py::list low_row;
                low_row.append(static_cast<int>(i));  // Index: Original row index from main data table
                low_row.append(row[1]);  // Weekday
                low_row.append(row[2]);  // Category
                low_row.append(row[4]);  // $Change Low
                low_row.append(row[6]);  // %Change Low
                low_row.append(row[8]);  // $Projected Low Change

                // Format Projected Low (column 10) to 2 decimal places
                try {
                    // Check if the value is a number first
                    if (py::isinstance<py::float_>(row[10]) || py::isinstance<py::int_>(row[10])) {
                        double projected_low = py::cast<double>(row[10]);
                        char buffer[32];
                        snprintf(buffer, sizeof(buffer), "%.2f", projected_low);
                        low_row.append(std::string(buffer));
                    } else {
                        // Try string conversion with better error handling
                        try {
                            std::string projected_low_str = py::cast<std::string>(row[10]);
                            if (!projected_low_str.empty() && projected_low_str != "None") {
                                double projected_low = std::stod(projected_low_str);
                                char buffer[32];
                                snprintf(buffer, sizeof(buffer), "%.2f", projected_low);
                                low_row.append(std::string(buffer));
                            } else {
                                low_row.append(projected_low_str);
                            }
                        } catch (const std::exception& e) {
                            // Log the error and use a default value
                            std::cerr << "Error converting projected low value: " << e.what() << std::endl;
                            low_row.append("0.00");  // Default value
                        }
                    }
                } catch (const std::exception& e) {
                    std::cerr << "Error formatting projected low: " << e.what() << std::endl;
                    low_row.append("0.00");  // Default value
                }

                low_data.append(low_row);
            }
        }

        py::dict result;
        result["high_data"] = high_data;
        result["low_data"] = low_data;
        return result;

    } catch (const std::exception& e) {
        throw std::runtime_error("Error in extract_volatility_statistics_data: " + std::string(e.what()));
    }
}

/**
 * Apply volatility statistics filtering - pure C++ computation
 * Filters high/low data based on filter type and reference data
 */
py::dict apply_volatility_statistics_filter(
    const py::list& high_data,
    const py::list& low_data,
    int filter_type  // 0 = H/L Matching, 1 = Weekday Matching, -1 = No filter
) {
    try {
        py::list filtered_high;
        py::list filtered_low;

        if (filter_type == -1 || high_data.size() == 0 || low_data.size() == 0) {
            // No filter or no data, return original data
            filtered_high = high_data;
            filtered_low = low_data;
        } else {
            // Get the last row data for comparison
            py::list last_high_row = high_data[high_data.size() - 1];
            py::list last_low_row = low_data[low_data.size() - 1];

            if (filter_type == 0) {  // H/L Matching - filter by category (column 2)
                if (last_high_row.size() > 2) {
                    std::string last_category = py::str(last_high_row[2]).cast<std::string>();

                    // Filter high data
                    for (size_t i = 0; i < high_data.size(); ++i) {
                        py::list row = high_data[i];
                        if (row.size() > 2) {
                            std::string row_category = py::str(row[2]).cast<std::string>();
                            if (row_category == last_category) {
                                filtered_high.append(row);
                            }
                        }
                    }

                    // Filter low data
                    for (size_t i = 0; i < low_data.size(); ++i) {
                        py::list row = low_data[i];
                        if (row.size() > 2) {
                            std::string row_category = py::str(row[2]).cast<std::string>();
                            if (row_category == last_category) {
                                filtered_low.append(row);
                            }
                        }
                    }
                } else {
                    filtered_high = high_data;
                    filtered_low = low_data;
                }
            } else if (filter_type == 1) {  // Weekday Matching - filter by weekday (column 1)
                if (last_high_row.size() > 1) {
                    std::string last_weekday = py::str(last_high_row[1]).cast<std::string>();

                    // Filter high data
                    for (size_t i = 0; i < high_data.size(); ++i) {
                        py::list row = high_data[i];
                        if (row.size() > 1) {
                            std::string row_weekday = py::str(row[1]).cast<std::string>();
                            if (row_weekday == last_weekday) {
                                filtered_high.append(row);
                            }
                        }
                    }

                    // Filter low data
                    for (size_t i = 0; i < low_data.size(); ++i) {
                        py::list row = low_data[i];
                        if (row.size() > 1) {
                            std::string row_weekday = py::str(row[1]).cast<std::string>();
                            if (row_weekday == last_weekday) {
                                filtered_low.append(row);
                            }
                        }
                    }
                } else {
                    filtered_high = high_data;
                    filtered_low = low_data;
                }
            } else {
                // Unknown filter type, return original data
                filtered_high = high_data;
                filtered_low = low_data;
            }
        }

        py::dict result;
        result["filtered_high"] = filtered_high;
        result["filtered_low"] = filtered_low;
        return result;

    } catch (const std::exception& e) {
        throw std::runtime_error("Error in apply_volatility_statistics_filter: " + std::string(e.what()));
    }
}

/**
 * Prepare data table rows with all formatting - pure C++ computation
 * Moves ALL calculations from UI to backend for zero-computation UI
 * Includes peak/trough detection using the same algorithm as market_odds.py
 */
py::list prepare_data_table_rows(
    const py::array_t<double>& timestamps,
    const py::list& actual_dates,
    const py::array_t<double>& opens,
    const py::array_t<double>& highs,
    const py::array_t<double>& lows,
    const py::array_t<double>& closes,
    const py::array_t<double>& volumes,
    const py::dict& crosshair_info_lookup,
    int length,
    const py::list& rebased_opens,
    const py::list& rebased_highs,
    const py::list& rebased_lows,
    const py::list& rebased_closes
) {
    try {
        py::list table_rows;

        // Calculate skip count (moved from UI)
        int skip_count = length + 1;

        // Get array sizes
        size_t data_length = std::min({
            timestamps.size(), opens.size(), highs.size(),
            lows.size(), closes.size(), volumes.size()
        });

        // Log dataset size for debugging large dataset issues
        if (data_length > 1000) {
            std::cout << "Processing large dataset: " << data_length << " rows" << std::endl;
        }

        // Removed artificial size limits; only log very large inputs
        if (data_length > 1000000) {
            std::cout << "Processing extremely large dataset: " << data_length << " rows" << std::endl;
        }

        // Get array pointers for efficient access
        auto timestamps_ptr = timestamps.unchecked<1>();
        auto opens_ptr = opens.unchecked<1>();
        auto highs_ptr = highs.unchecked<1>();
        auto lows_ptr = lows.unchecked<1>();
        auto closes_ptr = closes.unchecked<1>();
        auto volumes_ptr = volumes.unchecked<1>();

        // Calculate peak/trough detection using same algorithm as market_odds.py with rebased data
        std::vector<std::string> peak_trough_labels(data_length, "");

        // Convert rebased data to vectors for peak/trough calculation (use percentage data like market_odds.py)
        std::vector<int> indices;
        std::vector<double> rebased_opens_vec, rebased_highs_vec, rebased_lows_vec, rebased_closes_vec;

        size_t rebased_data_length = std::min({
            static_cast<size_t>(rebased_opens.size()),
            static_cast<size_t>(rebased_highs.size()),
            static_cast<size_t>(rebased_lows.size()),
            static_cast<size_t>(rebased_closes.size()),
            data_length
        });

        for (size_t i = 0; i < rebased_data_length; ++i) {
            indices.push_back(static_cast<int>(i));
            rebased_opens_vec.push_back(py::cast<double>(rebased_opens[i]));
            rebased_highs_vec.push_back(py::cast<double>(rebased_highs[i]));
            rebased_lows_vec.push_back(py::cast<double>(rebased_lows[i]));
            rebased_closes_vec.push_back(py::cast<double>(rebased_closes[i]));
        }

        // Calculate peaks and troughs using rebased percentage data (same as market_odds.py)
        auto peak_trough_result = calculate_peaks_and_troughs(indices, rebased_opens_vec, rebased_highs_vec, rebased_lows_vec, rebased_closes_vec);

        if (peak_trough_result.success) {
            auto [peaks, troughs] = peak_trough_result.data;

            // Mark peaks in the labels array (skip first length+1 candles like strength labels)
            for (const auto& peak : peaks) {
                if (peak.index >= 0 && peak.index < static_cast<int>(data_length) && peak.index >= skip_count) {
                    peak_trough_labels[peak.index] = "Peak";
                }
            }

            // Mark troughs in the labels array (skip first length+1 candles like strength labels)
            for (const auto& trough : troughs) {
                if (trough.index >= 0 && trough.index < static_cast<int>(data_length) && trough.index >= skip_count) {
                    peak_trough_labels[trough.index] = "Trough";
                }
            }
        }

        // Process each row
        for (size_t i = 0; i < data_length; ++i) {
            py::list row;

            // Format date (moved from UI)
            std::string date_str;
            if (i < actual_dates.size()) {
                try {
                    std::string date_input = py::str(actual_dates[i]);
                    if (date_input.length() >= 10) {
                        date_str = date_input.substr(0, 10);  // YYYY-MM-DD
                    } else {
                        date_str = date_input;
                    }
                } catch (...) {
                    date_str = "N/A";
                }
            } else {
                date_str = "N/A";
            }

            // Get strength label (moved from UI)
            std::string strength_label = "";
            if (static_cast<int>(i) >= skip_count) {
                std::string key = std::to_string(i);
                if (crosshair_info_lookup.contains(key)) {
                    py::dict info = crosshair_info_lookup[key.c_str()];
                    if (info.contains("category")) {
                        std::string category = py::str(info["category"]);
                        if (!category.empty() && category != "--") {
                            strength_label = category;
                        }
                    }
                }
            }

            // Add formatted data to row (all formatting moved from UI)
            row.append(date_str);
            row.append(strength_label);

            // Format numeric values with proper precision (moved from UI)
            char buffer[32];
            snprintf(buffer, sizeof(buffer), "%.4f", opens_ptr(i));
            row.append(std::string(buffer));

            snprintf(buffer, sizeof(buffer), "%.4f", highs_ptr(i));
            row.append(std::string(buffer));

            snprintf(buffer, sizeof(buffer), "%.4f", lows_ptr(i));
            row.append(std::string(buffer));

            snprintf(buffer, sizeof(buffer), "%.4f", closes_ptr(i));
            row.append(std::string(buffer));

            // Format volume with thousand separators (moved from UI)
            int volume = static_cast<int>(volumes_ptr(i));
            std::string volume_str = std::to_string(volume);
            std::string formatted_volume = "";
            int count = 0;
            for (int j = static_cast<int>(volume_str.length()) - 1; j >= 0; --j) {
                if (count > 0 && count % 3 == 0) {
                    formatted_volume = "," + formatted_volume;
                }
                formatted_volume = volume_str[j] + formatted_volume;
                count++;
            }
            row.append(formatted_volume);

            // Add peak/trough identification (moved from UI)
            row.append(peak_trough_labels[i]);

            // Pre-compute colors (moved from UI)
            std::string bg_color = "";
            std::string fg_color = "";
            if (!strength_label.empty()) {
                if (strength_label[0] == 'H') {  // Peak - Green
                    bg_color = "#1B4D3E";  // Dark green background
                    fg_color = "#FFFFFF";  // White text
                } else if (strength_label[0] == 'L') {  // Trough - Red
                    bg_color = "#4D1B1B";  // Dark red background
                    fg_color = "#FFFFFF";  // White text
                }
            }
            row.append(bg_color);
            row.append(fg_color);

            table_rows.append(row);
        }

        return table_rows;

    } catch (const std::exception& e) {
        // Log the actual error for debugging large dataset issues
        std::cerr << "Error in prepare_data_table_rows: " << e.what() << std::endl;

        // Return empty list on error - UI will handle gracefully
        py::list empty_list;
        return empty_list;
    } catch (...) {
        // Log unknown errors for debugging large dataset issues
        std::cerr << "Unknown error in prepare_data_table_rows" << std::endl;

        // Return empty list on error - UI will handle gracefully
        py::list empty_list;
        return empty_list;
    }
}

// Python bindings with enterprise exception handling
PYBIND11_MODULE(indicators, m) {
    m.doc() = "High-performance technical indicators with enterprise-grade error handling";

    // Register custom exception types for better Python integration
    py::register_exception<exception_handling::ComputationError>(m, "ComputationError");
    py::register_exception<exception_handling::InvalidParameterError>(m, "InvalidParameterError");
    py::register_exception<exception_handling::MemoryAllocationError>(m, "MemoryAllocationError");
    py::register_exception<exception_handling::ThreadSafetyError>(m, "ThreadSafetyError");

    // Bind Result template for double vectors
    py::class_<Result<std::vector<double>>>(m, "VectorResult")
        .def_readonly("data", &Result<std::vector<double>>::data)
        .def_readonly("success", &Result<std::vector<double>>::success)
        .def_readonly("error_message", &Result<std::vector<double>>::error_message);

    // Bind Result template for int vectors
    py::class_<Result<std::vector<int>>>(m, "IntVectorResult")
        .def_readonly("data", &Result<std::vector<int>>::data)
        .def_readonly("success", &Result<std::vector<int>>::success)
        .def_readonly("error_message", &Result<std::vector<int>>::error_message);

    // Bind Result template for int
    py::class_<Result<int>>(m, "IntResult")
        .def_readonly("data", &Result<int>::data)
        .def_readonly("success", &Result<int>::success)
        .def_readonly("error_message", &Result<int>::error_message);

    // Bind Result template for double
    py::class_<Result<double>>(m, "DoubleResult")
        .def_readonly("data", &Result<double>::data)
        .def_readonly("success", &Result<double>::success)
        .def_readonly("error_message", &Result<double>::error_message);

    // Bind DonchianStripSegment structure
    py::class_<DonchianStripSegment>(m, "DonchianStripSegment")
        .def_readonly("start_idx", &DonchianStripSegment::start_idx)
        .def_readonly("end_idx", &DonchianStripSegment::end_idx)
        .def_readonly("color", &DonchianStripSegment::color);

    // Bind Result template for DonchianStripSegment vectors
    py::class_<Result<std::vector<DonchianStripSegment>>>(m, "DonchianStripResult")
        .def_readonly("data", &Result<std::vector<DonchianStripSegment>>::data)
        .def_readonly("success", &Result<std::vector<DonchianStripSegment>>::success)
        .def_readonly("error_message", &Result<std::vector<DonchianStripSegment>>::error_message);

    // Bind VectorDirectionChange structure
    py::class_<VectorDirectionChange>(m, "VectorDirectionChange")
        .def_readonly("cycle_type", &VectorDirectionChange::cycle_type)
        .def_readonly("direction_changed", &VectorDirectionChange::direction_changed)
        .def_readonly("pivot_price", &VectorDirectionChange::pivot_price)
        .def_readonly("current_direction", &VectorDirectionChange::current_direction);

    // Bind RebasedOHLC structure
    py::class_<RebasedOHLC>(m, "RebasedOHLC")
        .def_readonly("index", &RebasedOHLC::index)
        .def_readonly("open_pct", &RebasedOHLC::open_pct)
        .def_readonly("high_pct", &RebasedOHLC::high_pct)
        .def_readonly("low_pct", &RebasedOHLC::low_pct)
        .def_readonly("close_pct", &RebasedOHLC::close_pct);

    // Bind PeakTroughAnnotation structure
    py::class_<PeakTroughAnnotation>(m, "PeakTroughAnnotation")
        .def_readonly("index", &PeakTroughAnnotation::index)
        .def_readonly("position", &PeakTroughAnnotation::position)
        .def_readonly("type", &PeakTroughAnnotation::type)
        .def_readonly("label", &PeakTroughAnnotation::label)
        .def_readonly("level", &PeakTroughAnnotation::level)
        .def_readonly("ray_end", &PeakTroughAnnotation::ray_end)
        .def_readonly("is_closed", &PeakTroughAnnotation::is_closed)
        .def_readonly("cycle_type", &PeakTroughAnnotation::cycle_type);

    // Bind Result template for VectorDirectionChange vectors
    py::class_<Result<std::vector<VectorDirectionChange>>>(m, "VectorDirectionChangeResult")
        .def_readonly("data", &Result<std::vector<VectorDirectionChange>>::data)
        .def_readonly("success", &Result<std::vector<VectorDirectionChange>>::success)
        .def_readonly("error_message", &Result<std::vector<VectorDirectionChange>>::error_message);

    // Bind Result template for rebased price pairs
    py::class_<Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>>(m, "RebasedPriceResult")
        .def_readonly("data", &Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>::data)
        .def_readonly("success", &Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>::success)
        .def_readonly("error_message", &Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>::error_message);

    // Bind Result template for peak/trough pairs
    py::class_<Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>>(m, "PeakTroughResult")
        .def_readonly("data", &Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>::data)
        .def_readonly("success", &Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>::success)
        .def_readonly("error_message", &Result<std::pair<std::vector<PeakTroughAnnotation>, std::vector<PeakTroughAnnotation>>>::error_message);

    // Bind visualization data structures
    py::class_<CandlestickRenderData>(m, "CandlestickRenderData")
        .def_readonly("x", &CandlestickRenderData::x)
        .def_readonly("open", &CandlestickRenderData::open)
        .def_readonly("high", &CandlestickRenderData::high)
        .def_readonly("low", &CandlestickRenderData::low)
        .def_readonly("close", &CandlestickRenderData::close)
        .def_readonly("body_color", &CandlestickRenderData::body_color)
        .def_readonly("wick_color", &CandlestickRenderData::wick_color)
        .def_readonly("is_valid", &CandlestickRenderData::is_valid);

    py::class_<StandardDeviationResult>(m, "StandardDeviationResult")
        .def_readonly("positive_std", &StandardDeviationResult::positive_std)
        .def_readonly("negative_std", &StandardDeviationResult::negative_std)
        .def_readonly("positive_count", &StandardDeviationResult::positive_count)
        .def_readonly("negative_count", &StandardDeviationResult::negative_count);

    // Bind Result templates for visualization functions
    py::class_<Result<std::vector<CandlestickRenderData>>>(m, "CandlestickRenderResult")
        .def_readonly("data", &Result<std::vector<CandlestickRenderData>>::data)
        .def_readonly("success", &Result<std::vector<CandlestickRenderData>>::success)
        .def_readonly("error_message", &Result<std::vector<CandlestickRenderData>>::error_message);

    py::class_<Result<StandardDeviationResult>>(m, "StandardDeviationResultWrapper")
        .def_readonly("data", &Result<StandardDeviationResult>::data)
        .def_readonly("success", &Result<StandardDeviationResult>::success)
        .def_readonly("error_message", &Result<StandardDeviationResult>::error_message);

    // StringResult removed - using CppStringResult instead to avoid conflicts

    // Bind new visualization data structures
    py::class_<MeanLevelsResult>(m, "MeanLevelsResult")
        .def_readonly("mean_peak_level", &MeanLevelsResult::mean_peak_level)
        .def_readonly("mean_trough_level", &MeanLevelsResult::mean_trough_level)
        .def_readonly("peak_count", &MeanLevelsResult::peak_count)
        .def_readonly("trough_count", &MeanLevelsResult::trough_count);

    py::class_<StairstepData>(m, "StairstepData")
        .def_readonly("x_values", &StairstepData::x_values)
        .def_readonly("y_values", &StairstepData::y_values)
        .def_readonly("is_valid", &StairstepData::is_valid);

    py::class_<Result<MeanLevelsResult>>(m, "MeanLevelsResultWrapper")
        .def_readonly("data", &Result<MeanLevelsResult>::data)
        .def_readonly("success", &Result<MeanLevelsResult>::success)
        .def_readonly("error_message", &Result<MeanLevelsResult>::error_message);

    py::class_<Result<StairstepData>>(m, "StairstepDataResult")
        .def_readonly("data", &Result<StairstepData>::data)
        .def_readonly("success", &Result<StairstepData>::success)
        .def_readonly("error_message", &Result<StairstepData>::error_message);

    // Bind Result template for py::dict
    py::class_<Result<py::dict>>(m, "PyDictResult")
        .def_readonly("data", &Result<py::dict>::data)
        .def_readonly("success", &Result<py::dict>::success)
        .def_readonly("error_message", &Result<py::dict>::error_message);

    // Bind Result template for std::string
    py::class_<Result<std::string>>(m, "CppStringResult")
        .def_readonly("data", &Result<std::string>::data)
        .def_readonly("success", &Result<std::string>::success)
        .def_readonly("error_message", &Result<std::string>::error_message);

    // ZeroPercentPriceResult removed - using existing DoubleResult instead

    m.def("calculate_donchian_midpoint", [](const std::vector<double>& closes, int length) {
        try {
            // Validate parameters (no artificial max cap)
            exception_handling::validate_array_size(closes.size(), 1);
            exception_handling::validate_window_size(length, static_cast<int>(closes.size()));

            auto result = calculate_donchian_midpoint_simd(closes, length);

            // Return success result as Python dict
            py::dict success_result;
            success_result["success"] = true;
            success_result["data"] = result;
            return success_result;

        } catch (const exception_handling::InvalidParameterError& e) {
            py::dict error_result;
            error_result["success"] = false;
            error_result["error_message"] = std::string("Invalid Parameter: ") + e.what();
            return error_result;
        } catch (const std::exception& e) {
            py::dict error_result;
            error_result["success"] = false;
            error_result["error_message"] = std::string("Error: ") + e.what();
            return error_result;
        }
    }, "Calculate Donchian midpoint with SIMD optimization and enterprise error handling",
       py::arg("closes"), py::arg("length"));

    m.def("calculate_donchian_midpoint_buffer", [](py::array_t<double> closes, int length) {
        // Zero-copy buffer access - direct pointer usage
        py::buffer_info buf = closes.request();
        double* ptr = static_cast<double*>(buf.ptr);
        size_t size = buf.shape[0];

        // Use pointer directly with existing algorithm - minimal overhead
        std::vector<double> closes_vec(ptr, ptr + size);
        return calculate_donchian_midpoint_simd(closes_vec, length);
    }, "Calculate Donchian midpoint with zero-copy buffer access",
       py::arg("closes"), py::arg("length"));

    m.def("calculate_donchian_strip_segments", &calculate_donchian_strip_segments,
          "Calculate Donchian indicator strip segments with color coding",
          py::arg("closes"), py::arg("donchian_midpoint_50"));

    m.def("calculate_bollinger_bandwidth", &calculate_bollinger_bandwidth,
          "Calculate Bollinger Bandwidth indicator",
          py::arg("closes"), py::arg("length"), py::arg("std_dev_multiplier") = 2.0);

    m.def("calculate_bollinger_bandwidth_buffer", [](py::array_t<double> closes, int length, double std_dev_multiplier) {
        // Zero-copy buffer access
        py::buffer_info buf = closes.request();
        double* ptr = static_cast<double*>(buf.ptr);
        size_t size = buf.shape[0];

        // Create vector view without copying
        std::vector<double> closes_view(ptr, ptr + size);
        return calculate_bollinger_bandwidth(closes_view, length, std_dev_multiplier);
    }, "Calculate Bollinger Bandwidth with zero-copy buffer access",
       py::arg("closes"), py::arg("length"), py::arg("std_dev_multiplier") = 2.0);

    m.def("rolling_max", &rolling_max,
          "Calculate rolling maximum",
          py::arg("data"), py::arg("window"));

    m.def("rolling_min", &rolling_min,
          "Calculate rolling minimum",
          py::arg("data"), py::arg("window"));

    m.def("simple_moving_average", &simple_moving_average,
          "Calculate simple moving average",
          py::arg("data"), py::arg("window"));

    m.def("track_vector_direction_changes", &track_vector_direction_changes,
          "Track vector direction changes for rebasing",
          py::arg("vector"));

    m.def("calculate_rebased_prices", [](const std::vector<double>& opens,
                                         const std::vector<double>& highs,
                                         const std::vector<double>& lows,
                                         const std::vector<double>& closes,
                                         const std::vector<double>& vector,
                                         const std::vector<VectorDirectionChange>& cycles) {
        try {
            // Validate array sizes (no artificial max cap)
            exception_handling::validate_array_size(opens.size(), 1);
            exception_handling::validate_array_size(highs.size(), 1);
            exception_handling::validate_array_size(lows.size(), 1);
            exception_handling::validate_array_size(closes.size(), 1);
            exception_handling::validate_array_size(vector.size(), 1);

            return calculate_rebased_prices(opens, highs, lows, closes, vector, cycles);

        } catch (const exception_handling::InvalidParameterError& e) {
            // Return error result in the same format as the function
            return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
                std::string("Parameter validation failed: ") + e.what()
            );
        } catch (const std::exception& e) {
            return Result<std::pair<std::vector<RebasedOHLC>, std::vector<double>>>(
                std::string("Error in rebased price calculation: ") + e.what()
            );
        }
    }, "Calculate rebased OHLC prices and vector values with memory safety validation",
       py::arg("opens"), py::arg("highs"), py::arg("lows"), py::arg("closes"),
       py::arg("vector"), py::arg("cycles"));

    m.def("calculate_rebased_prices_buffer", [](py::array_t<double> opens, py::array_t<double> highs,
                                               py::array_t<double> lows, py::array_t<double> closes,
                                               py::array_t<double> vector, const std::vector<VectorDirectionChange>& cycles) {
        // Zero-copy buffer access for all arrays
        auto opens_buf = opens.request();
        auto highs_buf = highs.request();
        auto lows_buf = lows.request();
        auto closes_buf = closes.request();
        auto vector_buf = vector.request();

        double* opens_ptr = static_cast<double*>(opens_buf.ptr);
        double* highs_ptr = static_cast<double*>(highs_buf.ptr);
        double* lows_ptr = static_cast<double*>(lows_buf.ptr);
        double* closes_ptr = static_cast<double*>(closes_buf.ptr);
        double* vector_ptr = static_cast<double*>(vector_buf.ptr);

        size_t size = opens_buf.shape[0];

        // Create vector views without copying
        std::vector<double> opens_view(opens_ptr, opens_ptr + size);
        std::vector<double> highs_view(highs_ptr, highs_ptr + size);
        std::vector<double> lows_view(lows_ptr, lows_ptr + size);
        std::vector<double> closes_view(closes_ptr, closes_ptr + size);
        std::vector<double> vector_view(vector_ptr, vector_ptr + size);

        return calculate_rebased_prices(opens_view, highs_view, lows_view, closes_view, vector_view, cycles);
    }, "Calculate rebased prices with zero-copy buffer access",
       py::arg("opens"), py::arg("highs"), py::arg("lows"), py::arg("closes"),
       py::arg("vector"), py::arg("cycles"));

    m.def("calculate_peaks_and_troughs", &calculate_peaks_and_troughs,
          "Calculate peaks and troughs based on zero crossings",
          py::arg("indices"), py::arg("opens"), py::arg("highs"), py::arg("lows"), py::arg("closes"));

    m.def("calculate_data_length", &calculate_data_length,
          "Calculate data length for array operations",
          py::arg("data"));

    m.def("generate_timestamps", &generate_timestamps,
          "Generate sequential timestamps array",
          py::arg("length"));

    m.def("calculate_total_candles_needed", &calculate_total_candles_needed,
          "Calculate total candles needed for timeframe",
          py::arg("dtl"), py::arg("candles_per_day"));

    m.def("apply_skip_logic_to_array", &apply_skip_logic_to_array,
          "Apply skip logic to array - set first skip_count elements to NaN",
          py::arg("input_array"), py::arg("skip_count"));

    m.def("calculate_actual_price", &calculate_actual_price,
          "Calculate actual price from pivot and percentage level",
          py::arg("current_pivot"), py::arg("level_percentage"));

    m.def("prepare_candlestick_render_data", &prepare_candlestick_render_data,
          "Prepare candlestick rendering data - moves computation from UI to backend",
          py::arg("timestamps"), py::arg("opens"), py::arg("highs"), py::arg("lows"), py::arg("closes"));

    m.def("calculate_separated_standard_deviations", &calculate_separated_standard_deviations,
          "Calculate separated standard deviations for positive and negative percentages",
          py::arg("percentage_closes"));

    m.def("calculate_separated_standard_deviations_from_peaks_troughs", &calculate_separated_standard_deviations_from_peaks_troughs,
          "Calculate separated standard deviations based on peaks and troughs levels",
          py::arg("peaks"), py::arg("troughs"));

    m.def("determine_donchian_color", &determine_donchian_color,
          "Determine Donchian color based on price position",
          py::arg("donchian_midpoint"), py::arg("closes"));

    m.def("prepare_donchian_stairstep_data", &prepare_donchian_stairstep_data,
          "Prepare Donchian stairstep data for UI rendering",
          py::arg("timestamps"), py::arg("donchian_values"));

    m.def("calculate_mean_peak_trough_levels", &calculate_mean_peak_trough_levels,
          "Calculate mean peak and trough levels",
          py::arg("peaks"), py::arg("troughs"));

    m.def("extract_peak_trough_strengths", &extract_peak_trough_strengths,
          "Extract peak and trough strengths - pure C++ computation",
          py::arg("peaks"), py::arg("troughs"));

    m.def("calculate_stdev_infinity_lines", &calculate_stdev_infinity_lines,
          "Calculate standard deviation infinity lines - pure C++ computation",
          py::arg("standard_deviations"), py::arg("zero_percent_price"));

    m.def("calculate_percentile_levels_from_peaks_troughs", &calculate_percentile_levels_from_peaks_troughs,
          "Calculate percentile levels from peaks and troughs - pure C++ computation",
          py::arg("peaks"), py::arg("troughs"), py::arg("zero_percent_price"));

    m.def("calculate_percentage_levels", &calculate_percentage_levels,
          "Calculate percentage levels for bandwidth chart - pure C++ computation",
          py::arg("bandwidth_values"));

    m.def("calculate_cycle_position", &calculate_cycle_position,
          "Calculate cycle position - pure C++ computation",
          py::arg("candle_index"), py::arg("cycles"));

    m.def("calculate_crosshair_info_lookup", &calculate_crosshair_info_lookup,
          "Calculate crosshair info lookup table - pure C++ computation",
          py::arg("rebased_data"), py::arg("cycles"));

    m.def("calculate_predictive_cycle_lines", &calculate_predictive_cycle_lines,
          "Calculate predictive cycle lines - pure C++ computation",
          py::arg("peaks"), py::arg("troughs"), py::arg("strengths_data"), py::arg("levels_data"));

    m.def("calculate_zero_percent_price", &calculate_zero_percent_price,
          "Calculate zero percent price - pure C++ computation",
          py::arg("rebased_data"), py::arg("closes"));

    m.def("prepare_data_table_rows", &prepare_data_table_rows,
          "Prepare data table rows with all formatting - pure C++ computation",
          py::arg("timestamps"), py::arg("actual_dates"), py::arg("opens"), py::arg("highs"),
          py::arg("lows"), py::arg("closes"), py::arg("volumes"), py::arg("crosshair_info_lookup"),
          py::arg("length"), py::arg("rebased_opens"), py::arg("rebased_highs"),
          py::arg("rebased_lows"), py::arg("rebased_closes"));

    m.def("prepare_projected_ohlc_table_rows", &prepare_projected_ohlc_table_rows,
          "Prepare projected OHLC table rows - pure C++ computation",
          py::arg("actual_dates"), py::arg("opens"), py::arg("highs"), py::arg("lows"), py::arg("closes"), py::arg("crosshair_info_lookup"));

    m.def("extract_volatility_statistics_data", &extract_volatility_statistics_data,
          "Extract and format volatility statistics high/low data from projected OHLC rows - pure C++ computation",
          py::arg("projected_ohlc_rows"));

    m.def("apply_volatility_statistics_filter", &apply_volatility_statistics_filter,
          "Apply volatility statistics filtering - pure C++ computation",
          py::arg("high_data"), py::arg("low_data"), py::arg("filter_type"));

    m.def("extract_highs_data_cpp", &extract_highs_data_cpp,
          "Extract highs data from filtered high data for volatility calculations - pure C++ computation",
          py::arg("filtered_high_data"));

    m.def("extract_lows_data_cpp", &extract_lows_data_cpp,
          "Extract lows data from filtered low data for volatility calculations - pure C++ computation",
          py::arg("filtered_low_data"));

    m.def("calculate_statistics_cpp", &calculate_statistics_cpp,
          "Calculate statistical values for a list of values - pure C++ computation",
          py::arg("values"));

    m.def("calculate_apex_values_cpp", &calculate_apex_values_cpp,
          "Calculate apex values and derived metrics for volatility calculations - pure C++ computation",
          py::arg("max_high"), py::arg("max_low"));

    m.def("calculate_axis_limits_cpp", &calculate_axis_limits_cpp,
          "Calculate axis limits with padding for volatility charts - pure C++ computation",
          py::arg("max_high"), py::arg("max_low"));

    m.def("calculate_title_position_cpp", &calculate_title_position_cpp,
          "Calculate title position at 7.5% above max_high for volatility charts - pure C++ computation",
          py::arg("max_high"), py::arg("max_low"));

    // Enterprise build and error information
    m.def("get_build_info", []() {
        py::dict info;
        info["version"] = "1.0.0";
        info["build_type"] =
#ifdef NDEBUG
            "Release";
#else
            "Debug";
#endif
        info["simd_enabled"] =
#ifdef SIMD_ENABLED
            true;
#else
            false;
#endif
        info["compiler"] =
#ifdef _MSC_VER
            "MSVC";
#elif defined(__GNUC__)
            "GCC";
#elif defined(__clang__)
            "Clang";
#else
            "Unknown";
#endif
        info["architecture"] =
#ifdef _WIN64
            "x64";
#elif defined(_WIN32)
            "x86";
#elif defined(__x86_64__)
            "x64";
#elif defined(__i386__)
            "x86";
#else
            "Unknown";
#endif

        // Add runtime performance info
        auto& error_logger = exception_handling::ErrorLogger::instance();
        info["total_errors"] = error_logger.get_error_count();

        return info;
    }, "Get build and runtime information");

    m.def("reset_error_count", []() {
        exception_handling::ErrorLogger::instance().reset_error_count();
    }, "Reset error counter for monitoring");

    m.def("get_error_count", []() {
        return exception_handling::ErrorLogger::instance().get_error_count();
    }, "Get total error count since startup");
}

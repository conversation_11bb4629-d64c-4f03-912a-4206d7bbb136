syntax = "proto3";

package market_data;

// Version information for schema evolution
message SchemaVersion {
  uint32 major = 1;
  uint32 minor = 2;
  uint32 patch = 3;
}

// Common timestamp with microsecond precision
message Timestamp {
  int64 seconds = 1;
  int32 nanos = 2;
}

// Correlation context for request tracing
message CorrelationContext {
  string correlation_id = 1;
  string request_id = 2;
  string operation = 3;
  string user_id = 4;
  string session_id = 5;
  Timestamp start_time = 6;
  string parent_span_id = 7;
}

// Market data request message
message MarketDataRequest {
  SchemaVersion schema_version = 1;
  CorrelationContext context = 2;
  
  string ticker = 3;
  string timeframe = 4;
  int32 dtl = 5;
  int32 length = 6;
  bool with_rebasing = 7;
  
  // Request metadata
  Timestamp request_time = 8;
  string client_id = 9;
}

// OHLC data point
message OHLCData {
  double open = 1;
  double high = 2;
  double low = 3;
  double close = 4;
  int64 volume = 5;
  Timestamp timestamp = 6;
}

// Rebased OHLC data point
message RebasedOHLCData {
  int32 index = 1;
  double open_pct = 2;
  double high_pct = 3;
  double low_pct = 4;
  double close_pct = 5;
}

// Technical indicator data
message TechnicalIndicators {
  repeated double donchian_midpoint = 1;
  repeated double simple_moving_average = 2;
  repeated double bollinger_upper = 3;
  repeated double bollinger_lower = 4;
  repeated double bollinger_bandwidth = 5;
}

// Peak/Trough data
message PeakTroughData {
  message Peak {
    int32 index = 1;
    double value = 2;
    double strength = 3;
  }
  
  message Trough {
    int32 index = 1;
    double value = 2;
    double strength = 3;
  }
  
  repeated Peak peaks = 1;
  repeated Trough troughs = 2;
}

// Standard deviation statistics
message StandardDeviationStats {
  double positive_std = 1;
  double negative_std = 2;
  int32 positive_count = 3;
  int32 negative_count = 4;
}

// Visualization data for charts
message VisualizationData {
  message CandlestickRenderData {
    double x = 1;
    double open = 2;
    double high = 3;
    double low = 4;
    double close = 5;
    string body_color = 6;
    string wick_color = 7;
    bool is_valid = 8;
  }
  
  message DonchianStairstepData {
    repeated double x_values = 1;
    repeated double y_values = 2;
    bool is_valid = 3;
  }
  
  repeated CandlestickRenderData candlestick_data = 1;
  repeated CandlestickRenderData rebased_candlestick_data = 2;
  DonchianStairstepData donchian_stairstep = 3;
  string donchian_color = 4;
}

// Market data response message
message MarketDataResponse {
  SchemaVersion schema_version = 1;
  CorrelationContext context = 2;
  
  // Response status
  enum Status {
    SUCCESS = 0;
    ERROR = 1;
    PARTIAL = 2;
  }
  
  Status status = 3;
  string error_message = 4;
  
  // Market data
  repeated OHLCData ohlc_data = 5;
  repeated RebasedOHLCData rebased_data = 6;
  TechnicalIndicators indicators = 7;
  PeakTroughData peak_trough_data = 8;
  StandardDeviationStats std_dev_stats = 9;
  VisualizationData visualization_data = 10;
  
  // Response metadata
  Timestamp response_time = 11;
  double processing_time_ms = 12;
  int32 data_points_count = 13;
}

// Chart update message
message ChartUpdateMessage {
  SchemaVersion schema_version = 1;
  CorrelationContext context = 2;
  
  enum UpdateType {
    FULL_REFRESH = 0;
    INCREMENTAL = 1;
    SETTINGS_CHANGE = 2;
  }
  
  UpdateType update_type = 3;
  VisualizationData visualization_data = 4;
  
  // Update metadata
  Timestamp update_time = 5;
  string chart_id = 6;
}

// Performance metrics message
message PerformanceMetrics {
  SchemaVersion schema_version = 1;
  Timestamp timestamp = 2;
  
  message RequestMetrics {
    string operation = 1;
    int64 count = 2;
    int64 error_count = 3;
    double avg_duration_ms = 4;
    double p95_duration_ms = 5;
    double p99_duration_ms = 6;
    double error_rate = 7;
  }
  
  message SystemMetrics {
    double cpu_usage_percent = 1;
    double memory_usage_mb = 2;
    int32 active_threads = 3;
    int32 active_connections = 4;
  }
  
  repeated RequestMetrics request_metrics = 3;
  SystemMetrics system_metrics = 4;
  
  // Custom metrics
  map<string, double> custom_counters = 5;
  map<string, double> custom_gauges = 6;
}

// Health check message
message HealthCheckMessage {
  SchemaVersion schema_version = 1;
  Timestamp timestamp = 2;
  
  enum HealthStatus {
    HEALTHY = 0;
    DEGRADED = 1;
    UNHEALTHY = 2;
  }
  
  message ComponentHealth {
    string component_name = 1;
    HealthStatus status = 2;
    string status_message = 3;
    double response_time_ms = 4;
  }
  
  HealthStatus overall_status = 3;
  repeated ComponentHealth components = 4;
  string version = 5;
  Timestamp uptime_start = 6;
}

// Error message
message ErrorMessage {
  SchemaVersion schema_version = 1;
  CorrelationContext context = 2;
  
  enum ErrorCode {
    UNKNOWN = 0;
    INVALID_REQUEST = 1;
    DATA_NOT_FOUND = 2;
    COMPUTATION_ERROR = 3;
    TIMEOUT = 4;
    RATE_LIMITED = 5;
    INTERNAL_ERROR = 6;
  }
  
  ErrorCode error_code = 3;
  string error_message = 4;
  string error_details = 5;
  Timestamp error_time = 6;
  
  // Stack trace for debugging
  repeated string stack_trace = 7;
}

"""
High-performance chart renderer with efficient painting and dirty region tracking.
Implements zero-computation UI layer with optimal rendering performance.
"""

import pyqtgraph as pg
from PyQt6.QtCore import QObject, QTimer, QRectF, pyqtSignal, Qt
from PyQt6.QtGui import QPainter, QPen, QBrush, QColor, QPixmap
from typing import Dict, Any, List, Optional, Set
import logging
from backend.models import ChartDataModel, ChartElement, ChartElementType


class DirtyRegionTracker:
    """
    Dirty region tracking for efficient repainting.
    Minimizes full-viewport repaints by tracking only changed areas.
    """

    def __init__(self):
        self.logger = self._setup_logging()
        self._dirty_regions: List[QRectF] = []
        self._full_repaint_needed = False
        self._viewport_bounds = QRectF()

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for dirty region tracking."""
        logger = logging.getLogger("DirtyRegionTracker")
        logger.setLevel(logging.DEBUG)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def add_dirty_region(self, bounds: Dict[str, float]):
        """Add a dirty region for repainting."""
        try:
            rect = QRectF(
                bounds.get("x_min", 0),
                bounds.get("y_min", 0),
                bounds.get("x_max", 1) - bounds.get("x_min", 0),
                bounds.get("y_max", 1) - bounds.get("y_min", 0),
            )

            # Only add if within viewport
            if self._viewport_bounds.intersects(rect):
                self._dirty_regions.append(rect)
                self.logger.debug(f"Added dirty region: {rect}")

        except Exception as e:
            self.logger.error(f"Error adding dirty region: {e}")

    def set_viewport_bounds(self, bounds: QRectF):
        """Set viewport bounds for intersection testing."""
        self._viewport_bounds = bounds

    def get_dirty_regions(self) -> List[QRectF]:
        """Get all dirty regions for repainting."""
        return self._dirty_regions.copy()

    def needs_full_repaint(self) -> bool:
        """Check if full repaint is needed."""
        return self._full_repaint_needed or len(self._dirty_regions) > 10

    def clear_dirty_regions(self):
        """Clear all dirty regions after repainting."""
        self._dirty_regions.clear()
        self._full_repaint_needed = False
        self.logger.debug("Cleared dirty regions")

    def force_full_repaint(self):
        """Force a full repaint on next render."""
        self._full_repaint_needed = True
        self._dirty_regions.clear()
        self.logger.debug("Forced full repaint")


class ChartRenderer(QObject):
    """
    High-performance chart renderer implementing efficient painting patterns.
    Uses Model/View architecture with dirty region tracking for optimal performance.
    """

    # Signals for rendering events
    render_started = pyqtSignal()
    render_completed = pyqtSignal(float)  # render_time_ms
    render_error = pyqtSignal(str)  # error_message

    def __init__(self, chart_widget: pg.PlotWidget, model: ChartDataModel, parent=None):
        super().__init__(parent)
        self.chart_widget = chart_widget
        self.model = model
        self.logger = self._setup_logging()

        # Dirty region tracking for efficient repainting
        self.dirty_tracker = DirtyRegionTracker()

        # Rendering optimization
        self._render_cache: Dict[str, QPixmap] = {}
        self._render_timer = QTimer()
        self._render_timer.setSingleShot(True)
        self._render_timer.timeout.connect(self._perform_render)

        # Performance monitoring
        self._render_stats = {
            "total_renders": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_render_time": 0.0,
        }

        # Connect to model signals
        self._setup_model_connections()

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for renderer."""
        logger = logging.getLogger("ChartRenderer")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _setup_model_connections(self):
        """Setup connections to model signals for automatic rendering."""
        self.model.element_added.connect(self._on_element_added)
        self.model.element_removed.connect(self._on_element_removed)
        self.model.element_updated.connect(self._on_element_updated)
        self.model.bounds_changed.connect(self._on_bounds_changed)
        self.model.dirty_region_changed.connect(self._on_dirty_region_changed)

    def _on_element_added(self, element_id: str, element_type: ChartElementType):
        """Handle element addition."""
        element = self.model.get_element(element_id)
        if element:
            self.dirty_tracker.add_dirty_region(element.bounds)
            self._invalidate_cache(element_id)
            self._schedule_render()

    def _on_element_removed(self, element_id: str):
        """Handle element removal."""
        self._invalidate_cache(element_id)
        self.dirty_tracker.force_full_repaint()
        self._schedule_render()

    def _on_element_updated(self, element_id: str, render_data: Dict[str, Any]):
        """Handle element update."""
        element = self.model.get_element(element_id)
        if element:
            self.dirty_tracker.add_dirty_region(element.bounds)
            self._invalidate_cache(element_id)
            self._schedule_render()

    def _on_bounds_changed(self, bounds: Dict[str, float]):
        """Handle bounds change."""
        self.dirty_tracker.set_viewport_bounds(
            QRectF(
                bounds.get("x_min", 0),
                bounds.get("y_min", 0),
                bounds.get("x_max", 1) - bounds.get("x_min", 0),
                bounds.get("y_max", 1) - bounds.get("y_min", 0),
            )
        )

    def _on_dirty_region_changed(self, dirty_info: Dict[str, Any]):
        """Handle dirty region changes."""
        regions = dirty_info.get("regions", [])
        for region in regions:
            self.dirty_tracker.add_dirty_region(region)
        self._schedule_render()

    def _schedule_render(self):
        """Schedule rendering with debouncing for performance."""
        # Debounce rapid updates
        self._render_timer.start(16)  # ~60 FPS max

    def _perform_render(self):
        """Perform the actual rendering with performance optimization."""
        try:
            import time

            start_time = time.perf_counter()

            self.render_started.emit()

            # Clear existing items efficiently
            self._clear_chart_items()

            # Get visible elements sorted by z-order
            visible_elements = self.model.get_visible_elements()

            if not visible_elements:
                self.logger.debug("No visible elements to render")
                return

            # Render elements based on type
            for element in visible_elements:
                self._render_element(element)

            # Clear dirty regions after successful render
            self.dirty_tracker.clear_dirty_regions()
            self.model.clear_dirty_regions()

            # Update performance stats
            render_time = (time.perf_counter() - start_time) * 1000
            self._update_render_stats(render_time)

            self.render_completed.emit(render_time)
            self.logger.debug(f"Render completed in {render_time:.2f}ms")

        except Exception as e:
            error_msg = f"Render error: {e}"
            self.logger.error(error_msg)
            self.render_error.emit(error_msg)

    def _clear_chart_items(self):
        """Efficiently clear chart items."""
        try:
            # Get plot item for efficient clearing
            plot_item = self.chart_widget.getPlotItem()

            # Clear all data items efficiently
            plot_item.clear()

        except Exception as e:
            self.logger.warning(f"Error clearing chart items: {e}")

    def _render_element(self, element: ChartElement):
        """Render a single chart element based on its type."""
        try:
            if element.element_type == ChartElementType.CANDLESTICK:
                self._render_candlestick(element)
            elif element.element_type == ChartElementType.INDICATOR_LINE:
                self._render_indicator_line(element)
            elif element.element_type == ChartElementType.ANNOTATION:
                self._render_annotation(element)
            elif element.element_type == ChartElementType.INFINITY_LINE:
                self._render_infinity_line(element)
            elif element.element_type == ChartElementType.PEAK_TROUGH:
                self._render_peak_trough(element)
            else:
                self.logger.warning(f"Unknown element type: {element.element_type}")

        except Exception as e:
            self.logger.error(f"Error rendering element {element.element_id}: {e}")

    def _render_candlestick(self, element: ChartElement):
        """Render candlestick element using pre-computed data."""
        render_data = element.render_data
        candlestick_data = render_data.get("candlesticks", [])

        if not candlestick_data:
            return

        # Create candlestick item from pre-computed data
        from .chart_items import PreComputedCandlestickItem

        candlestick_item = PreComputedCandlestickItem(candlestick_data, element.bounds)

        # Add to chart
        self.chart_widget.addItem(candlestick_item)

    def _render_indicator_line(self, element: ChartElement):
        """Render indicator line using pre-computed data."""
        render_data = element.render_data
        line_data = render_data.get("line_data", {})

        x_values = line_data.get("x_values", [])
        y_values = line_data.get("y_values", [])

        if not x_values or not y_values:
            return

        # Create line item
        color = render_data.get("color", "#ffffff")
        width = render_data.get("width", 1)
        style = render_data.get("style", "solid")

        pen_style = Qt.PenStyle.SolidLine if style == "solid" else Qt.PenStyle.DashLine
        pen = pg.mkPen(color=color, width=width, style=pen_style)

        line_item = self.chart_widget.plot(x_values, y_values, pen=pen)
        line_item.setZValue(element.z_order)

    def _render_annotation(self, element: ChartElement):
        """Render annotation using pre-computed data."""
        render_data = element.render_data

        if render_data.get("type") == "text":
            text_item = pg.TextItem(
                text=render_data.get("text", ""),
                color=render_data.get("color", "#ffffff"),
                anchor=render_data.get("anchor", (0.5, 0.5)),
            )
            text_item.setPos(render_data.get("x", 0), render_data.get("y", 0))
            text_item.setZValue(element.z_order)
            self.chart_widget.addItem(text_item)

    def _render_infinity_line(self, element: ChartElement):
        """Render infinity line using pre-computed data."""
        render_data = element.render_data

        angle = render_data.get("angle", 0)
        pos = render_data.get("position", 0)
        color = render_data.get("color", "#ffffff")
        width = render_data.get("width", 1)
        style = render_data.get("style", "solid")

        pen_style = Qt.PenStyle.SolidLine if style == "solid" else Qt.PenStyle.DashLine
        pen = pg.mkPen(color=color, width=width, style=pen_style)

        line_item = pg.InfiniteLine(angle=angle, pos=pos, pen=pen)
        line_item.setZValue(element.z_order)
        self.chart_widget.addItem(line_item)

    def _invalidate_cache(self, element_id: str):
        """Invalidate render cache for element."""
        if element_id in self._render_cache:
            del self._render_cache[element_id]
            self._render_stats["cache_misses"] += 1

    def _update_render_stats(self, render_time: float):
        """Update rendering performance statistics."""
        self._render_stats["total_renders"] += 1

        # Calculate rolling average
        alpha = 0.1  # Smoothing factor
        self._render_stats["avg_render_time"] = (
            alpha * render_time + (1 - alpha) * self._render_stats["avg_render_time"]
        )

    def get_render_stats(self) -> Dict[str, Any]:
        """Get rendering performance statistics."""
        return self._render_stats.copy()

    def force_full_render(self):
        """Force a complete re-render of all elements."""
        self.dirty_tracker.force_full_repaint()
        self._render_cache.clear()
        self._schedule_render()
        self.logger.info("Forced full render")

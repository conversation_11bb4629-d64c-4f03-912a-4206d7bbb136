import sys
import os

# Add the Debug build directory to Python path
debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
sys.path.insert(0, debug_path)

print(f"Python version: {sys.version}")
print(f"Looking for module in: {debug_path}")
print(f"Files in Debug directory:")

try:
    files = os.listdir(debug_path)
    for f in files:
        if f.endswith('.pyd') or f.endswith('.dll'):
            print(f"  - {f}")
except:
    print("  Could not list files")

print("\nAttempting to import indicators module...")

try:
    import indicators
    print("✅ SUCCESS: Module imported!")
    
    # Get build info
    if hasattr(indicators, 'get_build_info'):
        info = indicators.get_build_info()
        print(f"Build info: {info}")
    
    # Count functions
    funcs = [x for x in dir(indicators) if not x.startswith('_')]
    print(f"Available functions: {len(funcs)}")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    print(f"Error type: {type(e)}")

# -*- coding: utf-8 -*-

# Resource object code (Python)
# Created by: compile_resources_python.py
# Source: icons.qrc

from PyQt6 import QtCore

qt_resource_data = {
    0: b"""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""",
}

qt_resource_name = {
    "/styles/dark_theme.qss": 0,
}

def qInitResources():
    """Initialize Qt resources."""
    try:
        for path, file_id in qt_resource_name.items():
            data = base64.b64decode(qt_resource_data[file_id])
            QtCore.qRegisterResourceData(0x03, data, path.encode())
        return True
    except Exception as e:
        print(f"Failed to initialize resources: {e}")
        return False

def qCleanupResources():
    """Cleanup Qt resources."""
    try:
        for path in qt_resource_name.keys():
            QtCore.qUnregisterResourceData(0x03, path.encode())
    except Exception:
        pass

# Auto-initialize resources when module is imported
import base64
qInitResources()
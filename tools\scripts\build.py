#!/usr/bin/env python3
"""
Modern PEP 517 Build Script for DataDriven Trading Platform

This script provides a unified interface for building the project using modern
Python packaging standards (PEP 517/518) with scikit-build-core backend.
"""

import sys
import os
import subprocess
import argparse
import shutil
from pathlib import Path
from typing import List, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ModernBuilder:
    """Modern PEP 517 compliant builder for DataDriven Trading Platform."""

    def __init__(self, project_root: Optional[Path] = None):
        """Initialize builder with project root."""
        self.project_root = project_root or Path(__file__).parent.parent
        self.build_dir = self.project_root / "build"
        self.dist_dir = self.project_root / "dist"

    def clean(self, all_artifacts: bool = False):
        """Clean build artifacts."""
        logger.info("Cleaning build artifacts...")

        # Standard build directories
        dirs_to_clean = [
            self.build_dir,
            self.dist_dir,
            self.project_root / "*.egg-info",
            self.project_root / "__pycache__",
        ]

        if all_artifacts:
            # Additional directories for deep clean
            dirs_to_clean.extend(
                [
                    self.project_root / ".pytest_cache",
                    self.project_root / "htmlcov",
                    self.project_root / ".coverage",
                    self.project_root / ".mypy_cache",
                ]
            )

        for pattern in dirs_to_clean:
            if "*" in str(pattern):
                # Handle glob patterns
                for path in self.project_root.glob(pattern.name):
                    if path.is_dir():
                        shutil.rmtree(path, ignore_errors=True)
                        logger.info(f"Removed directory: {path}")
                    elif path.is_file():
                        path.unlink(missing_ok=True)
                        logger.info(f"Removed file: {path}")
            else:
                if pattern.exists():
                    if pattern.is_dir():
                        shutil.rmtree(pattern, ignore_errors=True)
                        logger.info(f"Removed directory: {pattern}")
                    else:
                        pattern.unlink()
                        logger.info(f"Removed file: {pattern}")

    def build_sdist(self) -> bool:
        """Build source distribution using PEP 517."""
        logger.info("Building source distribution (sdist)...")

        try:
            cmd = [
                sys.executable,
                "-m",
                "build",
                "--sdist",
                "--outdir",
                str(self.dist_dir),
            ]
            result = subprocess.run(
                cmd, cwd=self.project_root, check=True, capture_output=True, text=True
            )

            logger.info("Source distribution built successfully")
            logger.debug(f"Build output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Source distribution build failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False

    def build_wheel(self, config_settings: Optional[dict] = None) -> bool:
        """Build wheel distribution using PEP 517."""
        logger.info("Building wheel distribution...")

        try:
            cmd = [
                sys.executable,
                "-m",
                "build",
                "--wheel",
                "--outdir",
                str(self.dist_dir),
            ]

            # Add config settings if provided
            if config_settings:
                for key, value in config_settings.items():
                    cmd.extend(["--config-setting", f"{key}={value}"])

            result = subprocess.run(
                cmd, cwd=self.project_root, check=True, capture_output=True, text=True
            )

            logger.info("Wheel distribution built successfully")
            logger.debug(f"Build output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Wheel build failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False

    def build_both(self, config_settings: Optional[dict] = None) -> bool:
        """Build both source and wheel distributions."""
        logger.info("Building both source and wheel distributions...")

        try:
            cmd = [sys.executable, "-m", "build", "--outdir", str(self.dist_dir)]

            # Add config settings if provided
            if config_settings:
                for key, value in config_settings.items():
                    cmd.extend(["--config-setting", f"{key}={value}"])

            result = subprocess.run(
                cmd, cwd=self.project_root, check=True, capture_output=True, text=True
            )

            logger.info("Both distributions built successfully")
            logger.debug(f"Build output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Build failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False

    def install_editable(self, config_settings: Optional[dict] = None) -> bool:
        """Install package in editable mode using PEP 517."""
        logger.info("Installing package in editable mode...")

        try:
            cmd = [sys.executable, "-m", "pip", "install", "-e", "."]

            # Add config settings via environment variables
            env = os.environ.copy()
            if config_settings:
                for key, value in config_settings.items():
                    env[f"SKBUILD_{key.upper()}"] = str(value)

            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                check=True,
                capture_output=True,
                text=True,
                env=env,
            )

            logger.info("Package installed in editable mode successfully")
            logger.debug(f"Install output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Editable install failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False

    def verify_build(self) -> bool:
        """Verify the built distributions."""
        logger.info("Verifying built distributions...")

        # Check if distributions exist
        if not self.dist_dir.exists():
            logger.error("Distribution directory does not exist")
            return False

        dist_files = list(self.dist_dir.glob("*"))
        if not dist_files:
            logger.error("No distribution files found")
            return False

        # Verify with twine
        try:
            cmd = [sys.executable, "-m", "twine", "check"] + [
                str(f) for f in dist_files
            ]
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)

            logger.info("Distribution verification passed")
            logger.debug(f"Verification output: {result.stdout}")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Distribution verification failed: {e}")
            logger.error(f"Error output: {e.stderr}")
            return False
        except FileNotFoundError:
            logger.warning("twine not available, skipping verification")
            return True

    def get_build_info(self) -> dict:
        """Get build environment information."""
        info = {
            "python_version": sys.version,
            "platform": sys.platform,
            "project_root": str(self.project_root),
            "build_backend": "scikit_build_core.build",
        }

        # Check for build tools
        tools = ["cmake", "ninja", "git"]
        for tool in tools:
            try:
                result = subprocess.run(
                    [tool, "--version"], capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    info[f"{tool}_version"] = result.stdout.split("\n")[0]
                else:
                    info[f"{tool}_version"] = "not available"
            except (subprocess.TimeoutExpired, FileNotFoundError):
                info[f"{tool}_version"] = "not available"

        return info


def main():
    """Main build script entry point."""
    parser = argparse.ArgumentParser(
        description="Modern PEP 517 build script for DataDriven Trading Platform"
    )

    parser.add_argument(
        "command",
        choices=["clean", "sdist", "wheel", "both", "install", "verify", "info"],
        help="Build command to execute",
    )

    parser.add_argument(
        "--clean-all",
        action="store_true",
        help="Clean all artifacts including caches (for clean command)",
    )

    parser.add_argument(
        "--build-type",
        choices=["Release", "Debug", "RelWithDebInfo"],
        default="Release",
        help="CMake build type",
    )

    parser.add_argument(
        "--enable-simd",
        action="store_true",
        default=True,
        help="Enable SIMD optimizations",
    )

    parser.add_argument(
        "--enable-tests", action="store_true", help="Build with tests enabled"
    )

    parser.add_argument(
        "--enable-benchmarks", action="store_true", help="Build with benchmarks enabled"
    )

    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize builder
    builder = ModernBuilder()

    # Prepare config settings
    config_settings = {
        "cmake.build-type": args.build_type,
        "cmake.verbose": str(args.verbose).lower(),
    }

    if args.enable_simd:
        config_settings["cmake.define.ENABLE_SIMD"] = "ON"

    if args.enable_tests:
        config_settings["cmake.define.BUILD_TESTS"] = "ON"

    if args.enable_benchmarks:
        config_settings["cmake.define.BUILD_BENCHMARKS"] = "ON"

    # Execute command
    success = True

    if args.command == "clean":
        builder.clean(all_artifacts=args.clean_all)
    elif args.command == "sdist":
        success = builder.build_sdist()
    elif args.command == "wheel":
        success = builder.build_wheel(config_settings)
    elif args.command == "both":
        success = builder.build_both(config_settings)
    elif args.command == "install":
        success = builder.install_editable(config_settings)
    elif args.command == "verify":
        success = builder.verify_build()
    elif args.command == "info":
        info = builder.get_build_info()
        print("Build Environment Information:")
        print("=" * 40)
        for key, value in info.items():
            print(f"{key}: {value}")
        return 0

    if success:
        logger.info(f"Command '{args.command}' completed successfully")
        return 0
    else:
        logger.error(f"Command '{args.command}' failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())

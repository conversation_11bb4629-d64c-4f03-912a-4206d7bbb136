"""
Option Analyzer Tab
Advanced option analysis with three-chart layout for comprehensive visualization.
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QSplitter,
    QComboBox,
    QRadioButton,
    QCheckBox,
)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QFont
import pyqtgraph as pg
from pyqtgraph import PlotWidget, BarGraphItem
from typing import Dict, Any, Optional, List
import logging
import re
from collections import Counter

# Import enterprise-grade components following codebase patterns
from backend.signals import signal_manager, TypedSlotDecorator
from backend.observability import get_structured_logger


class OptionAnalyzerTab(QWidget):
    """
    Option Analyzer tab with three-chart layout.
    Implements enterprise-grade architecture with zero-computation UI.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()

        # Chart widgets
        self.top_left_chart: Optional[PlotWidget] = None
        self.top_right_chart: Optional[PlotWidget] = None
        self.bottom_chart: Optional[PlotWidget] = None

        # Bottom chart mode selector
        self.bottom_chart_mode_selector: Optional[QComboBox] = None
        self.current_bottom_chart_mode: str = "Peaks and Troughs Distribution"

        # Data storage
        self.current_data: Optional[Dict[str, Any]] = None

        self.setup_ui()

        # Initialize visibility based on default mode
        self._update_control_visibility()

        self.logger.info("Option Analyzer tab initialized with three-chart layout")

        # Debug: Log that this tab is ready to receive data
        self.logger.info("Option Analyzer ready to receive data via on_data_received method")

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging following enterprise patterns."""
        return get_structured_logger("OptionAnalyzerTab")

    def setup_ui(self):
        """Initialize the three-chart user interface layout."""
        # Main vertical layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # Create main splitter for horizontal division
        main_splitter = QSplitter(Qt.Orientation.Vertical)
        main_splitter.setChildrenCollapsible(False)

        # Top section (will be split vertically)
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        top_layout.setSpacing(5)

        # Create top splitter for vertical division of top half
        top_splitter = QSplitter(Qt.Orientation.Horizontal)
        top_splitter.setChildrenCollapsible(False)

        # Top-left chart
        self.top_left_chart = self._create_chart("Peak Distribution Chart")
        top_splitter.addWidget(self.top_left_chart)

        # Top-right chart
        self.top_right_chart = self._create_chart("Trough Distribution Chart")
        top_splitter.addWidget(self.top_right_chart)

        # Set equal sizes for top charts
        top_splitter.setSizes([400, 400])

        top_layout.addWidget(top_splitter)
        main_splitter.addWidget(top_widget)

        # Bottom chart with mode selector
        bottom_widget = self._create_bottom_chart_with_selector()
        main_splitter.addWidget(bottom_widget)

        # Set sizes: top half and bottom half equal
        main_splitter.setSizes([400, 400])

        main_layout.addWidget(main_splitter)

        self.logger.info("Three-chart layout initialized successfully")

    def _create_chart(self, title: str) -> PlotWidget:
        """
        Create a chart widget following enterprise patterns.

        Args:
            title: Chart title for identification

        Returns:
            Configured PlotWidget
        """
        chart = PlotWidget()

        # Apply enterprise-grade chart styling
        chart.setBackground("#1e1e1e")  # Dark theme following codebase patterns
        chart.setLabel("left", "")  # Clean appearance
        chart.setLabel("bottom", "")

        # Enable mouse interaction
        chart.setMouseEnabled(x=True, y=True)
        chart.enableAutoRange("xy", True)

        # Disable grid for cleaner look
        chart.showGrid(x=False, y=False)

        # Add title as plot item
        chart.setTitle(title, color="#ffffff", size="12pt")

        # Enable OpenGL if available for performance
        try:
            chart.setUseOpenGL(True)
            self.logger.debug(f"OpenGL enabled for {title} chart")
        except Exception as e:
            self.logger.warning(f"OpenGL not available for {title}: {e}")

        # Optimize rendering following codebase patterns
        plot_item = chart.getPlotItem()
        plot_item.setClipToView(True)
        plot_item.setDownsampling(auto=True)

        # Initially enable auto-range for data fitting
        chart.enableAutoRange("xy", True)
        chart.setMouseEnabled(x=True, y=True)  # Enable temporarily for setup

        self.logger.debug(f"Anchored chart created: {title}")
        return chart

    def _create_bottom_chart_with_selector(self) -> QWidget:
        """
        Create the bottom chart widget with mode selector dropdown.

        Returns:
            QWidget containing the chart and mode selector
        """
        # Create container widget
        bottom_container = QWidget()
        layout = QVBoxLayout(bottom_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)

        # Create top bar with mode selector
        top_bar = QWidget()
        top_bar_layout = QHBoxLayout(top_bar)
        top_bar_layout.setContentsMargins(5, 2, 5, 2)
        top_bar_layout.setSpacing(10)

        # Mode selector label
        mode_label = QLabel("Chart Mode:")

        # Mode selector dropdown
        self.bottom_chart_mode_selector = QComboBox()
        self.bottom_chart_mode_selector.addItems([
            "Peaks and Troughs Distribution",
            "Average Range Chart"
        ])
        self.bottom_chart_mode_selector.setCurrentText(self.current_bottom_chart_mode)
        self.bottom_chart_mode_selector.setMinimumWidth(200)

        # Connect mode selector to handler
        self.bottom_chart_mode_selector.currentTextChanged.connect(self._on_bottom_chart_mode_changed)

        # Rebasing options (only visible for Average Range Chart)
        self.rebase_label = QLabel("Rebase:")
        self.rebase_label.setVisible(False)

        self.rebase_previous_radio = QRadioButton("Previous Day Close")
        self.rebase_previous_radio.setChecked(True)  # Default
        self.rebase_previous_radio.setVisible(False)
        self.rebase_previous_radio.toggled.connect(self._on_rebase_option_changed)

        self.rebase_last_radio = QRadioButton("Last Close")
        self.rebase_last_radio.setVisible(False)
        self.rebase_last_radio.toggled.connect(self._on_rebase_option_changed)

        # Split Sort checkbox for Average Range Chart
        self.split_sort_avg_checkbox = QCheckBox("Split Sort")
        self.split_sort_avg_checkbox.setVisible(False)
        self.split_sort_avg_checkbox.toggled.connect(self._on_split_sort_changed)

        # Split Sort checkbox for Peaks and Troughs Distribution Chart
        self.split_sort_peaks_troughs_checkbox = QCheckBox("Split Sort")
        self.split_sort_peaks_troughs_checkbox.setVisible(False)
        self.split_sort_peaks_troughs_checkbox.toggled.connect(self._on_split_sort_peaks_troughs_changed)

        # Add to top bar
        top_bar_layout.addWidget(mode_label)
        top_bar_layout.addWidget(self.bottom_chart_mode_selector)
        top_bar_layout.addWidget(self.rebase_label)
        top_bar_layout.addWidget(self.rebase_previous_radio)
        top_bar_layout.addWidget(self.rebase_last_radio)
        top_bar_layout.addWidget(self.split_sort_avg_checkbox)
        top_bar_layout.addWidget(self.split_sort_peaks_troughs_checkbox)
        top_bar_layout.addStretch()  # Push everything to the left

        # Create the chart
        self.bottom_chart = self._create_chart(self.current_bottom_chart_mode)

        # Add to container
        layout.addWidget(top_bar)
        layout.addWidget(self.bottom_chart)

        self.logger.debug("Bottom chart with mode selector created")
        return bottom_container

    def _on_bottom_chart_mode_changed(self, mode: str):
        """
        Handle bottom chart mode change.

        Args:
            mode: Selected chart mode
        """
        try:
            self.logger.info(f"Bottom chart mode changed to: {mode}")
            self.current_bottom_chart_mode = mode

            # Update control visibility based on new mode
            self._update_control_visibility()

            # Update chart title
            self.bottom_chart.setTitle(mode, color="#ffffff", size="12pt")

            # Update chart content based on mode
            if self.current_data:
                self._update_bottom_chart_content()

        except Exception as e:
            self.logger.error(f"Error changing bottom chart mode: {e}")

    def _on_rebase_option_changed(self):
        """Handle rebasing option changes."""
        try:
            # Only update if we're in Average Range Chart mode
            if self.current_bottom_chart_mode == "Average Range Chart":
                self.logger.info(f"Rebasing option changed - updating average range chart")
                self._update_average_range_chart()
        except Exception as e:
            self.logger.error(f"Error handling rebase option change: {e}")

    def _on_split_sort_changed(self):
        """Handle split sort checkbox changes for average range chart."""
        try:
            if self.current_bottom_chart_mode == "Average Range Chart":
                self.logger.info(f"Split sort changed - updating average range chart")
                self._update_average_range_chart()
        except Exception as e:
            self.logger.error(f"Error handling split sort change: {e}")

    def _on_split_sort_peaks_troughs_changed(self):
        """Handle split sort checkbox changes for peaks and troughs distribution chart."""
        try:
            if self.current_bottom_chart_mode == "Peaks and Troughs Distribution":
                self.logger.info(f"Peaks and troughs split sort changed - updating chronological chart")
                if self.current_data:
                    self._update_bottom_chart_content()
        except Exception as e:
            self.logger.error(f"Error handling peaks and troughs split sort change: {e}")

    def _update_control_visibility(self):
        """Update control visibility based on current bottom chart mode."""
        try:
            mode = self.current_bottom_chart_mode
            is_average_range = (mode == "Average Range Chart")
            is_peaks_troughs = (mode == "Peaks and Troughs Distribution")

            # Rebasing options only for Average Range Chart
            self.rebase_label.setVisible(is_average_range)
            self.rebase_previous_radio.setVisible(is_average_range)
            self.rebase_last_radio.setVisible(is_average_range)

            # Split sort checkboxes for respective modes
            self.split_sort_avg_checkbox.setVisible(is_average_range)
            self.split_sort_peaks_troughs_checkbox.setVisible(is_peaks_troughs)

            self.logger.debug(f"Control visibility updated for mode: {mode}")

        except Exception as e:
            self.logger.error(f"Error updating control visibility: {e}")

    def _update_bottom_chart_content(self):
        """
        Update bottom chart content based on current mode.
        """
        if not self.bottom_chart:
            return

        try:
            if self.current_bottom_chart_mode == "Peaks and Troughs Distribution":
                self._update_chronological_peaks_troughs_chart()
            elif self.current_bottom_chart_mode == "Average Range Chart":
                self._update_average_range_chart()

        except Exception as e:
            self.logger.error(f"Error updating bottom chart content: {e}")

    def _update_average_range_chart(self):
        """
        Update bottom chart with average range OHLC bars for last 25% of data.
        Shows OHLC bars (not candlesticks) rebased on the last close price.
        Accounts for gaps from previous close to current open.
        """
        if not self.bottom_chart:
            return

        try:
            # Clear existing chart
            self.bottom_chart.clear()

            if not self.current_data:
                self.logger.warning("No data available for average range chart")
                return

            # Get OHLC data
            opens = self.current_data.get("open", [])
            highs = self.current_data.get("high", [])
            lows = self.current_data.get("low", [])
            closes = self.current_data.get("close", [])

            # Check if we have valid OHLC data
            if (opens is None or highs is None or lows is None or closes is None or
                len(opens) == 0 or len(highs) == 0 or len(lows) == 0 or len(closes) == 0):
                self.logger.warning("Insufficient OHLC data for average range chart")
                return

            # Calculate last 25% of data
            total_length = len(closes)
            start_index = int(total_length * 0.75)  # Start at 75% to get last 25%

            if start_index >= total_length - 1:
                self.logger.warning("Not enough data for 25% range")
                return

            # Get last 25% of data
            last_25_opens = opens[start_index:]
            last_25_highs = highs[start_index:]
            last_25_lows = lows[start_index:]
            last_25_closes = closes[start_index:]

            # Get the last close price for rebasing
            last_close = closes[-1]

            if last_close == 0:
                self.logger.warning("Last close price is zero - cannot rebase")
                return

            self.logger.info(f"Creating average range chart with {len(last_25_closes)} bars, rebased on last close: {last_close:.2f}")

            # First pass: Calculate all daily returns
            daily_returns_data = []
            for i, (open_price, high_price, low_price, close_price) in enumerate(
                zip(last_25_opens, last_25_highs, last_25_lows, last_25_closes)
            ):
                # Get previous close for daily return calculation
                if i == 0:
                    # For first bar, get RTH close from before the 25% range
                    if start_index > 0:
                        prev_close = self._get_rth_close(start_index - 1)
                    else:
                        # If no previous data, use the open of first bar
                        prev_close = open_price
                else:
                    # Get RTH close from previous day
                    prev_close = self._get_rth_close(start_index + i - 1)

                # Calculate daily returns (percentage change from previous close)
                daily_open_return = ((open_price / prev_close) - 1.0) * 100.0
                daily_high_return = ((high_price / prev_close) - 1.0) * 100.0
                daily_low_return = ((low_price / prev_close) - 1.0) * 100.0
                daily_close_return = ((close_price / prev_close) - 1.0) * 100.0

                daily_returns_data.append({
                    'open': daily_open_return,
                    'high': daily_high_return,
                    'low': daily_low_return,
                    'close': daily_close_return
                })

            # Apply rebasing based on selected option
            if self.rebase_last_radio.isChecked():
                # Rebase to last close (shift entire chart so last close = 0%)
                last_close_return = daily_returns_data[-1]['close']
                offset = -last_close_return
            else:
                # Rebase to previous day close (no offset - keep daily returns as-is)
                offset = 0.0

            # Apply split sort if enabled
            if self.split_sort_avg_checkbox.isChecked():
                daily_returns_data = self._apply_split_sort_to_daily_returns(daily_returns_data, offset)

            # Second pass: Apply rebasing and create bars
            for i, daily_data in enumerate(daily_returns_data):
                # Apply rebasing
                rebased_open = daily_data['open'] + offset
                rebased_high = daily_data['high'] + offset
                rebased_low = daily_data['low'] + offset
                rebased_close = daily_data['close'] + offset

                # Create OHLC bar (not candlestick)
                x_pos = i
                bar_width = 0.3

                # Determine color based on bullish/bearish (close vs open)
                if rebased_close >= rebased_open:
                    color = "#4CAF50"  # Green for bullish bars
                else:
                    color = "#F44336"  # Red for bearish bars

                # Draw high-low line (main vertical line)
                high_low_line = self.bottom_chart.plot(
                    [x_pos, x_pos], [rebased_low, rebased_high],
                    pen=pg.mkPen(color=color, width=2),
                    name=f'Bar {i+1} High-Low'
                )

                # Draw open tick (left horizontal line)
                open_tick = self.bottom_chart.plot(
                    [x_pos - bar_width, x_pos], [rebased_open, rebased_open],
                    pen=pg.mkPen(color=color, width=2),
                    name=f'Bar {i+1} Open'
                )

                # Draw close tick (right horizontal line)
                close_tick = self.bottom_chart.plot(
                    [x_pos, x_pos + bar_width], [rebased_close, rebased_close],
                    pen=pg.mkPen(color=color, width=2),
                    name=f'Bar {i+1} Close'
                )

            # Set chart labels and title based on rebasing option
            if self.rebase_last_radio.isChecked():
                y_label = 'Rebased % (Last Close = 0%)'
                reference_label = 'Last Close Reference (0%)'
            else:
                y_label = 'Daily Returns % (Previous Close = 0%)'
                reference_label = 'Previous Close Reference'

            self.bottom_chart.setLabel('left', y_label)
            self.bottom_chart.setLabel('bottom', 'Bar Index (Last 25% of Data)')
            self.bottom_chart.setTitle('Average Range Chart - OHLC Bars', color="#ffffff", size="12pt")

            # Configure x-axis to show indices
            x_axis = self.bottom_chart.getAxis('bottom')
            x_indices = list(range(len(last_25_closes)))
            x_axis.setTicks([[(i, str(i)) for i in x_indices]])

            # Add horizontal line at 0% reference
            if self.rebase_last_radio.isChecked():
                # Only show reference line when rebasing to last close
                zero_line = self.bottom_chart.plot(
                    [0, len(last_25_closes)-1], [0, 0],
                    pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
                    name=reference_label
                )

            # Add statistical lines (white solid)
            self._add_statistical_lines(daily_returns_data, offset, len(last_25_closes), opens, highs, lows, closes)

            # Anchor the chart after all data is loaded (like A button)
            self._anchor_chart_after_data(self.bottom_chart)

            self.logger.info(f"Average range chart updated with {len(last_25_closes)} OHLC bars")

        except Exception as e:
            self.logger.error(f"Error updating average range chart: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    @TypedSlotDecorator.typed_slot(dict)
    def on_market_data_updated(self, market_data: Dict[str, Any]) -> None:
        """
        Handle market data updates with enterprise-grade error handling.
        Uses the same method name and pattern as data_tab.py for consistency.

        Args:
            market_data: Market data from backend service
        """
        try:
            self.logger.info("*** OPTION ANALYZER: on_market_data_updated CALLED ***")
            self.logger.info(f"Market data keys: {list(market_data.keys()) if market_data else 'None'}")

            # Check table_rows specifically
            table_rows = market_data.get("table_rows", [])
            self.logger.info(f"table_rows length: {len(table_rows)}")

            if table_rows:
                self.logger.info(f"First row: {table_rows[0]}")
                if len(table_rows[0]) > 7:
                    self.logger.info(f"Peak/Trough value in first row: '{table_rows[0][7]}'")

                # Check multiple rows to see if ANY have Peak/Trough data
                peak_trough_found = False
                for i in range(min(10, len(table_rows))):
                    row = table_rows[i]
                    if len(row) > 7 and row[7] and row[7].strip():
                        self.logger.info(f"Row {i} has Peak/Trough: '{row[7]}'")
                        peak_trough_found = True

                if not peak_trough_found:
                    self.logger.warning("NO Peak/Trough data found in first 10 rows - all empty!")
                    # Check if data is in a different column
                    for col_idx in range(len(table_rows[0])):
                        sample_val = str(table_rows[0][col_idx]).strip()
                        if sample_val and (sample_val.startswith('H') or sample_val.startswith('L')):
                            self.logger.info(f"Found potential Peak/Trough data in column {col_idx}: '{sample_val}'")

            # Store the data (same as data_tab.py pattern)
            self.current_data = market_data

            # Update bar charts with new data
            self._update_distribution_charts()

        except Exception as e:
            self.logger.error(f"Error processing market data update: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _update_distribution_charts(self):
        """
        Update peak and trough distribution bar charts.
        Uses the same data source as data_tab.py (table_rows) for consistency.
        Implements zero-computation UI following enterprise patterns.
        """
        if not self.current_data:
            self.logger.warning("No data available for distribution charts")
            return

        try:
            # Since table_rows Peak/Trough column is empty, use crosshair_info_lookup instead
            crosshair_info = self.current_data.get("crosshair_info_lookup", {})

            if not crosshair_info:
                self.logger.warning("No crosshair_info_lookup data available")
                return

            self.logger.info(f"Processing {len(crosshair_info)} crosshair entries for distribution charts")

            # Extract peak/trough categories from crosshair_info_lookup
            # Only include actual peaks/troughs, not all cycle positions
            peak_categories = []
            trough_categories = []

            for key, info in crosshair_info.items():
                category = info.get("category", "")
                if category and category.strip():
                    # Handle current/active peaks/troughs marked with ⇨ symbol
                    is_current = category.startswith('⇨')
                    clean_category = category.replace('⇨', '') if is_current else category

                    # Check if this is an actual peak or trough
                    # We need to determine if this candle is at a cycle extreme
                    candle_index = info.get("candle_index", -1)

                    # Check if it's a peak (starts with H) or trough (starts with L)
                    if clean_category.startswith('H'):
                        # For peaks, check if this is at a cycle high
                        if self._is_actual_peak(candle_index, info):
                            # Keep the original category (with ⇨ if present)
                            peak_categories.append(category)
                    elif clean_category.startswith('L'):
                        # For troughs, check if this is at a cycle low
                        if self._is_actual_trough(candle_index, info):
                            # Keep the original category (with ⇨ if present)
                            trough_categories.append(category)

            self.logger.info(f"Extracted {len(peak_categories)} peak categories and {len(trough_categories)} trough categories")
            self.logger.info(f"Peak categories: {peak_categories[:10]}")  # Show first 10
            self.logger.info(f"Trough categories: {trough_categories[:10]}")  # Show first 10

            # Debug: Check for ⇨ symbol specifically
            current_peaks = [cat for cat in peak_categories if '⇨' in cat]
            current_troughs = [cat for cat in trough_categories if '⇨' in cat]
            self.logger.info(f"Current peaks with ⇨: {current_peaks}")
            self.logger.info(f"Current troughs with ⇨: {current_troughs}")

            # Update charts with extracted categories
            self._update_peak_distribution_chart_from_categories(peak_categories)
            self._update_trough_distribution_chart_from_categories(trough_categories)

            # Update bottom chart based on current mode
            self._update_bottom_chart_content()

            self.logger.info("Distribution charts updated successfully")

        except Exception as e:
            self.logger.error(f"Error updating distribution charts: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _update_peak_distribution_chart_from_categories(self, peak_categories: List[str]):
        """
        Update peak distribution bar chart from pre-extracted categories.

        Args:
            peak_categories: List of peak category strings (H1, H2, H3, etc.)
        """
        if not self.top_left_chart:
            self.logger.warning("Top left chart not available")
            return

        try:
            # Clear existing chart
            self.top_left_chart.clear()

            if not peak_categories:
                self.logger.warning("No peak categories available")
                return

            self.logger.info(f"Processing {len(peak_categories)} peak categories for distribution chart")

            # Count occurrences of each category
            category_counts = Counter(peak_categories)
            self.logger.info(f"Peak category counts: {dict(category_counts)}")

            if category_counts:
                # Prepare data for bar chart - handle ⇨ symbol in sorting
                def sort_key(cat):
                    clean_cat = cat.replace('⇨', '')
                    return int(clean_cat[1:])

                categories = sorted(category_counts.keys(), key=sort_key)
                counts = [category_counts[cat] for cat in categories]
                x_positions = list(range(len(categories)))

                # Validate counts to prevent NaN values
                import math
                valid_counts = []
                valid_categories = []
                valid_x_positions = []

                for i, (count, category) in enumerate(zip(counts, categories)):
                    if isinstance(count, (int, float)) and not math.isnan(count) and count >= 0:
                        valid_counts.append(count)
                        valid_categories.append(category)
                        valid_x_positions.append(len(valid_counts) - 1)  # Reindex for valid data
                    else:
                        self.logger.warning(f"Skipping invalid count for category {category}: {count}")

                if not valid_counts:
                    self.logger.warning("No valid peak counts found - skipping peak chart")
                    return

                self.logger.info(f"Creating peak bar chart with categories: {valid_categories}, counts: {valid_counts}")

                # Create bar graph with validated data
                bar_item = BarGraphItem(
                    x=valid_x_positions,
                    height=valid_counts,
                    width=0.6,
                    brush=pg.mkBrush(color="#4CAF50")  # Green for peaks
                )
                self.top_left_chart.addItem(bar_item)

                # Add labels above each bar
                total_peaks = sum(valid_counts)
                max_count = max(valid_counts) if valid_counts else 0

                for i, (count, category) in enumerate(zip(valid_counts, valid_categories)):
                    if count > 0 and total_peaks > 0:
                        # Calculate percentage of all peaks
                        percentage = (count / total_peaks) * 100

                        # Create label text
                        label_text = f"{count}\n{percentage:.1f}%"

                        # Position label just above the bar (use validated max_count)
                        label_y = count + (max_count * 0.05)  # Small offset above bar

                        # Add text item
                        text_item = pg.TextItem(text=label_text, color='white', anchor=(0.5, 0.5))
                        text_item.setPos(i, label_y)
                        self.top_left_chart.addItem(text_item)

                # Set x-axis labels with custom formatting for ⇨ symbol and cycle percentages
                x_axis = self.top_left_chart.getAxis('bottom')

                # Find the overall latest extreme (peak OR trough)
                latest_extreme = self._find_latest_extreme()

                # Calculate cumulative cycle percentages using the validated count data
                # Create custom tick labels - add ⇨ and cumulative cycle percentages
                tick_labels = []
                for i, cat in enumerate(valid_categories):
                    clean_cat = cat.replace('⇨', '')

                    # Calculate cumulative percentage: sum of this category + all higher categories
                    cumulative_count = sum(valid_counts[j] for j in range(i, len(valid_counts)))
                    total_count = sum(valid_counts)
                    cumulative_percentage = (cumulative_count / total_count * 100) if total_count > 0 else 0

                    if (latest_extreme['type'] == 'peak' and
                        clean_cat == latest_extreme['category']):
                        # This is the latest extreme and it's a peak
                        tick_labels.append((i, f"⇨{clean_cat} ({cumulative_percentage:.0f}%)"))
                    else:
                        # Regular completed peak
                        tick_labels.append((i, f"{clean_cat} ({cumulative_percentage:.0f}%)"))

                x_axis.setTicks([tick_labels])

                # Set chart labels
                self.top_left_chart.setLabel('left', 'Count')
                self.top_left_chart.setLabel('bottom', 'Peak Category')

                # Anchor the chart after all data is loaded (like A button)
                self._anchor_chart_after_data(self.top_left_chart)

                self.logger.info(f"Peak distribution chart updated with {len(valid_categories)} categories")
            else:
                self.logger.warning("No valid peak categories found")

        except Exception as e:
            self.logger.error(f"Error updating peak distribution chart: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _update_trough_distribution_chart_from_categories(self, trough_categories: List[str]):
        """
        Update trough distribution bar chart from pre-extracted categories.

        Args:
            trough_categories: List of trough category strings (L1, L2, L3, etc.)
        """
        if not self.top_right_chart:
            self.logger.warning("Top right chart not available")
            return

        try:
            # Clear existing chart
            self.top_right_chart.clear()

            if not trough_categories:
                self.logger.warning("No trough categories available")
                return

            self.logger.info(f"Processing {len(trough_categories)} trough categories for distribution chart")

            # Count occurrences of each category
            category_counts = Counter(trough_categories)
            self.logger.info(f"Trough category counts: {dict(category_counts)}")

            if category_counts:
                # Prepare data for bar chart - handle ⇨ symbol in sorting
                def sort_key(cat):
                    clean_cat = cat.replace('⇨', '')
                    return int(clean_cat[1:])

                categories = sorted(category_counts.keys(), key=sort_key)
                counts = [category_counts[cat] for cat in categories]
                x_positions = list(range(len(categories)))

                # Validate counts to prevent NaN values
                import math
                valid_counts = []
                valid_categories = []
                valid_x_positions = []

                for i, (count, category) in enumerate(zip(counts, categories)):
                    if isinstance(count, (int, float)) and not math.isnan(count) and count >= 0:
                        valid_counts.append(count)
                        valid_categories.append(category)
                        valid_x_positions.append(len(valid_counts) - 1)  # Reindex for valid data
                    else:
                        self.logger.warning(f"Skipping invalid count for category {category}: {count}")

                if not valid_counts:
                    self.logger.warning("No valid trough counts found - skipping trough chart")
                    return

                self.logger.info(f"Creating trough bar chart with categories: {valid_categories}, counts: {valid_counts}")

                # Create bar graph with validated data
                bar_item = BarGraphItem(
                    x=valid_x_positions,
                    height=valid_counts,
                    width=0.6,
                    brush=pg.mkBrush(color="#F44336")  # Red for troughs
                )
                self.top_right_chart.addItem(bar_item)

                # Add labels above each bar
                total_troughs = sum(valid_counts)
                max_count = max(valid_counts) if valid_counts else 0

                for i, (count, category) in enumerate(zip(valid_counts, valid_categories)):
                    if count > 0 and total_troughs > 0:
                        # Calculate percentage of all troughs
                        percentage = (count / total_troughs) * 100

                        # Create label text
                        label_text = f"{count}\n{percentage:.1f}%"

                        # Position label just above the bar (use validated max_count)
                        label_y = count + (max_count * 0.05)  # Small offset above bar

                        # Add text item
                        text_item = pg.TextItem(text=label_text, color='white', anchor=(0.5, 0.5))
                        text_item.setPos(i, label_y)
                        self.top_right_chart.addItem(text_item)

                # Set x-axis labels with custom formatting for ⇨ symbol and cycle percentages
                x_axis = self.top_right_chart.getAxis('bottom')

                # Find the overall latest extreme (peak OR trough)
                latest_extreme = self._find_latest_extreme()

                # Calculate cumulative cycle percentages using the validated count data
                # Create custom tick labels - add ⇨ and cumulative cycle percentages
                tick_labels = []
                for i, cat in enumerate(valid_categories):
                    clean_cat = cat.replace('⇨', '')

                    # Calculate cumulative percentage: sum of this category + all deeper categories
                    cumulative_count = sum(valid_counts[j] for j in range(i, len(valid_counts)))
                    total_count = sum(valid_counts)
                    cumulative_percentage = (cumulative_count / total_count * 100) if total_count > 0 else 0

                    if (latest_extreme['type'] == 'trough' and
                        clean_cat == latest_extreme['category']):
                        # This is the latest extreme and it's a trough
                        tick_labels.append((i, f"⇨{clean_cat} ({cumulative_percentage:.0f}%)"))
                    else:
                        # Regular completed trough
                        tick_labels.append((i, f"{clean_cat} ({cumulative_percentage:.0f}%)"))

                x_axis.setTicks([tick_labels])

                # Set chart labels
                self.top_right_chart.setLabel('left', 'Count')
                self.top_right_chart.setLabel('bottom', 'Trough Category')

                # Anchor the chart after all data is loaded (like A button)
                self._anchor_chart_after_data(self.top_right_chart)

                self.logger.info(f"Trough distribution chart updated with {len(categories)} categories")
            else:
                self.logger.warning("No valid trough categories found")

        except Exception as e:
            self.logger.error(f"Error updating trough distribution chart: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _is_actual_peak(self, candle_index: int, info: Dict[str, Any]) -> bool:
        """
        Determine if this candle is an actual peak (cycle extreme).
        For now, use a simple approach - check if we have peak data from backend.
        """
        # Check if this candle is marked as a peak in the backend data
        peaks = self.current_data.get("peaks", [])
        for peak in peaks:
            if hasattr(peak, 'index') and peak.index == candle_index:
                return True
        return False

    def _is_actual_trough(self, candle_index: int, info: Dict[str, Any]) -> bool:
        """
        Determine if this candle is an actual trough (cycle extreme).
        For now, use a simple approach - check if we have trough data from backend.
        """
        # Check if this candle is marked as a trough in the backend data
        troughs = self.current_data.get("troughs", [])
        for trough in troughs:
            if hasattr(trough, 'index') and trough.index == candle_index:
                return True
        return False

    def _find_latest_peak_category(self) -> str:
        """
        Find the category of the chronologically latest peak.
        """
        if not self.current_data:
            return ""

        crosshair_info = self.current_data.get("crosshair_info_lookup", {})
        latest_peak_index = -1
        latest_peak_category = ""

        for key, info in crosshair_info.items():
            category = info.get("category", "").replace('⇨', '')
            candle_index = info.get("candle_index", -1)

            if category.startswith('H') and candle_index > latest_peak_index:
                if self._is_actual_peak(candle_index, info):
                    latest_peak_index = candle_index
                    latest_peak_category = category

        return latest_peak_category

    def _find_latest_trough_category(self) -> str:
        """
        Find the category of the chronologically latest trough.
        """
        if not self.current_data:
            return ""

        crosshair_info = self.current_data.get("crosshair_info_lookup", {})
        latest_trough_index = -1
        latest_trough_category = ""

        for key, info in crosshair_info.items():
            category = info.get("category", "").replace('⇨', '')
            candle_index = info.get("candle_index", -1)

            if category.startswith('L') and candle_index > latest_trough_index:
                if self._is_actual_trough(candle_index, info):
                    latest_trough_index = candle_index
                    latest_trough_category = category

        return latest_trough_category

    def _find_latest_extreme(self) -> Dict[str, str]:
        """
        Find the single latest extreme (peak OR trough) chronologically.
        Returns dict with 'type' ('peak' or 'trough') and 'category' (e.g., 'H2', 'L1').
        """
        if not self.current_data:
            return {'type': '', 'category': ''}

        crosshair_info = self.current_data.get("crosshair_info_lookup", {})
        latest_index = -1
        latest_extreme = {'type': '', 'category': ''}

        for key, info in crosshair_info.items():
            category = info.get("category", "").replace('⇨', '')
            candle_index = info.get("candle_index", -1)

            # Check if this is a peak or trough and if it's more recent
            if category.startswith('H') and candle_index > latest_index:
                if self._is_actual_peak(candle_index, info):
                    latest_index = candle_index
                    latest_extreme = {'type': 'peak', 'category': category}
            elif category.startswith('L') and candle_index > latest_index:
                if self._is_actual_trough(candle_index, info):
                    latest_index = candle_index
                    latest_extreme = {'type': 'trough', 'category': category}

        self.logger.info(f"Latest extreme: {latest_extreme} at index {latest_index}")
        return latest_extreme

    def _update_chronological_peaks_troughs_chart(self):
        """
        Update bottom chart with chronological sequence of peaks and troughs.
        Each bar shows the rebased high/low value for that specific peak/trough.
        """
        if not self.bottom_chart:
            self.logger.warning("Bottom chart not available")
            return

        try:
            # Clear existing chart
            self.bottom_chart.clear()

            if not self.current_data:
                self.logger.warning("No data available for chronological chart")
                return

            # Get chronologically ordered peaks and troughs
            chronological_extremes = self._get_chronological_extremes()

            if not chronological_extremes:
                self.logger.warning("No chronological extremes found")
                return

            self.logger.info(f"Creating chronological chart with {len(chronological_extremes)} extremes")

            # Apply split sort if enabled
            if self.split_sort_peaks_troughs_checkbox.isChecked():
                chronological_extremes = self._apply_split_sort_to_extremes(chronological_extremes)

            # Prepare data for bar chart with validation
            import math
            valid_extremes = []
            for extreme in chronological_extremes:
                rebased_value = extreme['rebased_value']
                if isinstance(rebased_value, (int, float)) and not math.isnan(rebased_value):
                    valid_extremes.append(extreme)
                else:
                    self.logger.warning(f"Skipping extreme with invalid rebased_value: {extreme}")

            if not valid_extremes:
                self.logger.warning("No valid extremes found for bottom chart")
                return

            categories = [extreme['category'] for extreme in valid_extremes]
            values = [extreme['rebased_value'] for extreme in valid_extremes]
            x_positions = list(range(len(categories)))

            # Create colors: green for peaks (H), red for troughs (L)
            colors = []
            for extreme in valid_extremes:
                if extreme['type'] == 'peak':
                    colors.append("#4CAF50")  # Green for peaks
                else:
                    colors.append("#F44336")  # Red for troughs

            # Create bar graph with individual colors
            for i, (x, height, color) in enumerate(zip(x_positions, values, colors)):
                bar_item = BarGraphItem(
                    x=[x],
                    height=[height],
                    width=0.6,
                    brush=pg.mkBrush(color=color)
                )
                self.bottom_chart.addItem(bar_item)

            # Set x-axis labels
            x_axis = self.bottom_chart.getAxis('bottom')
            x_axis.setTicks([[(i, cat) for i, cat in enumerate(categories)]])

            # Set chart labels
            self.bottom_chart.setLabel('left', 'Rebased Value')
            self.bottom_chart.setLabel('bottom', 'Chronological Sequence')

            # Add average peak and trough lines
            peak_values = [extreme['rebased_value'] for extreme in valid_extremes if extreme['type'] == 'peak']
            trough_values = [extreme['rebased_value'] for extreme in valid_extremes if extreme['type'] == 'trough']

            if peak_values:
                avg_peak_value = sum(peak_values) / len(peak_values)
                # Validate average before adding line
                import math
                if not math.isnan(avg_peak_value):
                    self._add_average_line_to_chart(self.bottom_chart, avg_peak_value, len(categories), f"{avg_peak_value:.2f}%", "above")
                else:
                    self.logger.warning("Average peak value is NaN - skipping average line")

            if trough_values:
                avg_trough_value = sum(trough_values) / len(trough_values)
                # Validate average before adding line
                import math
                if not math.isnan(avg_trough_value):
                    self._add_average_line_to_chart(self.bottom_chart, avg_trough_value, len(categories), f"{avg_trough_value:.2f}%", "below")
                else:
                    self.logger.warning("Average trough value is NaN - skipping average line")

            # Anchor the chart after all data is loaded (like A button)
            self._anchor_chart_after_data(self.bottom_chart)

            self.logger.info(f"Chronological chart updated with {len(categories)} extremes")

        except Exception as e:
            self.logger.error(f"Error updating chronological chart: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

    def _get_chronological_extremes(self) -> List[Dict[str, Any]]:
        """
        Get all peaks and troughs in chronological order with their rebased values.
        Returns list of dicts with 'type', 'category', 'candle_index', 'rebased_value'.
        """
        if not self.current_data:
            return []

        crosshair_info = self.current_data.get("crosshair_info_lookup", {})
        rebased_data = self.current_data.get("rebased_data", [])

        # Debug: Check rebased_data structure
        if rebased_data:
            self.logger.info(f"Rebased data sample: {rebased_data[0]} (type: {type(rebased_data[0])})")

        extremes = []

        for key, info in crosshair_info.items():
            category = info.get("category", "").replace('⇨', '')
            candle_index = info.get("candle_index", -1)

            if category.startswith('H'):
                # This is a peak
                if self._is_actual_peak(candle_index, info):
                    # Get rebased high value
                    if 0 <= candle_index < len(rebased_data):
                        candle_data = rebased_data[candle_index]

                        # Handle different data structures
                        if isinstance(candle_data, dict):
                            rebased_high = candle_data.get('high', 0)
                        elif isinstance(candle_data, (list, tuple)) and len(candle_data) >= 5:
                            # Format: (index, open_pct, high_pct, low_pct, close_pct)
                            rebased_high = candle_data[2]  # High is index 2
                        else:
                            self.logger.warning(f"Unknown rebased_data structure: {type(candle_data)}")
                            continue

                        extremes.append({
                            'type': 'peak',
                            'category': category,
                            'candle_index': candle_index,
                            'rebased_value': rebased_high
                        })
            elif category.startswith('L'):
                # This is a trough
                if self._is_actual_trough(candle_index, info):
                    # Get rebased low value
                    if 0 <= candle_index < len(rebased_data):
                        candle_data = rebased_data[candle_index]

                        # Handle different data structures
                        if isinstance(candle_data, dict):
                            rebased_low = candle_data.get('low', 0)
                        elif isinstance(candle_data, (list, tuple)) and len(candle_data) >= 5:
                            # Format: (index, open_pct, high_pct, low_pct, close_pct)
                            rebased_low = candle_data[3]  # Low is index 3
                        else:
                            self.logger.warning(f"Unknown rebased_data structure: {type(candle_data)}")
                            continue

                        extremes.append({
                            'type': 'trough',
                            'category': category,
                            'candle_index': candle_index,
                            'rebased_value': rebased_low
                        })

        # Sort by candle index (chronological order)
        extremes.sort(key=lambda x: x['candle_index'])

        self.logger.info(f"Found {len(extremes)} chronological extremes")
        return extremes

    def _get_rth_close(self, bar_index: int) -> float:
        """
        Get RTH (Regular Trading Hours) close for the given bar index.
        RTH close times based on timeframe:
        - 1m: 15:59 or 16:59
        - 5m: 15:55 or 16:55
        - 15m: 15:45 or 16:45
        - 30m: 15:30 or 16:30
        - 1h: 15:00 or 16:00
        - Daily: previous row close
        """
        try:
            if not self.current_data or bar_index < 0:
                return 0.0

            closes = self.current_data.get("close", [])
            if bar_index >= len(closes):
                return 0.0

            # For now, return the close at the given index
            # TODO: Implement proper RTH close detection based on timeframe and timestamps
            # This would require timestamp data to identify RTH close times
            return float(closes[bar_index])

        except Exception as e:
            self.logger.error(f"Error getting RTH close: {e}")
            return 0.0

    def _add_statistical_lines(self, daily_returns_data: List[Dict], offset: float, num_bars: int,
                              all_opens: List, all_highs: List, all_lows: List, all_closes: List):
        """
        Add statistical lines to the average range chart.
        - Mean high for bars that closed above 0
        - Mean low for bars that closed below 0
        - Highest high from ALL data (candle with highest daily return high)
        - Lowest low from ALL data (candle with lowest daily return low)
        - 50% levels (highest high/2 and lowest low/2)
        All lines are white and solid.
        """
        try:
            # Check if we have valid data
            if not daily_returns_data or len(daily_returns_data) == 0:
                return

            # Collect data for statistics from 25% data
            highs_above_zero = []
            lows_below_zero = []

            for data in daily_returns_data:
                rebased_high = data['high'] + offset
                rebased_low = data['low'] + offset
                rebased_close = data['close'] + offset

                # Collect highs for bars that closed above 0
                if rebased_close > 0:
                    highs_above_zero.append(rebased_high)

                # Collect lows for bars that closed below 0
                if rebased_close < 0:
                    lows_below_zero.append(rebased_low)

            # Calculate highest high from ALL data
            highest_high_all_data = self._calculate_highest_high_all_data(all_opens, all_highs, all_lows, all_closes)

            # Calculate lowest low from ALL data
            lowest_low_all_data = self._calculate_lowest_low_all_data(all_opens, all_highs, all_lows, all_closes)

            # Calculate statistics
            mean_high_above_zero = None
            mean_low_below_zero = None

            if len(highs_above_zero) > 0:
                mean_high_above_zero = sum(highs_above_zero) / len(highs_above_zero)
                self._add_horizontal_line_with_label(mean_high_above_zero, num_bars, "Mean High (Close > 0)", f"{mean_high_above_zero:.2f}%", "above")

            if len(lows_below_zero) > 0:
                mean_low_below_zero = sum(lows_below_zero) / len(lows_below_zero)
                self._add_horizontal_line_with_label(mean_low_below_zero, num_bars, "Mean Low (Close < 0)", f"{mean_low_below_zero:.2f}%", "below")

            # Add white transparent zone between the two averages
            if mean_high_above_zero is not None and mean_low_below_zero is not None:
                self._add_transparent_zone(mean_low_below_zero, mean_high_above_zero, num_bars)

            # Highest high from all data
            if highest_high_all_data is not None:
                self._add_horizontal_line_with_label(highest_high_all_data, num_bars, "Highest High (All Data)", f"{highest_high_all_data:.2f}%", "above")

                # 50% level of highest high
                highest_high_50 = highest_high_all_data / 2.0
                self._add_horizontal_line_with_label(highest_high_50, num_bars, "Highest High 50%", f"{highest_high_50:.2f}%", "above")

            # Lowest low from ALL data (candle with lowest daily return low)
            if lowest_low_all_data is not None:
                self._add_horizontal_line_with_label(lowest_low_all_data, num_bars, "Lowest Low (All Data)", f"{lowest_low_all_data:.2f}%", "below")

                # 50% level of lowest low
                lowest_low_50 = lowest_low_all_data / 2.0
                self._add_horizontal_line_with_label(lowest_low_50, num_bars, "Lowest Low 50%", f"{lowest_low_50:.2f}%", "below")

        except Exception as e:
            self.logger.error(f"Error adding statistical lines: {e}")

    def _calculate_highest_high_all_data(self, all_opens: List, all_highs: List, all_lows: List, all_closes: List) -> float:
        """
        Calculate the highest daily return high from ALL data (not just 25%).
        This finds the candle with the highest daily return high value across the entire dataset.
        """
        try:
            # Check if we have valid data arrays
            if (all_highs is None or all_closes is None or
                len(all_highs) == 0 or len(all_closes) == 0):
                return None

            highest_daily_return_high = float('-inf')

            # Calculate daily return high for each candle in ALL data
            for i in range(len(all_highs)):
                # Get previous close for daily return calculation
                if i == 0:
                    # For first candle, use its open as reference (or skip)
                    continue
                else:
                    # Use previous day's RTH close
                    prev_close = self._get_rth_close(i - 1)

                if prev_close == 0:
                    continue

                # Calculate daily return high
                daily_return_high = ((all_highs[i] / prev_close) - 1.0) * 100.0

                # Track the highest daily return high
                if daily_return_high > highest_daily_return_high:
                    highest_daily_return_high = daily_return_high

            # Apply the same rebasing logic as the chart
            if self.rebase_last_radio.isChecked():
                # Need to calculate what the offset would be for this value
                # For now, return the raw highest daily return high
                # TODO: Apply proper offset calculation for consistency
                return highest_daily_return_high
            else:
                # No offset needed for previous day close mode
                return highest_daily_return_high

        except Exception as e:
            self.logger.error(f"Error calculating highest high from all data: {e}")
            return None

    def _calculate_lowest_low_all_data(self, all_opens: List, all_highs: List, all_lows: List, all_closes: List) -> float:
        """
        Calculate the lowest daily return low from ALL data.
        This finds the candle with the lowest daily return low value across the entire dataset.
        """
        try:
            # Check if we have valid data arrays
            if (all_lows is None or all_closes is None or
                len(all_lows) == 0 or len(all_closes) == 0):
                return None

            lowest_daily_return_low = float('inf')

            # Calculate daily return low for each candle in ALL data
            for i in range(len(all_lows)):
                # Get previous close for daily return calculation
                if i == 0:
                    # For first candle, use its open as reference (or skip)
                    continue
                else:
                    # Use previous day's RTH close
                    prev_close = self._get_rth_close(i - 1)

                if prev_close == 0:
                    continue

                # Calculate daily return low
                daily_return_low = ((all_lows[i] / prev_close) - 1.0) * 100.0

                # Track the lowest daily return low
                if daily_return_low < lowest_daily_return_low:
                    lowest_daily_return_low = daily_return_low

            # Apply the same rebasing logic as the chart
            if self.rebase_last_radio.isChecked():
                # Need to calculate what the offset would be for this value
                # For now, return the raw lowest daily return low
                # TODO: Apply proper offset calculation for consistency
                return lowest_daily_return_low
            else:
                # No offset needed for previous day close mode
                return lowest_daily_return_low

        except Exception as e:
            self.logger.error(f"Error calculating lowest low from all data: {e}")
            return None

    def _add_horizontal_line(self, y_value: float, num_bars: int, name: str):
        """Add a white solid horizontal line at the specified y value."""
        try:
            line = self.bottom_chart.plot(
                [0, num_bars - 1], [y_value, y_value],
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.SolidLine),
                name=name
            )
        except Exception as e:
            self.logger.error(f"Error adding horizontal line {name}: {e}")

    def _add_horizontal_line_with_label(self, y_value: float, num_bars: int, name: str, label_text: str, position: str):
        """Add a white solid horizontal line with a left-aligned label."""
        try:
            # Add the line
            line = self.bottom_chart.plot(
                [0, num_bars - 1], [y_value, y_value],
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.SolidLine),
                name=name
            )

            # Add text label
            text_item = pg.TextItem(text=label_text, color='white', anchor=(0, 0.5))

            # Position the label
            if position == "above":
                text_item.setPos(0, y_value + abs(y_value) * 0.05)  # Slightly above the line
            else:  # below
                text_item.setPos(0, y_value - abs(y_value) * 0.05)  # Slightly below the line

            self.bottom_chart.addItem(text_item)

        except Exception as e:
            self.logger.error(f"Error adding horizontal line with label {name}: {e}")

    def _add_transparent_zone(self, y_min: float, y_max: float, num_bars: int):
        """Add a white transparent zone between two y values."""
        try:
            # Create fill between the two levels
            x_data = [0, num_bars - 1, num_bars - 1, 0, 0]
            y_data = [y_min, y_min, y_max, y_max, y_min]

            # Create a filled area with low transparency
            fill_item = self.bottom_chart.plot(
                x_data, y_data,
                fillLevel=y_min,
                brush=pg.mkBrush(color=(255, 255, 255, 30)),  # White with low alpha
                pen=None,
                name="Average Zone"
            )

        except Exception as e:
            self.logger.error(f"Error adding transparent zone: {e}")

    def _add_average_line_to_chart(self, chart: 'PlotWidget', y_value: float, num_bars: int, label_text: str, position: str):
        """Add a white solid horizontal line with label to any chart."""
        try:
            # Validate inputs
            import math
            if not isinstance(y_value, (int, float)) or math.isnan(y_value):
                self.logger.warning(f"Invalid y_value for average line: {y_value}")
                return

            if not isinstance(num_bars, int) or num_bars <= 0:
                self.logger.warning(f"Invalid num_bars for average line: {num_bars}")
                return

            # Add the line
            line = chart.plot(
                [0, num_bars - 1], [y_value, y_value],
                pen=pg.mkPen(color='white', width=1, style=pg.QtCore.Qt.PenStyle.SolidLine),
                name=f"Average: {label_text}"
            )

            # Add text label
            text_item = pg.TextItem(text=label_text, color='white', anchor=(0, 0.5))

            # Position the label with safe calculation
            offset = abs(y_value) * 0.05 if y_value != 0 else 0.1  # Fallback offset for zero values
            if position == "above":
                text_item.setPos(0, y_value + offset)  # Slightly above the line
            else:  # below
                text_item.setPos(0, y_value - offset)  # Slightly below the line

            chart.addItem(text_item)

        except Exception as e:
            self.logger.error(f"Error adding average line to chart: {e}")

    def _apply_split_sort_to_daily_returns(self, daily_returns_data: List[Dict], offset: float) -> List[Dict]:
        """
        Apply split sort to daily returns data.
        - Bars that closed above 0: sorted low to high by their high
        - Bars that closed below 0: sorted highest low to lowest low
        """
        try:
            above_zero = []
            below_zero = []

            # Separate bars based on close relative to 0
            for data in daily_returns_data:
                rebased_close = data['close'] + offset
                if rebased_close > 0:
                    above_zero.append(data)
                else:
                    below_zero.append(data)

            # Sort above zero bars by their high (low to high)
            above_zero.sort(key=lambda x: x['high'] + offset)

            # Sort below zero bars by their low (highest to lowest)
            below_zero.sort(key=lambda x: x['low'] + offset, reverse=True)

            # Combine: above zero first, then below zero
            return above_zero + below_zero

        except Exception as e:
            self.logger.error(f"Error applying split sort to daily returns: {e}")
            return daily_returns_data

    def _apply_split_sort_to_extremes(self, extremes: List[Dict]) -> List[Dict]:
        """
        Apply split sort to chronological extremes.
        - Extremes that are above 0: sorted low to high by their value
        - Extremes that are below 0: sorted highest to lowest by their value
        """
        try:
            above_zero = []
            below_zero = []

            # Separate extremes based on rebased value
            for extreme in extremes:
                if extreme['rebased_value'] > 0:
                    above_zero.append(extreme)
                else:
                    below_zero.append(extreme)

            # Sort above zero extremes by their value (low to high)
            above_zero.sort(key=lambda x: x['rebased_value'])

            # Sort below zero extremes by their value (highest to lowest)
            below_zero.sort(key=lambda x: x['rebased_value'], reverse=True)

            # Combine: above zero first, then below zero
            return above_zero + below_zero

        except Exception as e:
            self.logger.error(f"Error applying split sort to extremes: {e}")
            return extremes

    def _get_total_bullish_cycles(self) -> int:
        """Get total number of bullish cycles (cycles that have peaks)."""
        try:
            if not self.current_data:
                self.logger.debug("No current data for bullish cycles")
                return 1

            crosshair_info = self.current_data.get("crosshair_info", {})
            self.logger.debug(f"Crosshair info has {len(crosshair_info)} items")

            # H1 should be 100% since all bullish cycles have H1
            # So total bullish cycles = number of H1 peaks
            h1_count = 0
            all_peaks = 0

            for key, info in crosshair_info.items():
                category = info.get("category", "").replace('⇨', '')
                if category.startswith('H'):
                    all_peaks += 1
                    if category == 'H1':
                        h1_count += 1
                        self.logger.debug(f"Found H1 peak, count now: {h1_count}")

            self.logger.debug(f"Total peaks found: {all_peaks}, H1 peaks: {h1_count}")
            return max(h1_count, 1)  # At least 1 to avoid division by zero

        except Exception as e:
            self.logger.error(f"Error getting total bullish cycles: {e}")
            return 1

    def _get_total_bearish_cycles(self) -> int:
        """Get total number of bearish cycles (cycles that have troughs)."""
        try:
            if not self.current_data:
                self.logger.debug("No current data for bearish cycles")
                return 1

            crosshair_info = self.current_data.get("crosshair_info", {})

            # L1 should be 100% since all bearish cycles have L1
            # So total bearish cycles = number of L1 troughs
            l1_count = 0
            all_troughs = 0

            for key, info in crosshair_info.items():
                category = info.get("category", "").replace('⇨', '')
                if category.startswith('L'):
                    all_troughs += 1
                    if category == 'L1':
                        l1_count += 1
                        self.logger.debug(f"Found L1 trough, count now: {l1_count}")

            self.logger.debug(f"Total troughs found: {all_troughs}, L1 troughs: {l1_count}")
            return max(l1_count, 1)  # At least 1 to avoid division by zero

        except Exception as e:
            self.logger.error(f"Error getting total bearish cycles: {e}")
            return 1

    def _get_cycles_with_peak_category(self, category: str) -> int:
        """Get number of bullish cycles that have the specified peak category."""
        try:
            if not self.current_data:
                return 0

            crosshair_info = self.current_data.get("crosshair_info", {})
            count = 0

            for key, info in crosshair_info.items():
                cat = info.get("category", "").replace('⇨', '')
                if cat == category:
                    count += 1

            return count

        except Exception as e:
            self.logger.error(f"Error getting cycles with peak category {category}: {e}")
            return 0

    def _get_cycles_with_trough_category(self, category: str) -> int:
        """Get number of bearish cycles that have the specified trough category."""
        try:
            if not self.current_data:
                return 0

            crosshair_info = self.current_data.get("crosshair_info", {})
            count = 0

            for key, info in crosshair_info.items():
                cat = info.get("category", "").replace('⇨', '')
                if cat == category:
                    count += 1

            return count

        except Exception as e:
            self.logger.error(f"Error getting cycles with trough category {category}: {e}")
            return 0

    def _get_cumulative_peak_percentage(self, category: str, total_bullish_cycles: int) -> float:
        """
        Get cumulative percentage for peak category (this category + all higher categories).
        H1 = H1 + H2 + H3 + H4 + ...
        H2 = H2 + H3 + H4 + H5 + ...
        """
        try:
            if not self.current_data or total_bullish_cycles == 0:
                self.logger.debug(f"No data or zero cycles for {category}")
                return 0.0

            # Extract the number from the category (e.g., "H1" -> 1, "H2" -> 2)
            if not category.startswith('H') or len(category) < 2:
                self.logger.debug(f"Invalid peak category format: {category}")
                return 0.0

            try:
                current_level = int(category[1:])
            except ValueError:
                self.logger.debug(f"Could not parse level from category: {category}")
                return 0.0

            crosshair_info = self.current_data.get("crosshair_info", {})
            cumulative_count = 0

            self.logger.debug(f"Calculating cumulative for {category} (level {current_level})")

            # Count all categories at this level and higher
            for key, info in crosshair_info.items():
                cat = info.get("category", "").replace('⇨', '')
                if cat.startswith('H') and len(cat) >= 2:
                    try:
                        level = int(cat[1:])
                        if level >= current_level:  # This level or higher
                            cumulative_count += 1
                            self.logger.debug(f"Found {cat} (level {level}) >= {current_level}, count now: {cumulative_count}")
                    except ValueError:
                        continue

            percentage = (cumulative_count / total_bullish_cycles) * 100.0
            self.logger.debug(f"Cumulative for {category}: {cumulative_count}/{total_bullish_cycles} = {percentage:.1f}%")
            return percentage

        except Exception as e:
            self.logger.error(f"Error getting cumulative peak percentage for {category}: {e}")
            return 0.0

    def _get_cumulative_trough_percentage(self, category: str, total_bearish_cycles: int) -> float:
        """
        Get cumulative percentage for trough category (this category + all deeper categories).
        L1 = L1 + L2 + L3 + L4 + ...
        L2 = L2 + L3 + L4 + L5 + ...
        """
        try:
            if not self.current_data or total_bearish_cycles == 0:
                self.logger.debug(f"No data or zero cycles for {category}")
                return 0.0

            # Extract the number from the category (e.g., "L1" -> 1, "L2" -> 2)
            if not category.startswith('L') or len(category) < 2:
                self.logger.debug(f"Invalid trough category format: {category}")
                return 0.0

            try:
                current_level = int(category[1:])
            except ValueError:
                self.logger.debug(f"Could not parse level from category: {category}")
                return 0.0

            crosshair_info = self.current_data.get("crosshair_info", {})
            cumulative_count = 0

            self.logger.debug(f"Calculating cumulative for {category} (level {current_level})")

            # Count all categories at this level and deeper
            for key, info in crosshair_info.items():
                cat = info.get("category", "").replace('⇨', '')
                if cat.startswith('L') and len(cat) >= 2:
                    try:
                        level = int(cat[1:])
                        if level >= current_level:  # This level or deeper
                            cumulative_count += 1
                            self.logger.debug(f"Found {cat} (level {level}) >= {current_level}, count now: {cumulative_count}")
                    except ValueError:
                        continue

            percentage = (cumulative_count / total_bearish_cycles) * 100.0
            self.logger.debug(f"Cumulative for {category}: {cumulative_count}/{total_bearish_cycles} = {percentage:.1f}%")
            return percentage

        except Exception as e:
            self.logger.error(f"Error getting cumulative trough percentage for {category}: {e}")
            return 0.0

    def _anchor_chart_after_data(self, chart: 'PlotWidget'):
        """
        Anchor chart after data is loaded (like clicking A button).
        This auto-fits the view to all data, then locks it.
        """
        try:
            if not chart:
                return

            # Get plot item and view box
            plot_item = chart.getPlotItem()
            view_box = plot_item.getViewBox()

            # Check if there's valid data to range over
            data_items = plot_item.listDataItems()
            has_valid_data = False

            for item in data_items:
                try:
                    # Check if item has valid, non-NaN data
                    if hasattr(item, 'getData'):
                        x_data, y_data = item.getData()
                        if x_data is not None and y_data is not None:
                            # Check for valid, non-NaN values
                            import numpy as np
                            if (len(x_data) > 0 and len(y_data) > 0 and
                                not np.all(np.isnan(x_data)) and not np.all(np.isnan(y_data))):
                                has_valid_data = True
                                break
                    elif hasattr(item, 'opts') and 'x' in item.opts and 'height' in item.opts:
                        # Bar graph item
                        x_data = item.opts['x']
                        y_data = item.opts['height']
                        if x_data is not None and y_data is not None:
                            import numpy as np
                            if (len(x_data) > 0 and len(y_data) > 0 and
                                not np.all(np.isnan(x_data)) and not np.all(np.isnan(y_data))):
                                has_valid_data = True
                                break
                except Exception as e:
                    self.logger.debug(f"Error checking data item: {e}")
                    continue

            if not has_valid_data:
                self.logger.warning("No valid data found for chart anchoring - skipping auto-range")
                # Still anchor the chart but with a default range
                chart.enableAutoRange("xy", False)
                chart.setMouseEnabled(x=False, y=False)
                view_box.setMouseEnabled(x=False, y=False)
                view_box.enableAutoRange(axis='x', enable=False)
                view_box.enableAutoRange(axis='y', enable=False)
                view_box.setMenuEnabled(False)
                return

            # First auto-fit to all data (like A button does)
            chart.enableAutoRange("xy", True)
            view_box.autoRange()

            # Small delay to ensure auto-range completes
            chart.repaint()

            # Now anchor the view (disable all interaction)
            chart.enableAutoRange("xy", False)
            chart.setMouseEnabled(x=False, y=False)

            # Lock the view box
            view_box.setMouseEnabled(x=False, y=False)
            view_box.enableAutoRange(axis='x', enable=False)
            view_box.enableAutoRange(axis='y', enable=False)
            view_box.setMenuEnabled(False)

            # Set anchored state
            try:
                if hasattr(view_box, 'state'):
                    view_box.state['autoRange'] = [False, False]
                    view_box.state['autoVisibleOnly'] = [False, False]
            except Exception as e:
                self.logger.warning(f"Could not set viewbox state: {e}")

            self.logger.debug(f"Chart anchored after data load")

        except Exception as e:
            self.logger.error(f"Error anchoring chart after data: {e}")

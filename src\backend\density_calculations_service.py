"""
Density Calculations Service - Backend service for density chart calculations
Moved from frontend DensityChart methods to separate calculation logic from visualization
"""

import logging
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple, Set


class DensityCalculationsService:
    """Service for performing density chart calculations."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def calculate_option_averages(self, market_data: Dict[str, Any], price_type: str = 'ask') -> Tuple[float, float, float, float]:
        """Calculate average IVs and prices (bid/ask) from all available options."""
        try:
            if not market_data:
                return 0.12338, 9.58, 0.10708, 4.02  # Default fallback values

            # Try to get options data from various possible keys
            options_data = None
            for key in ['options_data', 'density_options_data', 'options']:
                if key in market_data:
                    options_data = market_data[key]
                    break

            if not options_data:
                return 0.12338, 9.58, 0.10708, 4.02  # Default fallback values

            calls_df = options_data.get('calls', [])
            puts_df = options_data.get('puts', [])

            # Handle both DataFrame and list formats
            if isinstance(calls_df, pd.DataFrame):
                calls_data = calls_df.to_dict('records') if not calls_df.empty else []
            else:
                calls_data = calls_df if calls_df else []

            if isinstance(puts_df, pd.DataFrame):
                puts_data = puts_df.to_dict('records') if not puts_df.empty else []
            else:
                puts_data = puts_df if puts_df else []

            if not calls_data or not puts_data:
                return 0.12338, 9.58, 0.10708, 4.02  # Default fallback values

            # Calculate averages for calls
            call_ivs = []
            call_prices = []
            for call in calls_data:
                if 'iv' in call and call['iv'] is not None:
                    try:
                        iv_decimal = float(call['iv']) / 100.0  # Convert percentage to decimal
                        call_ivs.append(iv_decimal)
                    except:
                        pass
                if price_type in call and call[price_type] is not None:
                    try:
                        call_prices.append(float(call[price_type]))
                    except:
                        pass

            # Calculate averages for puts
            put_ivs = []
            put_prices = []
            for put in puts_data:
                if 'iv' in put and put['iv'] is not None:
                    try:
                        iv_decimal = float(put['iv']) / 100.0  # Convert percentage to decimal
                        put_ivs.append(iv_decimal)
                    except:
                        pass
                if price_type in put and put[price_type] is not None:
                    try:
                        put_prices.append(float(put[price_type]))
                    except:
                        pass

            # Calculate final averages
            avg_call_iv = sum(call_ivs) / len(call_ivs) if call_ivs else 0.12338
            avg_call_price = sum(call_prices) / len(call_prices) if call_prices else 9.58
            avg_put_iv = sum(put_ivs) / len(put_ivs) if put_ivs else 0.10708
            avg_put_price = sum(put_prices) / len(put_prices) if put_prices else 4.02

            self.logger.debug(f"Option averages calculated using {price_type} prices: Call IV={avg_call_iv:.5f}, Call {price_type}={avg_call_price:.2f}, Put IV={avg_put_iv:.5f}, Put {price_type}={avg_put_price:.2f}")

            return avg_call_iv, avg_call_price, avg_put_iv, avg_put_price

        except Exception as e:
            self.logger.error(f"Error calculating option averages: {e}")
            return 0.12338, 9.58, 0.10708, 4.02  # Default fallback values

    def calculate_complex_divisor(self, avg_call_iv, avg_call_price, avg_put_iv, avg_put_price, current_price):
        """Calculate the complex divisor using the specified formula."""
        try:
            # Formula: (avg_call_price + avg_put_price) + (avg_put_iv * avg_call_price) - (avg_put_iv / avg_call_iv) - ((last_close * avg_call_iv * avg_put_iv) / (avg_call_price + avg_put_price))

            # Avoid division by zero
            if avg_call_iv == 0 or (avg_call_price + avg_put_price) == 0:
                return 13.25  # Fallback to original simple divisor

            term1 = avg_call_price + avg_put_price
            term2 = avg_put_iv * avg_call_price
            term3 = avg_put_iv / avg_call_iv
            term4 = (current_price * avg_call_iv * avg_put_iv) / (avg_call_price + avg_put_price)

            divisor = term1 + term2 - term3 - term4

            # Ensure divisor is positive and reasonable
            if divisor <= 0:
                return 13.25  # Fallback to original simple divisor

            # Debug output for divisor calculation
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Complex divisor breakdown: {term1:.1f} + {term2:.6f} - {term3:.6f} - {term4:.6f} = {divisor:.6f}")
            return divisor

        except Exception as e:
            print(f"Error calculating complex divisor: {e}")
            return 13.25  # Fallback to original simple divisor

    def get_filtered_strikes_and_options_data(self, lowest_low: float, highest_high: float,
                                            options_data: Dict[str, Any], selected_expiry: Optional[str] = None) -> Tuple[List[float], Dict[str, Any]]:
        """Get filtered strikes within the projected range and process options data."""
        try:
            if not options_data:
                return [], {}

            calls_df = options_data['calls']
            puts_df = options_data['puts']

            # Filter by selected expiry if specified
            if selected_expiry:
                calls_df = calls_df[calls_df['expiry'] == selected_expiry]
                puts_df = puts_df[puts_df['expiry'] == selected_expiry]

            # Get all available strikes from both calls and puts
            available_strikes = set()

            # Collect all available strikes for the selected expiry
            if not calls_df.empty:
                available_strikes.update(calls_df['strike'].tolist())
            if not puts_df.empty:
                available_strikes.update(puts_df['strike'].tolist())

            # Filter strikes to only those within the projected range
            filtered_strikes = [strike for strike in available_strikes
                              if lowest_low <= strike <= highest_high]

            # Sort strikes for consistent display
            filtered_strikes.sort()

            # Return filtered strikes and processed options data
            processed_options_data = {
                'calls': calls_df,
                'puts': puts_df,
                'filtered_strikes': filtered_strikes
            }

            return filtered_strikes, processed_options_data

        except Exception as e:
            self.logger.error(f"Error getting filtered strikes and options data: {e}")
            return [], {}

    def calculate_options_averages_for_strikes(self, lowest_low: float, highest_high: float,
                                             options_data: Dict[str, Any], filtered_strikes: List[float],
                                             price_type: str = 'ask') -> Dict[str, Any]:
        """Calculate average calculations for options data within the projected range."""
        try:
            if not options_data or not filtered_strikes:
                return {}

            calls_df = options_data.get('calls', pd.DataFrame())
            puts_df = options_data.get('puts', pd.DataFrame())

            if calls_df.empty or puts_df.empty:
                return {}

            # Filter to only strikes within the projected range
            calls_in_range = calls_df[calls_df['strike'].isin(filtered_strikes)]
            puts_in_range = puts_df[puts_df['strike'].isin(filtered_strikes)]

            result = {
                'avg_y_position': lowest_low - ((highest_high - lowest_low) * 0.01),  # 1% below lowest low
                'call_price_avg': None,
                'call_iv_avg': 0.0,
                'put_iv_avg': 0.0,
                'put_price_avg': None,
                'global_average_iv': 0.0
            }

            # Calculate call price average
            if not calls_in_range.empty and price_type in calls_in_range.columns:
                call_prices = calls_in_range[price_type].dropna()
                call_prices = call_prices[call_prices >= 0]  # Include 0 values
                if not call_prices.empty:
                    result['call_price_avg'] = call_prices.mean()

            # Calculate call IV average (include 0 values)
            if not calls_in_range.empty and 'iv' in calls_in_range.columns:
                call_ivs = calls_in_range['iv'].dropna()
                if not call_ivs.empty:
                    result['call_iv_avg'] = call_ivs.mean() / 100.0  # Convert to decimal

            # Calculate put IV average (include 0 values)
            if not puts_in_range.empty and 'iv' in puts_in_range.columns:
                put_ivs = puts_in_range['iv'].dropna()
                if not put_ivs.empty:
                    result['put_iv_avg'] = put_ivs.mean() / 100.0  # Convert to decimal

            # Compute global average IV (mean of avg call IV and avg put IV)
            try:
                result['global_average_iv'] = max(0.0, float((result['call_iv_avg'] + result['put_iv_avg']) / 2.0))
            except Exception:
                result['global_average_iv'] = 0.0

            # Calculate put price average
            if not puts_in_range.empty and price_type in puts_in_range.columns:
                put_prices = puts_in_range[price_type].dropna()
                put_prices = put_prices[put_prices >= 0]  # Allow 0 for puts
                if not put_prices.empty:
                    result['put_price_avg'] = put_prices.mean()

            return result

        except Exception as e:
            self.logger.error(f"Error calculating options averages for strikes: {e}")
            return {}

    def get_options_data_for_strike(self, strike_price: float, options_data: Dict[str, Any],
                                  price_type: str = 'ask', selected_expiry: Optional[str] = None) -> Dict[str, Any]:
        """Get options data (prices and IV) for a specific strike price."""
        try:
            if not options_data:
                return {}

            calls_df = options_data.get('calls', pd.DataFrame())
            puts_df = options_data.get('puts', pd.DataFrame())

            # Filter by selected expiry if specified
            if selected_expiry is not None:
                if not calls_df.empty:
                    calls_df = calls_df[calls_df['expiry'] == selected_expiry]
                if not puts_df.empty:
                    puts_df = puts_df[puts_df['expiry'] == selected_expiry]

            result = {
                'call_data': None,
                'put_data': None,
                'call_price': None,
                'call_iv': None,
                'put_price': None,
                'put_iv': None,
                'logs': []  # capture NaN->0 conversions
            }

            # Helper to check NaN-like
            def _is_nan_like(val: Any) -> bool:
                s = str(val).lower()
                return s in ['nan', 'none', 'n/a', '-', '']

            # Find call option for this exact strike
            if not calls_df.empty:
                call_matches = calls_df[calls_df['strike'] == strike_price]
                if not call_matches.empty:
                    call_data = call_matches.iloc[0]
                    result['call_data'] = call_data

                    # Extract call price
                    if price_type in call_data:
                        price_value = call_data[price_type]
                        if price_value is not None and not _is_nan_like(price_value) and float(price_value) >= 0:
                            result['call_price'] = float(price_value)

                    # Extract call IV
                    if 'iv' in call_data and call_data['iv'] is not None:
                        iv_value = call_data['iv']
                        try:
                            if _is_nan_like(iv_value):
                                iv_float = 0.0
                                result['logs'].append(f"Call IV for strike {strike_price}: '{iv_value}' -> 0.000")
                            else:
                                iv_float = float(iv_value)
                            result['call_iv'] = iv_float / 100.0
                        except (ValueError, TypeError):
                            pass

            # Find put option for this exact strike
            if not puts_df.empty:
                put_matches = puts_df[puts_df['strike'] == strike_price]
                if not put_matches.empty:
                    put_data = put_matches.iloc[0]
                    result['put_data'] = put_data

                    # Extract put price
                    if price_type in put_data:
                        price_value = put_data[price_type]
                        if price_value is not None and not _is_nan_like(price_value) and float(price_value) >= 0:
                            result['put_price'] = float(price_value)

                    # Extract put IV
                    if 'iv' in put_data and put_data['iv'] is not None:
                        iv_value = put_data['iv']
                        try:
                            if _is_nan_like(iv_value):
                                iv_float = 0.0
                                result['logs'].append(f"Put IV for strike {strike_price}: '{iv_value}' -> 0.000")
                            else:
                                iv_float = float(iv_value)
                            result['put_iv'] = iv_float / 100.0
                        except (ValueError, TypeError):
                            pass

            return result

        except Exception as e:
            self.logger.error(f"Error getting options data for strike {strike_price}: {e}")
            return {}

    def calculate_density_data(self, filtered_high_data: List, filtered_low_data: List,
                             market_data: Optional[Dict[str, Any]] = None,
                             price_type: str = 'ask', selected_expiry: Optional[str] = None) -> Dict[str, Any]:
        """
        Calculate all density chart data including statistics, options data, and positioning.

        Args:
            filtered_high_data: List of filtered high data rows
            filtered_low_data: List of filtered low data rows
            market_data: Market data dictionary containing ticker, timeframe, options data, etc.
            price_type: Price type for options ('ask' or 'bid')
            selected_expiry: Selected expiry for options filtering

        Returns:
            Dictionary containing all calculated values for visualization
        """
        try:
            result = {
                'title_info': {},
                'current_close': None,
                'projected_highs': [],
                'projected_lows': [],
                'lowest_low': None,
                'highest_high': None,
                'highs_stats': {},
                'lows_stats': {},
                'options_data': None,
                'filtered_strikes': [],
                'options_averages': {},
                'strike_options_data': {},
                'complex_divisor': 13.25,
                'axis_limits': {'x_min': -5.5, 'x_max': 5, 'y_min': 0, 'y_max': 100},
                'title_position': {'x': -5.25, 'y': 0}
            }

            # Calculate title information
            if market_data:
                result['title_info'] = self._calculate_title_info(
                    filtered_high_data, filtered_low_data, market_data
                )

            # Get current close price
            result['current_close'] = self._extract_current_close(market_data)

            # Extract projected highs and lows
            projected_highs, projected_lows = self._extract_projected_data(
                filtered_high_data, filtered_low_data
            )

            result['projected_highs'] = projected_highs
            result['projected_lows'] = projected_lows

            if projected_highs and projected_lows:
                result['lowest_low'] = min(projected_lows)
                result['highest_high'] = max(projected_highs)

                # Calculate statistical values
                result['highs_stats'] = self._calculate_highs_statistics(projected_highs)
                result['lows_stats'] = self._calculate_lows_statistics(projected_lows)

                # Calculate axis limits and positioning
                result['axis_limits'] = self._calculate_axis_limits(result['highest_high'], result['lowest_low'])
                result['title_position'] = self._calculate_title_position(result['highest_high'], result['lowest_low'])

            # Process options data if available
            if market_data and result['lowest_low'] is not None and result['highest_high'] is not None:
                options_data = self._get_options_data_from_market_data(market_data)
                if options_data:
                    result['options_data'] = options_data

                    # Get filtered strikes
                    filtered_strikes, processed_options_data = self.get_filtered_strikes_and_options_data(
                        result['lowest_low'], result['highest_high'], options_data, selected_expiry
                    )
                    result['filtered_strikes'] = filtered_strikes

                    if filtered_strikes:
                        # Calculate options averages
                        result['options_averages'] = self.calculate_options_averages_for_strikes(
                            result['lowest_low'], result['highest_high'], processed_options_data,
                            filtered_strikes, price_type
                        )

                        # Get options data for each strike
                        strike_options_data = {}
                        for strike in filtered_strikes:
                            strike_options_data[strike] = self.get_options_data_for_strike(
                                strike, processed_options_data, price_type
                            )
                        result['strike_options_data'] = strike_options_data

                        # Calculate complex divisor
                        if result['current_close'] is not None:
                            avg_call_iv, avg_call_price, avg_put_iv, avg_put_price = self.calculate_option_averages(
                                market_data, price_type
                            )
                            result['complex_divisor'] = self.calculate_complex_divisor(
                                avg_call_iv, avg_call_price, avg_put_iv, avg_put_price, result['current_close']
                            )

            self.logger.debug(f"Density calculations completed successfully")
            return result

        except Exception as e:
            self.logger.error(f"Error in density calculations: {e}")
            import traceback
            traceback.print_exc()
            return result

    def _calculate_title_info(self, filtered_high_data: List, filtered_low_data: List,
                            market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate title information including occurrences."""
        try:
            ticker = market_data.get("ticker", "Unknown")
            timeframe = market_data.get("timeframe", "Unknown")
            dtl = market_data.get("dtl", 0)
            length = market_data.get("length", 0)

            # Calculate occurrences using the formula: (occurrences / 2) - 1
            total_occurrences = 0
            if filtered_high_data:
                total_occurrences += len(filtered_high_data)
            if filtered_low_data:
                total_occurrences += len(filtered_low_data)

            occurrences = int((total_occurrences / 2) - 1) if total_occurrences > 0 else 0

            return {
                'ticker': ticker,
                'timeframe': timeframe,
                'dtl': dtl,
                'length': length,
                'occurrences': occurrences,
                'title_text': f'{ticker} Length: [{length}] {timeframe} Aggregations: {dtl}\noccurrence: {occurrences}'
            }

        except Exception as e:
            self.logger.error(f"Error calculating title info: {e}")
            return {}

    def _extract_current_close(self, market_data: Optional[Dict[str, Any]]) -> Optional[float]:
        """Extract current close price from market data."""
        try:
            if not market_data:
                return None

            close_prices = market_data.get("close", [])
            if close_prices is not None and len(close_prices) > 0:
                if hasattr(close_prices, '__len__') and len(close_prices) > 0:
                    return float(close_prices[-1])
            return None

        except Exception as e:
            self.logger.error(f"Error extracting current close: {e}")
            return None

    def _extract_projected_data(self, filtered_high_data: List, filtered_low_data: List) -> Tuple[List[float], List[float]]:
        """Extract projected highs and lows from filtered data."""
        projected_highs = []
        projected_lows = []

        # Extract projected highs from filtered high data
        try:
            if filtered_high_data:
                for row in filtered_high_data:
                    if len(row) > 6:  # Projected High is at index 6
                        try:
                            projected_high_str = str(row[6])
                            if projected_high_str and projected_high_str != '' and projected_high_str != 'None':
                                projected_high = float(projected_high_str)
                                projected_highs.append(projected_high)
                        except (ValueError, TypeError):
                            continue
        except Exception as e:
            self.logger.error(f"Error extracting projected highs: {e}")

        # Extract projected lows from filtered low data
        try:
            if filtered_low_data:
                for row in filtered_low_data:
                    if len(row) > 6:  # Projected Low is at index 6
                        try:
                            projected_low_str = str(row[6])
                            if projected_low_str and projected_low_str != '' and projected_low_str != 'None':
                                projected_low = float(projected_low_str)
                                projected_lows.append(projected_low)
                        except (ValueError, TypeError):
                            continue
        except Exception as e:
            self.logger.error(f"Error extracting projected lows: {e}")

        return projected_highs, projected_lows

    def _calculate_highs_statistics(self, projected_highs: List[float]) -> Dict[str, float]:
        """Calculate statistical values for projected highs."""
        highs_stats = {}

        if projected_highs:
            try:
                import statistics
                sorted_highs = sorted(projected_highs)
                n_highs = len(sorted_highs)

                # Average of all highs
                highs_stats['average'] = statistics.mean(sorted_highs)

                # Median of all highs
                highs_stats['median'] = statistics.median(sorted_highs)

                # MaxAvg: highest 50% highs average
                top_50_count = max(1, n_highs // 2)
                highs_stats['max_avg'] = statistics.mean(sorted_highs[-top_50_count:])

                # MinAvg: lowest 50% highs average
                bottom_50_count = max(1, n_highs // 2)
                highs_stats['min_avg'] = statistics.mean(sorted_highs[:bottom_50_count])

            except Exception as e:
                self.logger.error(f"Error calculating highs statistics: {e}")

        return highs_stats

    def _calculate_lows_statistics(self, projected_lows: List[float]) -> Dict[str, float]:
        """Calculate statistical values for projected lows."""
        lows_stats = {}

        if projected_lows:
            try:
                import statistics
                sorted_lows = sorted(projected_lows)
                n_lows = len(sorted_lows)

                # Average of all lows
                lows_stats['average'] = statistics.mean(sorted_lows)

                # Median of all lows
                lows_stats['median'] = statistics.median(sorted_lows)

                # MinAvg: highest 50% average low (highest values among lows)
                top_50_count = max(1, n_lows // 2)
                lows_stats['min_avg'] = statistics.mean(sorted_lows[-top_50_count:])

                # MaxAvg: lowest 50% average low (lowest values among lows)
                bottom_50_count = max(1, n_lows // 2)
                lows_stats['max_avg'] = statistics.mean(sorted_lows[:bottom_50_count])

            except Exception as e:
                self.logger.error(f"Error calculating lows statistics: {e}")

        return lows_stats

    def _calculate_axis_limits(self, highest_high: float, lowest_low: float) -> Dict[str, float]:
        """Calculate axis limits with padding."""
        try:
            # Calculate range and add 10% padding
            price_range = highest_high - lowest_low
            padding = price_range * 0.10  # 10% of range

            return {
                'x_min': -5.5,
                'x_max': 5,
                'y_min': lowest_low - padding,
                'y_max': highest_high + padding
            }

        except Exception as e:
            self.logger.error(f"Error calculating axis limits: {e}")
            return {'x_min': -5.5, 'x_max': 5, 'y_min': 0, 'y_max': 100}

    def calculate_axis_limits(self, highest_high: float, lowest_low: float) -> Dict[str, float]:
        """Public wrapper to calculate axis limits with padding."""
        return self._calculate_axis_limits(highest_high, lowest_low)

    def calculate_title_position(self, highest_high: float, lowest_low: float) -> Dict[str, float]:
        """Public wrapper to calculate title position."""
        return self._calculate_title_position(highest_high, lowest_low)

    def calculate_current_price_label_y(self, lowest_low: float, highest_high: float, current_close: float, offset_pct: float = 0.02) -> float:
        """Calculate Y position for the current price label as offset percentage above current_close."""
        try:
            price_range = highest_high - lowest_low
            return float(current_close + (price_range * offset_pct))
        except Exception as e:
            self.logger.error(f"Error calculating current price label Y: {e}")
            return float(current_close)

    def _calculate_title_position(self, highest_high: float, lowest_low: float) -> Dict[str, float]:
        """Calculate title position at 7.5% above highest_high."""
        try:
            # Calculate 7.5% of the range above highest high
            price_range = highest_high - lowest_low
            range_offset = price_range * 0.075  # 7.5% of the range
            title_y = highest_high + range_offset

            return {'x': -5.25, 'y': title_y}

        except Exception as e:
            self.logger.error(f"Error calculating title position: {e}")
            return {'x': -5.25, 'y': 0}

    def calculate_strikes_title_y(self, lowest_low: float, highest_high: float) -> float:
        """Calculate Y position for 'Strikes' title: 1% above highest high."""
        try:
            price_range = highest_high - lowest_low
            return float(highest_high + (price_range * 0.01))
        except Exception as e:
            self.logger.error(f"Error calculating strikes title Y: {e}")
            return float(highest_high)

    def _get_options_data_from_market_data(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract options data from market data."""
        try:
            # Try to get options data from various possible keys
            for key in ['options_data', 'density_options_data', 'options']:
                if key in market_data:
                    return market_data[key]
            return None

        except Exception as e:
            self.logger.error(f"Error getting options data from market data: {e}")
            return None

    def compute_profit_loss_curves(self,
                                   strike_price: float,
                                   options_data: Dict[str, Any],
                                   price_type: str,
                                   selected_expiry: Optional[str],
                                   market_data: Optional[Dict[str, Any]],
                                   cached_divisor: Optional[float],
                                   cached_divisor_price_type: Optional[str]) -> Dict[str, Any]:
        """Compute profit/loss curve points for call and put without any plotting.
        Returns dict with divisor status and curve points.
        """
        try:
            if not options_data:
                return {}

            import numpy as np
            # Prepare options frames
            calls_df = options_data.get('calls', pd.DataFrame())
            puts_df = options_data.get('puts', pd.DataFrame())

            # Filter by selected expiry if specified
            if selected_expiry:
                if not calls_df.empty:
                    calls_df = calls_df[calls_df['expiry'] == selected_expiry]
                if not puts_df.empty:
                    puts_df = puts_df[puts_df['expiry'] == selected_expiry]

            # Find call/put for this strike
            call_data = None
            put_data = None
            if not calls_df.empty:
                m = calls_df[calls_df['strike'] == strike_price]
                if not m.empty:
                    call_data = m.iloc[0]
            if not puts_df.empty:
                m = puts_df[puts_df['strike'] == strike_price]
                if not m.empty:
                    put_data = m.iloc[0]

            # Determine divisor and whether it was updated
            divisor_updated = False
            divisor = cached_divisor if cached_divisor is not None else 13.25
            if cached_divisor_price_type != price_type:
                # recalc using backend averages
                if market_data:
                    current_price = market_data.get('zero_percent_price', 643.44)
                    avg_call_iv, avg_call_price, avg_put_iv, avg_put_price = self.calculate_option_averages(market_data, price_type)
                    new_divisor = self.calculate_complex_divisor(avg_call_iv, avg_call_price, avg_put_iv, avg_put_price, current_price)
                    if new_divisor != divisor:
                        divisor = new_divisor
                        divisor_updated = True
                # else keep existing divisor

            def _is_valid_price(v: Any) -> bool:
                try:
                    return v is not None and str(v).lower() not in ['nan', 'none', ''] and float(v) >= 0
                except Exception:
                    return False

            result: Dict[str, Any] = {
                'divisor': float(divisor),
                'divisor_updated': bool(divisor_updated),
                'curves': {
                    'call': None,
                    'put': None,
                },
            }

            # Build call curve
            if call_data is not None and price_type in call_data and _is_valid_price(call_data[price_type]):
                call_price = float(call_data[price_type])
                if divisor > 0:
                    adjustment = call_price / divisor
                    x_points = [-3.5, -3.2, -2.2, -1.825]
                    y_points = [
                        strike_price,
                        strike_price - adjustment,
                        strike_price + call_price + adjustment,
                        strike_price + call_price,
                    ]
                    # Attempt cubic spline
                    smooth = None
                    try:
                        from scipy.interpolate import CubicSpline
                        x_array = np.array(x_points)
                        y_array = np.array(y_points)
                        order = np.argsort(x_array)
                        cs = CubicSpline(x_array[order], y_array[order], bc_type='natural')
                        xs = np.linspace(x_array[order][0], x_array[order][-1], 100)
                        ys = cs(xs)
                        smooth = {'x': xs.tolist(), 'y': ys.tolist()}
                    except Exception:
                        pass
                    result['curves']['call'] = {
                        'base': {'x': x_points, 'y': y_points},
                        'smooth': smooth,
                        'price': call_price,
                    }
                else:
                    # simple 2 point line
                    result['curves']['call'] = {
                        'base': {'x': [-3.5, -1.825], 'y': [strike_price, strike_price + call_price]},
                        'smooth': None,
                        'price': call_price,
                    }

            # Build put curve
            if put_data is not None and price_type in put_data and _is_valid_price(put_data[price_type]):
                put_price = float(put_data[price_type])
                if divisor > 0:
                    adjustment = put_price / divisor
                    x_points = [4, 3.7, 2.35, 2.125]
                    y_points = [
                        strike_price,
                        strike_price + adjustment,
                        strike_price - put_price - adjustment,
                        strike_price - put_price,
                    ]
                    smooth = None
                    try:
                        from scipy.interpolate import CubicSpline
                        x_array = np.array(x_points)
                        y_array = np.array(y_points)
                        order = np.argsort(x_array)


                        cs = CubicSpline(x_array[order], y_array[order], bc_type='natural')
                        xs = np.linspace(x_array[order][0], x_array[order][-1], 100)
                        ys = cs(xs)
                        smooth = {'x': xs.tolist(), 'y': ys.tolist()}
                    except Exception:
                        pass
                    result['curves']['put'] = {
                        'base': {'x': x_points, 'y': y_points},
                        'smooth': smooth,
                        'price': put_price,
                    }
                else:
                    result['curves']['put'] = {
                        'base': {'x': [4, 2.125], 'y': [strike_price, strike_price - put_price]},
                        'smooth': None,
                        'price': put_price,
                    }

            return result

        except Exception as e:
            self.logger.error(f"Error computing P/L curves for strike {strike_price}: {e}")
            import traceback
            traceback.print_exc()
            return {}

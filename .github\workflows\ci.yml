name: Enterprise CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    tags: [ 'v*' ]  # Trigger on version tags for releases
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run nightly benchmarks and dependency checks
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  CMAKE_VERSION: '3.25'
  CMAKE_BUILD_TYPE: Release
  CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
  PERFORMANCE_REGRESSION_THRESHOLD: '5.0'
  
jobs:
  # Code Quality & Linting
  lint:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install Python linting tools
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy pylint bandit safety
        
    - name: Python Code Formatting (Black)
      run: black --check --diff .
      
    - name: Python Import Sorting (isort)
      run: isort --check-only --diff .
      
    - name: <PERSON> Linting (Flake8)
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
      
    - name: Python Type Checking (MyPy)
      run: mypy --ignore-missing-imports backend/ frontend/
      
    - name: Python Security Scan (Bandit)
      run: bandit -r backend/ frontend/ -f json -o bandit-report.json
      continue-on-error: true
      
    - name: Python Dependency Security (Safety)
      run: safety check --json --output safety-report.json
      continue-on-error: true
      
    - name: Install clang-tidy
      run: sudo apt-get update && sudo apt-get install -y clang-tidy
      
    - name: C++ Linting (clang-tidy)
      run: |
        find compute/ -name "*.cpp" -o -name "*.h" | xargs clang-tidy \
          --checks='-*,readability-*,performance-*,modernize-*,bugprone-*' \
          --format-style=file || true
          
    - name: Upload lint reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: lint-reports
        path: |
          bandit-report.json
          safety-report.json

  # Unit & Integration Tests
  test:
    name: Unit & Integration Tests
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.9', '3.10', '3.11', '3.12']
        exclude:
          # Reduce matrix size for faster CI
          - os: macos-latest
            python-version: '3.9'
          - os: windows-latest
            python-version: '3.10'
            
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y qt6-base-dev qt6-tools-dev libgl1-mesa-dev
        
    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install qt6 cmake
        
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-qt pytest-asyncio pytest-mock
        pip install coverage[toml] pytest-html pytest-json-report
        
    - name: Build C++ extensions
      run: |
        cd compute
        python setup.py build_ext --inplace
        cd ..
        
    - name: Run Python unit tests
      run: |
        pytest tests/ -v --cov=backend --cov=frontend \
          --cov-report=xml --cov-report=html \
          --html=pytest-report.html --json-report --json-report-file=pytest-report.json
          
    - name: Run C++ unit tests
      if: matrix.os == 'ubuntu-latest'
      run: |
        cd compute
        if [ -f "test_indicators.cpp" ]; then
          g++ -std=c++17 -O2 test_indicators.cpp -o test_indicators -lgtest -lgtest_main -pthread
          ./test_indicators --gtest_output=xml:cpp-test-results.xml
        fi
        cd ..
        
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.os }}-py${{ matrix.python-version }}
        path: |
          htmlcov/
          pytest-report.html
          pytest-report.json
          coverage.xml
          compute/cpp-test-results.xml

  # Performance Benchmarks
  benchmark:
    name: Performance Benchmarks
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[benchmark]')
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Need history for comparison
        
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y qt6-base-dev libgoogle-benchmark-dev
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark
        
    - name: Build optimized C++ extensions
      run: |
        cd compute
        BUILD_PROFILE=release python setup.py build_ext --inplace
        cd ..
        
    - name: Run Python benchmarks
      run: |
        pytest benchmarks/ --benchmark-json=python-benchmark.json \
          --benchmark-compare-fail=mean:5% --benchmark-compare-fail=stddev:10%
          
    - name: Build and run C++ benchmarks
      run: |
        cd compute
        if [ -f "benchmark.cpp" ]; then
          g++ -std=c++17 -O3 -march=native benchmark.cpp -o benchmark \
            -lbenchmark -lbenchmark_main -pthread
          ./benchmark --benchmark_format=json --benchmark_out=cpp-benchmark.json
        fi
        cd ..
        
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: |
          python-benchmark.json
          compute/cpp-benchmark.json
          
    - name: Performance regression check
      run: |
        python scripts/check_performance_regression.py \
          --current python-benchmark.json \
          --threshold 5.0 \
          --fail-on-regression

  # Security Scanning
  security:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Build & Package
  build:
    name: Build & Package
    runs-on: ${{ matrix.os }}
    needs: [lint, test]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build wheel cibuildwheel
        
    - name: Build wheels
      uses: pypa/cibuildwheel@v2.16.2
      env:
        CIBW_BUILD: cp39-* cp310-* cp311-* cp312-*
        CIBW_SKIP: "*-win32 *-manylinux_i686"
        CIBW_BEFORE_BUILD: pip install pybind11
        
    - name: Upload wheels
      uses: actions/upload-artifact@v3
      with:
        name: wheels-${{ matrix.os }}
        path: wheelhouse/*.whl

  # Release
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [build, benchmark]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/')
      uses: softprops/action-gh-release@v1
      with:
        files: |
          dist/*.whl
          dist/*.tar.gz
          benchmark-results/*.json
        body: |
          ## Release Notes

          ### Performance Benchmarks
          - C++ kernel performance: See attached benchmark results
          - Memory usage optimizations included
          - Cross-platform wheel compatibility verified

          ### Installation
          ```bash
          pip install datadriven-trading==${{ github.ref_name }}
          ```

          ### Verification
          All tests passed across Python 3.9-3.12 on Windows, Linux, and macOS.

  # ============================================================================
  # Cross-Platform Wheel Building
  # ============================================================================
  build-wheels:
    name: Build wheels on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    if: github.event_name == 'push' && (startsWith(github.ref, 'refs/tags/') || github.ref == 'refs/heads/main')
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Build wheels
        uses: pypa/cibuildwheel@v2.16.2
        env:
          # Build for Python 3.9-3.12
          CIBW_BUILD: cp39-* cp310-* cp311-* cp312-*
          # Skip 32-bit builds and PyPy
          CIBW_SKIP: "*-win32 *-manylinux_i686 pp*"
          # Install build dependencies
          CIBW_BEFORE_BUILD: pip install pybind11 cmake ninja
          # Test the built wheels
          CIBW_TEST_REQUIRES: pytest pytest-cov
          CIBW_TEST_COMMAND: pytest {project}/tests/ -v --tb=short
          # Build settings
          CIBW_BUILD_VERBOSITY: 1
          # Environment variables for build
          CIBW_ENVIRONMENT: CMAKE_BUILD_TYPE=Release ENABLE_SIMD=ON

      - name: Upload wheels
        uses: actions/upload-artifact@v3
        with:
          name: wheels-${{ matrix.os }}
          path: ./wheelhouse/*.whl

  # ============================================================================
  # PyPI Release (on tags)
  # ============================================================================
  release-pypi:
    name: Release to PyPI
    runs-on: ubuntu-latest
    needs: [test, benchmark, build-wheels]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/')
    environment: release

    steps:
      - name: Download all wheels
        uses: actions/download-artifact@v3
        with:
          pattern: wheels-*
          path: dist/
          merge-multiple: true

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}
          packages_dir: dist/

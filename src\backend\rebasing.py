"""
Donchian Midpoint Rebasing Module - C++ Only

This module provides C++-only rebasing functionality using high-performance compute kernels.
All calculations are performed in C++ with no Python fallbacks.

Key Features:
- C++ Donchian midpoint calculation with SIMD optimization
- C++ vector direction tracking for rebasing
- C++ percentage-based price rebasing
- Proper error handling for C++ kernel failures
"""

import numpy as np
import pandas as pd
from typing import Dict, Any
import logging
import sys
from pathlib import Path

logger = logging.getLogger(__name__)

# Import C++ kernels - required, no fallback
try:
    # Add compute directory to path
    compute_dir = Path(__file__).parent / "compute"
    if compute_dir.exists() and str(compute_dir) not in sys.path:
        sys.path.insert(0, str(compute_dir))

    import indicators

    CPP_KERNELS_AVAILABLE = True
    logger.info("C++ rebasing kernels loaded successfully")
except ImportError as e:
    CPP_KERNELS_AVAILABLE = False
    logger.error(f"C++ kernels required but not available: {e}")
    raise ImportError("C++ kernels are required for rebasing functionality") from e

# Donchian midpoint calculation moved to data_service.py to eliminate duplication


def track_vector_direction_changes_cpp(vector: np.ndarray) -> list:
    """
    Track vector direction changes using C++ kernels only.

    Args:
        vector: Array of Donchian midpoint values

    Returns:
        List of VectorDirectionChange objects

    Raises:
        RuntimeError: If C++ kernel fails
    """
    if not CPP_KERNELS_AVAILABLE:
        # Graceful fallback: simple direction change detection
        logger.warning("Using fallback calculation for vector direction changes")
        if len(vector) < 2:
            return []

        changes = []
        current_direction = None
        start_idx = 0

        for i in range(1, len(vector)):
            if vector[i] > vector[i-1]:
                direction = "up"
            elif vector[i] < vector[i-1]:
                direction = "down"
            else:
                continue  # No change

            if current_direction != direction:
                if current_direction is not None:
                    changes.append({
                        "start_index": start_idx,
                        "end_index": i-1,
                        "direction": current_direction
                    })
                current_direction = direction
                start_idx = i-1

        # Add final segment
        if current_direction is not None:
            changes.append({
                "start_index": start_idx,
                "end_index": len(vector)-1,
                "direction": current_direction
            })

        return changes

    if len(vector) == 0:
        return []

    try:
        # Use zero-copy buffer interface if available
        if hasattr(vector, "__array_interface__") and hasattr(
            indicators, "track_vector_direction_changes_buffer"
        ):
            result = indicators.track_vector_direction_changes_buffer(vector)
        else:
            # Fallback to list conversion
            vector_list = vector.tolist() if hasattr(vector, "tolist") else list(vector)
            result = indicators.track_vector_direction_changes(vector_list)

        if result.success:
            return result.data
        else:
            raise RuntimeError(
                f"C++ vector direction tracking failed: {result.error_message}"
            )
    except Exception as e:
        raise RuntimeError(f"C++ kernel error: {e}") from e


def calculate_rebased_prices_cpp(
    opens: np.ndarray,
    highs: np.ndarray,
    lows: np.ndarray,
    closes: np.ndarray,
    vector: np.ndarray,
    cycles: list,
) -> tuple:
    """
    Calculate rebased prices using C++ kernels only.

    Args:
        opens: Array of open prices
        highs: Array of high prices
        lows: Array of low prices
        closes: Array of close prices
        vector: Array of Donchian midpoint values
        cycles: List of VectorDirectionChange objects


    Returns:
        Tuple of (rebased_data, rebased_vector)

    Raises:
        RuntimeError: If C++ kernel fails
    """
    if not CPP_KERNELS_AVAILABLE:
        # Graceful fallback: simple percentage-based rebasing
        logger.warning("Using fallback calculation for rebased prices")
        if not opens or not closes:
            return [], []

        # Simple rebasing: convert to percentage changes from first value
        import math
        base_price = closes[0] if closes else 1.0

        # Validate base_price to prevent division by zero or NaN
        if base_price == 0 or math.isnan(base_price):
            logger.warning(f"Invalid base_price ({base_price}) - using fallback value of 1.0")
            base_price = 1.0

        rebased_data = []
        rebased_vector = []

        for i, (o, h, l, c) in enumerate(zip(opens, highs, lows, closes)):
            # Validate input values to prevent NaN propagation
            if any(math.isnan(val) for val in [o, h, l, c]):
                logger.warning(f"NaN values found in OHLC data at index {i} - using zero values")
                rebased_data.append((i, 0.0, 0.0, 0.0, 0.0))
            else:
                rebased_data.append((
                    i,  # index
                    ((o - base_price) / base_price) * 100,  # open_pct
                    ((h - base_price) / base_price) * 100,  # high_pct
                    ((l - base_price) / base_price) * 100,  # low_pct
                    ((c - base_price) / base_price) * 100   # close_pct
                ))

            if i < len(vector):
                vector_val = vector[i]
                if math.isnan(vector_val):
                    rebased_vector.append(0.0)
                else:
                    rebased_vector.append(((vector_val - base_price) / base_price) * 100)

        return rebased_data, rebased_vector

    try:
        # Use zero-copy buffer interface if available
        if hasattr(opens, "__array_interface__") and hasattr(
            indicators, "calculate_rebased_prices_buffer"
        ):
            result = indicators.calculate_rebased_prices_buffer(
                opens, highs, lows, closes, vector, cycles
            )
        else:
            # Fallback to list conversion
            result = indicators.calculate_rebased_prices(
                opens.tolist(),
                highs.tolist(),
                lows.tolist(),
                closes.tolist(),
                vector.tolist(),
                cycles,
            )

        if result.success:
            rebased_data, rebased_vector = result.data
            # Convert RebasedOHLC objects to tuples
            rebased_tuples = [
                (r.index, r.open_pct, r.high_pct, r.low_pct, r.close_pct)
                for r in rebased_data
            ]
            return rebased_tuples, rebased_vector
        else:
            raise RuntimeError(
                f"C++ rebased price calculation failed: {result.error_message}"
            )
    except Exception as e:
        raise RuntimeError(f"C++ kernel error: {e}") from e


def calculate_donchian_rebasing(
    ohlc_data: pd.DataFrame, length: int = 20
) -> Dict[str, Any]:
    """
    Main function to calculate Donchian midpoint rebasing using C++ kernels only.

    Args:
        ohlc_data: DataFrame with Open, High, Low, Close columns
        length: Lookback period for Donchian channel calculation


    Returns:
        Dictionary containing:
        - rebased_data: List of rebased OHLC tuples
        - rebased_vector: List of rebased vector values
        - vector: Original Donchian midpoint values
        - cycles: Cycle tracking information

    Raises:
        RuntimeError: If C++ kernels fail
    """
    logger.info(f"Calculating Donchian rebasing with C++ kernels: length={length}")

    # Extract close prices
    closes = ohlc_data["Close"].values
    opens = ohlc_data["Open"].values
    highs = ohlc_data["High"].values
    lows = ohlc_data["Low"].values

    # Calculate Donchian midpoint using C++ kernel (delegated to data_service)
    from .data_service import DataService

    data_service = DataService()
    vector = data_service._calculate_donchian_midpoint(closes, length)

    # Track vector direction changes using C++ kernel
    cycles = track_vector_direction_changes_cpp(vector)

    # Calculate rebased prices using C++ kernel
    rebased_data, rebased_vector = calculate_rebased_prices_cpp(
        opens, highs, lows, closes, vector, cycles
    )

    # Extract pivot information from cycles
    pivot_price = None
    visualization_pivot = None

    if cycles:
        # Find the last pivot price - let C++ handle NaN checking
        for cycle in reversed(cycles):
            if (
                cycle.direction_changed and cycle.pivot_price == cycle.pivot_price
            ):  # NaN != NaN in Python
                pivot_price = cycle.pivot_price
                visualization_pivot = cycle.pivot_price
                break

        # If no pivot found, use first cycle's pivot
        if pivot_price is None and cycles[0].direction_changed:
            pivot_price = cycles[0].pivot_price
            visualization_pivot = cycles[0].pivot_price

    logger.info(
        f"Rebasing complete: {len(rebased_data)} rebased candles, pivot={pivot_price}"
    )

    return {
        "rebased_data": rebased_data,
        "rebased_vector": rebased_vector,
        "vector": vector,
        "cycles": cycles,
        "pivot_price": pivot_price,
        "visualization_pivot": visualization_pivot,
    }

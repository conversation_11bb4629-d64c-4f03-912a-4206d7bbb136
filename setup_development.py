#!/usr/bin/env python3
"""
Development environment setup script for DataDriven Trading Platform.
Installs and configures all required dependencies for building the project.
"""

import subprocess
import sys
import os
import platform
from pathlib import Path
import shutil


def run_command(cmd, check=True, shell=True):
    """Run command and return result."""
    print(f"Running: {cmd}")
    try:
        result = subprocess.run(
            cmd, shell=shell, check=check, capture_output=True, text=True
        )
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False


def run_command_list(cmd_list, check=True):
    """Run command from list and return result."""
    print(f"Running: {' '.join(cmd_list)}")
    try:
        result = subprocess.run(
            cmd_list, check=check, capture_output=True, text=True
        )
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Error: Command '{' '.join(cmd_list)}' returned non-zero exit status {e.returncode}.")
        if e.stdout:
            print(f"Stdout: {e.stdout}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False


def check_command_exists(cmd):
    """Check if command exists in PATH."""
    return shutil.which(cmd) is not None


def install_python_dependencies():
    """Install Python dependencies."""
    print("\n=== Installing Python Dependencies ===")

    dependencies = [
        "pip --upgrade",
        "setuptools wheel",
        "pybind11[global]",
        "cmake",  # Python cmake package as fallback
        "ninja",  # Fast build system
        "protobuf grpcio-tools",
        "pytest pytest-cov pytest-qt",
        "black isort flake8 mypy",
    ]

    for dep in dependencies:
        cmd = f"{sys.executable} -m pip install {dep}"
        if not run_command(cmd, check=False):
            print(f"Warning: Failed to install {dep}")

    return True


def check_cmake():
    """Check CMake installation."""
    print("\n=== Checking CMake ===")

    if check_command_exists("cmake"):
        result = subprocess.run(["cmake", "--version"], capture_output=True, text=True)
        print(
            f"CMake found: {result.stdout.split()[2] if result.stdout else 'unknown version'}"
        )
        return True
    else:
        print("CMake not found in PATH")
        return False


def check_compiler():
    """Check C++ compiler."""
    print("\n=== Checking C++ Compiler ===")

    if platform.system() == "Windows":
        # Check for MSVC
        if check_command_exists("cl"):
            print("MSVC compiler found")
            return True
        else:
            print("MSVC compiler not found")
            print("Please install Visual Studio Build Tools or Visual Studio")
            print("Download: https://visualstudio.microsoft.com/downloads/")
            return False
    else:
        # Check for GCC or Clang
        for compiler in ["g++", "clang++"]:
            if check_command_exists(compiler):
                result = subprocess.run(
                    [compiler, "--version"], capture_output=True, text=True
                )
                print(
                    f"{compiler} found: {result.stdout.split()[0] if result.stdout else 'unknown'}"
                )
                return True

        print("No C++ compiler found")
        print("Please install GCC or Clang")
        return False


def check_qt():
    """Check Qt installation."""
    print("\n=== Checking Qt6 ===")

    # Check for qmake
    if check_command_exists("qmake6") or check_command_exists("qmake"):
        qmake_cmd = "qmake6" if check_command_exists("qmake6") else "qmake"
        result = subprocess.run([qmake_cmd, "-v"], capture_output=True, text=True)
        if "Qt version 6" in result.stdout:
            print("Qt6 found via qmake")
            return True

    # Check for Qt6 in common locations
    qt_paths = []
    if platform.system() == "Windows":
        qt_paths = [
            "C:/Qt/6.*/msvc*/bin",
            "C:/Qt/*/6.*/msvc*/bin",
            os.path.expanduser("~/Qt/6.*/msvc*/bin"),
        ]
    elif platform.system() == "Darwin":  # macOS
        qt_paths = [
            "/usr/local/Qt-6.*/bin",
            "/opt/homebrew/bin",
            "/usr/local/bin",
        ]
    else:  # Linux
        qt_paths = [
            "/usr/bin",
            "/usr/local/bin",
            "/opt/Qt/6.*/gcc_64/bin",
        ]

    for path_pattern in qt_paths:
        import glob

        for path in glob.glob(path_pattern):
            if os.path.exists(os.path.join(path, "qmake")):
                print(f"Qt6 found at: {path}")
                return True

    print("Qt6 not found")
    print("Please install Qt6 from: https://www.qt.io/download-qt-installer")
    return False


def setup_build_directory():
    """Setup build directory."""
    print("\n=== Setting up Build Directory ===")

    build_dir = Path("build")
    if build_dir.exists():
        print("Build directory already exists")
    else:
        build_dir.mkdir()
        print("Created build directory")

    return True


def configure_cmake():
    """Configure CMake build."""
    print("\n=== Configuring CMake Build ===")

    build_dir = Path("build")
    if not build_dir.exists():
        build_dir.mkdir()

    os.chdir(build_dir)

    # CMake configuration command
    cmake_args = [
        "cmake",
        "..",
        "-DCMAKE_BUILD_TYPE=Release",
        "-DBUILD_PYTHON_BINDINGS=ON",
        "-DBUILD_TESTS=ON",
        "-DBUILD_BENCHMARKS=ON",
        "-DENABLE_SIMD=ON",
    ]

    # Add generator for Windows - Force Visual Studio to avoid Ninja linker issues
    if platform.system() == "Windows":
        cmake_args.extend(["-G", "Visual Studio 17 2022", "-A", "x64"])
        cmake_args.extend(["-DCMAKE_INTERPROCEDURAL_OPTIMIZATION=OFF"])  # Disable LTO to avoid linker crashes

    # Use subprocess.run instead of shell command to handle spaces properly
    success = run_command_list(cmake_args)

    os.chdir("..")
    return success


def build_project():
    """Build the project."""
    print("\n=== Building Project ===")

    build_dir = Path("build")
    if not build_dir.exists():
        print("Build directory not found. Run configure first.")
        return False

    os.chdir(build_dir)

    # Build command
    cmd = "cmake --build . --config Release"
    success = run_command(cmd)

    os.chdir("..")
    return success


def run_tests():
    """Run tests to verify build."""
    print("\n=== Running Tests ===")

    # Run Python tests
    print("Running Python tests...")
    python_tests = run_command(f"{sys.executable} -m pytest tests/ -v", check=False)

    # Run C++ tests if available
    print("Running C++ tests...")
    build_dir = Path("build")
    if build_dir.exists():
        os.chdir(build_dir)
        cpp_tests = run_command("ctest --verbose", check=False)
        os.chdir("..")
    else:
        cpp_tests = False

    return python_tests or cpp_tests


def main():
    """Main setup function."""
    print("DataDriven Trading Platform - Development Setup")
    print("=" * 50)

    # Check current directory
    if not Path("CMakeLists.txt").exists():
        print("Error: CMakeLists.txt not found. Please run from project root.")
        return False

    success = True

    # Install Python dependencies
    if not install_python_dependencies():
        print("Warning: Some Python dependencies failed to install")

    # Check required tools
    checks = [
        ("CMake", check_cmake),
        ("C++ Compiler", check_compiler),
        ("Qt6", check_qt),
    ]

    for name, check_func in checks:
        if not check_func():
            print(f"❌ {name} check failed")
            success = False
        else:
            print(f"✅ {name} check passed")

    if not success:
        print("\n❌ Setup incomplete. Please install missing dependencies.")
        print("\nInstallation guides:")
        print("- CMake: https://cmake.org/download/")
        print(
            "- Visual Studio Build Tools: https://visualstudio.microsoft.com/downloads/"
        )
        print("- Qt6: https://www.qt.io/download-qt-installer")
        return False

    # Setup build
    if not setup_build_directory():
        print("❌ Failed to setup build directory")
        return False

    # Configure CMake
    if not configure_cmake():
        print("❌ CMake configuration failed")
        return False

    # Build project
    if not build_project():
        print("❌ Build failed")
        return False

    # Run tests
    if not run_tests():
        print("⚠️ Some tests failed, but build completed")

    print("\n✅ Development environment setup complete!")
    print("\nNext steps:")
    print("1. Run the application: python main.py")
    print("2. Run tests: pytest tests/ -v")
    print("3. Build C++ extensions: cd build && cmake --build . --config Release")

    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

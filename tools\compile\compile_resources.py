#!/usr/bin/env python3
"""
Resource compilation script for Qt Resource files.
Compiles .qrc files to .rcc for efficient runtime loading.
"""

import subprocess
import sys
from pathlib import Path


def compile_resources():
    """Compile Qt resource files to binary format."""
    resources_dir = Path("resources")

    if not resources_dir.exists():
        print("Resources directory not found!")
        return False

    # Find all .qrc files
    qrc_files = list(resources_dir.glob("*.qrc"))

    if not qrc_files:
        print("No .qrc files found in resources directory!")
        print("Creating default resource files...")

        # Create a basic QRC file if none exists
        default_qrc = resources_dir / "app_resources.qrc"
        if not default_qrc.exists():
            print("Please run the application setup to create resource files.")
            return False

    success = True

    for qrc_file in qrc_files:
        rcc_file = qrc_file.with_suffix(".rcc")

        print(f"Compiling {qrc_file} -> {rcc_file}")

        try:
            # Use rcc to compile the resource file
            result = subprocess.run(
                ["rcc", "--binary", str(qrc_file), "-o", str(rcc_file)],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                print(f"✓ Successfully compiled {qrc_file.name}")
            else:
                print(f"✗ Failed to compile {qrc_file.name}")
                print(f"Error: {result.stderr}")
                success = False

        except FileNotFoundError:
            print("✗ rcc command not found. Please install Qt development tools.")
            print("Alternative: Using pyrcc6 (if available)")

            try:
                # Try pyrcc6 as fallback
                result = subprocess.run(
                    ["pyrcc6", str(qrc_file), "-o", str(rcc_file.with_suffix(".py"))],
                    capture_output=True,
                    text=True,
                )

                if result.returncode == 0:
                    print(f"✓ Successfully compiled {qrc_file.name} with pyrcc6")
                else:
                    print(f"✗ pyrcc6 also failed: {result.stderr}")
                    success = False

            except FileNotFoundError:
                print("✗ Neither rcc nor pyrcc6 found. Skipping resource compilation.")
                success = False

    return success


if __name__ == "__main__":
    if compile_resources():
        print("\n✓ Resource compilation completed successfully!")
        sys.exit(0)
    else:
        print("\n✗ Resource compilation failed!")
        sys.exit(1)

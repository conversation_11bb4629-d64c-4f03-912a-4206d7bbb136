"""
Options Service - Backend service for fetching and parsing options data
Moved from frontend volatility_statistics.py to be used across the application
"""

import requests
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import re
import time
import logging
from typing import Dict, Any, List, Optional, Tuple


class OptionsService:
    """Service for fetching options data from optioncharts.io with caching."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        # Class-level options data cache to prevent multiple fetches
        self._options_cache = {}  # Format: {(ticker, expiry): options_data}
        self._cache_timestamp = {}  # Format: {(ticker, expiry): timestamp}
        self._cache_ttl = 300  # 5 minutes TTL
        
    def fetch_options_data(self, ticker: str, selected_expiry: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Fetch options data from optioncharts.io for the current ticker with caching."""
        try:
            if not ticker:
                return None

            # Determine which expiry to use
            if selected_expiry is None:
                # Fetch available expiration dates and use the first one
                expiry_dates = self._fetch_available_expiry_dates(ticker)
                if not expiry_dates:
                    return None
                # Use the first available expiry as default
                selected_expiry = expiry_dates[0]

            # Check cache first (5 minute TTL)
            cache_key = (ticker, selected_expiry)
            current_time = time.time()

            if cache_key in self._options_cache:
                cache_time = self._cache_timestamp.get(cache_key, 0)
                if current_time - cache_time < self._cache_ttl:
                    self.logger.debug(f"Using cached options data for {ticker} expiry {selected_expiry}")
                    return self._options_cache[cache_key]
                else:
                    # Cache expired, remove it
                    self.logger.debug(f"Cache expired for {ticker} expiry {selected_expiry}, removing")
                    del self._options_cache[cache_key]
                    del self._cache_timestamp[cache_key]

            # Build optioncharts.io URL with selected expiration date
            # Convert date format if needed (YYYY-MM-DD to URL format)
            if isinstance(selected_expiry, str) and len(selected_expiry) == 10:  # YYYY-MM-DD format
                expiry_url = f"{selected_expiry}%3Aw"
            else:
                expiry_url = selected_expiry

            url = f"https://optioncharts.io/async/option_chain?expiration_dates={expiry_url}&option_type=all&strike_range=all&ticker={ticker}&view=list"

            self.logger.info(f"Fetching options data for {ticker} expiry {selected_expiry}")

            # Set headers to mimic browser request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            # Make request
            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract all available expiration dates from this page
            all_expiry_dates = self._extract_expiry_dates_from_page(soup)

            # Parse the optioncharts.io HTML structure
            # The page has calls and puts data in separate sections
            calls_data = self._parse_optioncharts_data(soup, 'calls', selected_expiry)
            puts_data = self._parse_optioncharts_data(soup, 'puts', selected_expiry)

            # Create DataFrames
            calls_df = pd.DataFrame(calls_data)
            puts_df = pd.DataFrame(puts_data)

            if calls_df.empty and puts_df.empty:
                self.logger.warning(f"No valid options data found for {ticker} from optioncharts.io")
                return None

            self.logger.info(f"Options data fetched: {len(calls_df)} calls, {len(puts_df)} puts for {ticker}")

            # Debug: Show available strike ranges
            if not calls_df.empty:
                call_strikes = sorted(calls_df['strike'].unique())
                self.logger.debug(f"Call strikes: {call_strikes[:5]}...{call_strikes[-5:]} (showing first/last 5)")

            if not puts_df.empty:
                put_strikes = sorted(puts_df['strike'].unique())
                self.logger.debug(f"Put strikes: {put_strikes[:5]}...{put_strikes[-5:]} (showing first/last 5)")

            # Use the extracted expiration dates from the page, or fall back to the selected one
            if all_expiry_dates:
                expiry_list = all_expiry_dates
            else:
                # Fallback: collect expiration dates from the data
                all_expiries = set()
                if not calls_df.empty:
                    all_expiries.update(calls_df['expiry'].unique())
                if not puts_df.empty:
                    all_expiries.update(puts_df['expiry'].unique())
                expiry_list = sorted([exp for exp in all_expiries if exp != 'Unknown'])
                if not expiry_list and 'Unknown' in all_expiries:
                    expiry_list = ['Unknown']

            # Prepare result
            result = {
                'calls': calls_df,
                'puts': puts_df,
                'expiry_dates': expiry_list,
                'default_expiry': selected_expiry,
                'selected_expiry': selected_expiry
            }

            # Cache the result
            self._options_cache[cache_key] = result
            self._cache_timestamp[cache_key] = current_time
            self.logger.debug(f"Cached options data for {ticker} expiry {selected_expiry}")

            return result

        except Exception as e:
            self.logger.error(f"Error fetching options data from optioncharts.io: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _parse_optioncharts_data(self, soup, option_type: str, expiry_date: str) -> List[Dict[str, Any]]:
        """Parse calls or puts data from optioncharts.io HTML structure."""
        try:
            options_data = []

            # The optioncharts.io page has a specific structure where calls and puts are in separate sections
            # Let's find all tables and determine which one contains the data we want

            all_tables = soup.find_all('table')
            # Tables found - continue processing

            # Look for the section that contains our target option type
            target_section = None

            # Search for text that indicates the section
            page_text = soup.get_text()

            if option_type == 'calls':
                # Find the calls section - it should be the first table after "Calls" text
                calls_start = page_text.find('Calls')
                puts_start = page_text.find('Puts')

                if calls_start != -1 and puts_start != -1:
                    # Find all tables and use the first one (which should be calls)
                    for i, table in enumerate(all_tables):
                        table_text = table.get_text()
                        # Check if this table contains strike prices and option data
                        if 'Strike' in table_text and 'Bid' in table_text and 'Ask' in table_text:
                            target_section = table
                            # Table found for option type
                            break

            elif option_type == 'puts':
                # For puts, we need the second table or the table after the puts section
                puts_start = page_text.find('Puts')

                if puts_start != -1:
                    # Find tables that appear after the puts section
                    table_found = False
                    for i, table in enumerate(all_tables):
                        table_text = table.get_text()
                        # Check if this table contains strike prices and option data
                        if 'Strike' in table_text and 'Bid' in table_text and 'Ask' in table_text:
                            if table_found:  # This should be the puts table (second occurrence)
                                target_section = table
                                # Table found for option type
                                break
                            table_found = True

            if not target_section:
                self.logger.warning(f"Could not find table for {option_type}")
                return options_data

            # Parse the table rows
            rows = target_section.find_all('tr')
            # Parse table rows

            for row_idx, row in enumerate(rows[1:]):  # Skip header row
                cells = row.find_all(['td', 'th'])
                if len(cells) < 8:  # Need enough columns for options data
                    continue

                try:
                    # First cell should be the strike price
                    first_cell = cells[0].get_text().strip()

                    # Check if this looks like a strike price (numeric)
                    try:
                        strike = float(first_cell)
                    except (ValueError, TypeError):
                        continue

                    # Extract option data based on the optioncharts.io column structure:
                    # Strike, Last Price, Bid, Mid, Ask, Volume, Open Interest, Implied Volatility, Delta, Gamma, Theta, Vega

                    option_data = {
                        'strike': strike,
                        'expiry': expiry_date,
                        'type': option_type.rstrip('s')  # Remove 's' from 'calls'/'puts'
                    }

                    # Extract bid (column 2)
                    if len(cells) > 2:
                        bid_text = cells[2].get_text().strip()
                        if bid_text and bid_text != '-':
                            try:
                                option_data['bid'] = float(bid_text)
                            except (ValueError, TypeError):
                                pass

                    # Extract ask (column 4)
                    if len(cells) > 4:
                        ask_text = cells[4].get_text().strip()
                        if ask_text and ask_text != '-':
                            try:
                                option_data['ask'] = float(ask_text)
                            except (ValueError, TypeError):
                                pass

                    # Extract implied volatility (column 7)
                    if len(cells) > 7:
                        iv_text = cells[7].get_text().strip()
                        if iv_text and iv_text not in ['-', '']:
                            try:
                                # Remove % sign if present
                                iv_clean = iv_text.replace('%', '')

                                # Handle NaN values by converting to 0
                                if iv_clean.lower() in ['nan', 'n/a']:
                                    iv_float = 0.0
                                else:
                                    iv_float = float(iv_clean)

                                option_data['iv'] = iv_float
                            except (ValueError, TypeError):
                                # If we can't parse it, treat as 0
                                option_data['iv'] = 0.0

                    # Only add if we have at least bid or ask price
                    if 'bid' in option_data or 'ask' in option_data:
                        options_data.append(option_data)

                except (ValueError, TypeError, IndexError):
                    continue

            self.logger.debug(f"Parsed {len(options_data)} {option_type} options")
            return options_data

        except Exception as e:
            self.logger.error(f"Error parsing {option_type} data from optioncharts.io: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _parse_options_table(self, table, option_type: str, expiry_date: str) -> List[Dict[str, Any]]:
        """Parse options table from optioncharts.io HTML."""
        try:
            options_data = []
            rows = table.find_all('tr')

            if len(rows) < 2:  # Need at least header and one data row
                return options_data

            # Find header row to identify column indices
            header_row = rows[0]
            headers = [th.get_text().strip().lower() for th in header_row.find_all(['th', 'td'])]

            # Based on the optioncharts.io structure, the table has columns:
            # Strike, Last Price, Bid, Mid, Ask, Volume, Open Interest, Implied Volatility, Delta, Gamma, Theta, Vega
            col_indices = {}
            for i, header in enumerate(headers):
                header_clean = header.replace('\n', ' ').strip()
                if 'strike' in header_clean:
                    col_indices['strike'] = i
                elif 'bid' in header_clean:
                    col_indices['bid'] = i
                elif 'ask' in header_clean:
                    col_indices['ask'] = i
                elif 'implied volatility' in header_clean or header_clean == 'iv':
                    col_indices['iv'] = i

            # Check if we found the essential columns
            if 'strike' not in col_indices:
                self.logger.warning("Could not find strike column in options table")
                return options_data

            # Parse data rows
            for row in rows[1:]:  # Skip header
                cells = row.find_all(['td', 'th'])
                if len(cells) < 3:  # Need at least strike and some price data
                    continue

                try:
                    # Extract strike price
                    strike_text = cells[col_indices['strike']].get_text().strip()
                    if not strike_text or strike_text == '-':
                        continue

                    strike = float(strike_text)

                    # For optioncharts.io, we need to determine if this is calls or puts
                    # The page structure shows calls first, then puts
                    # We'll parse both and let the caller separate them

                    # Create call option data entry
                    if option_type in ['call', 'both']:
                        call_data = {
                            'strike': strike,
                            'expiry': expiry_date,
                            'type': 'call'
                        }

                        # Extract bid price
                        if 'bid' in col_indices and col_indices['bid'] < len(cells):
                            bid_text = cells[col_indices['bid']].get_text().strip()
                            if bid_text and bid_text != '-':
                                try:
                                    call_data['bid'] = float(bid_text)
                                except (ValueError, TypeError):
                                    pass

                        # Extract ask price
                        if 'ask' in col_indices and col_indices['ask'] < len(cells):
                            ask_text = cells[col_indices['ask']].get_text().strip()
                            if ask_text and ask_text != '-':
                                try:
                                    call_data['ask'] = float(ask_text)
                                except (ValueError, TypeError):
                                    pass

                        # Extract implied volatility
                        if 'iv' in col_indices and col_indices['iv'] < len(cells):
                            iv_text = cells[col_indices['iv']].get_text().strip()
                            # Parse IV values, including NaN which should be treated as 0
                            if iv_text and iv_text not in ['-', '']:
                                try:
                                    # Remove % sign if present
                                    iv_clean = iv_text.replace('%', '')

                                    # Handle NaN values by converting to 0
                                    if iv_clean.lower() in ['nan', 'n/a']:
                                        iv_float = 0.0
                                    else:
                                        iv_float = float(iv_clean)

                                    call_data['iv'] = iv_float

                                    # Log NaN conversions
                                    if iv_clean.lower() in ['nan', 'n/a']:
                                        self.logger.info(f"Converted NaN IV to 0: '{iv_text}' -> {iv_float} for strike {call_data.get('strike', 'unknown')}")

                                except (ValueError, TypeError):
                                    # If we can't parse it, treat as 0
                                    call_data['iv'] = 0.0

                        # Only add if we have at least bid or ask price
                        if 'bid' in call_data or 'ask' in call_data:
                            options_data.append(call_data)

                except (ValueError, TypeError, IndexError):
                    continue  # Skip invalid rows

            return options_data

        except Exception as e:
            self.logger.error(f"Error parsing options table: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _fetch_available_expiry_dates(self, ticker: str) -> List[str]:
        """Fetch available expiration dates for the ticker from optioncharts.io."""
        try:
            # Calculate next Friday for initial request
            today = datetime.now()
            days_ahead = 4 - today.weekday()  # Friday is weekday 4
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            next_friday = today + timedelta(days_ahead)
            default_expiry = next_friday.strftime('%Y-%m-%d')

            # Build initial URL to get the page with expiration dates
            url = f"https://optioncharts.io/async/option_chain?expiration_dates={default_expiry}%3Aw&option_type=all&strike_range=all&ticker={ticker}&view=list"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(url, headers=headers, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')
            return self._extract_expiry_dates_from_page(soup)

        except Exception as e:
            self.logger.error(f"Error fetching available expiry dates: {e}")
            return []

    def _extract_expiry_dates_from_page(self, soup) -> List[str]:
        """Extract expiration dates from the optioncharts.io page."""
        try:
            expiry_dates = []

            # Look for expiration date list items
            expiry_items = soup.find_all('div', class_='expiration-date-list-item')

            if not expiry_items:
                # Fallback: look for text patterns that match dates
                page_text = soup.get_text()
                # Look for patterns like "Aug 22, 2025 (7 days) (w)"
                date_pattern = r'([A-Z][a-z]{2} \d{1,2}, \d{4}) \(\d+ days\)'
                matches = re.findall(date_pattern, page_text)

                for match in matches:
                    try:
                        # Convert "Aug 22, 2025" to "2025-08-22"
                        date_obj = datetime.strptime(match, '%b %d, %Y')
                        formatted_date = date_obj.strftime('%Y-%m-%d')
                        if formatted_date not in expiry_dates:
                            expiry_dates.append(formatted_date)
                    except ValueError:
                        continue
            else:
                # Extract dates from the dropdown items
                for item in expiry_items:
                    text = item.get_text().strip()
                    # Extract date from text like "Aug 22, 2025 (7 days) (w)"
                    date_match = re.search(r'([A-Z][a-z]{2} \d{1,2}, \d{4})', text)
                    if date_match:
                        try:
                            date_obj = datetime.strptime(date_match.group(1), '%b %d, %Y')
                            formatted_date = date_obj.strftime('%Y-%m-%d')
                            if formatted_date not in expiry_dates:
                                expiry_dates.append(formatted_date)
                        except ValueError:
                            continue

            # Sort the dates
            expiry_dates.sort()
            self.logger.debug(f"Found {len(expiry_dates)} expiration dates")
            return expiry_dates

        except Exception as e:
            self.logger.error(f"Error extracting expiry dates from page: {e}")
            return []

    def get_asset_class(self, ticker: str) -> str:
        """Determine the asset class for NASDAQ API based on ticker."""
        ticker_upper = ticker.upper()

        # Common ETFs
        etfs = ['SPY', 'QQQ', 'IWM', 'EFA', 'VTI', 'VOO', 'VEA', 'VWO', 'AGG', 'BND', 'GLD', 'SLV', 'USO', 'TLT', 'HYG', 'LQD', 'XLF', 'XLE', 'XLK', 'XLV', 'XLI', 'XLP', 'XLY', 'XLU', 'XLRE', 'XLB', 'XME', 'XRT', 'XHB', 'XBI', 'XOP', 'XTN', 'XAR', 'XES', 'XSD', 'XTL', 'XPH', 'XHS', 'XHE', 'XTH', 'XSW', 'XTK', 'XAI', 'XTN', 'XAR', 'XES', 'XSD', 'XTL', 'XPH', 'XHS', 'XHE', 'XTH', 'XSW', 'XTK', 'XAI']

        # Common indices
        indices = ['SPX', 'NDX', 'RUT', 'VIX', 'DJI', 'OEX', 'XEO', 'XSP', 'MNX', 'RUI', 'RUTW', 'SPXW', 'NDXP']

        if ticker_upper in etfs:
            return 'etf'
        elif ticker_upper in indices:
            return 'index'
        else:
            return 'stocks'

    def clear_cache(self):
        """Clear the options data cache."""
        self._options_cache.clear()
        self._cache_timestamp.clear()
        self.logger.info("Options cache cleared")

    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about the current cache state."""
        current_time = time.time()
        cache_info = {
            'total_entries': len(self._options_cache),
            'entries': []
        }

        for cache_key, timestamp in self._cache_timestamp.items():
            ticker, expiry = cache_key
            age = current_time - timestamp
            cache_info['entries'].append({
                'ticker': ticker,
                'expiry': expiry,
                'age_seconds': age,
                'expired': age > self._cache_ttl
            })

        return cache_info

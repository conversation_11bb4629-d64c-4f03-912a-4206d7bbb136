"""
Visualization Data Service
Prepares all visualization data in the backend layer.
UI layer should only render pre-computed data from this service.
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
import numpy as np
from concurrent.futures import ProcessPoolExecutor
import sys
from pathlib import Path

# Try to import C++ kernels if available
try:
    # Add compute directory to path
    compute_dir = Path(__file__).parent.parent / "compute"
    if compute_dir.exists() and str(compute_dir) not in sys.path:
        sys.path.insert(0, str(compute_dir))

    import indicators

    CPP_KERNELS_AVAILABLE = True
except ImportError:
    CPP_KERNELS_AVAILABLE = False


class VisualizationService:
    """Service for preparing all visualization data in the backend."""

    def __init__(self):
        self.logger = logging.getLogger("VisualizationService")
        # Use ProcessPoolExecutor to bypass GIL for CPU-bound tasks
        self.process_executor = ProcessPoolExecutor(max_workers=2, mp_context=None)

    async def shutdown(self):
        """Shutdown the visualization service."""
        self.process_executor.shutdown(wait=True)

    def prepare_candlestick_data(
        self,
        timestamps: List[float],
        opens: np.ndarray,
        highs: np.ndarray,
        lows: np.ndarray,
        closes: np.ndarray,
    ) -> List[Dict[str, Any]]:
        """Prepare candlestick data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare all candlestick rendering data
            result = indicators.prepare_candlestick_render_data(
                timestamps, opens, highs, lows, closes
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ candlestick preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing candlestick data: {e}")
            raise

    def prepare_rebased_candlestick_data(
        self,
        rebased_data: List[Tuple],
        bullish_color: str = "#4CAF50",
        bearish_color: str = "#F44336",
    ) -> List[Dict[str, Any]]:
        """Prepare rebased candlestick data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare all rebased candlestick rendering data
            result = indicators.prepare_rebased_candlestick_render_data(
                rebased_data, bullish_color, bearish_color
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ rebased candlestick preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing rebased candlestick data: {e}")
            raise

    def prepare_peak_trough_display_data(
        self, peaks: List, troughs: List, peak_color: str = "g", trough_color: str = "r"
    ) -> List[Dict[str, Any]]:
        """Prepare peak/trough display data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare all peak/trough rendering data
            result = indicators.prepare_peak_trough_display_data(
                peaks, troughs, peak_color, trough_color
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ peak/trough preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing peak/trough display data: {e}")
            raise

    def calculate_standard_deviations(
        self, peaks, troughs
    ) -> Dict[str, float]:
        """Calculate positive/negative standard deviations based on peaks and troughs - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for calculations")

        try:
            # Use C++ to calculate separate standard deviations from peaks and troughs
            result = indicators.calculate_separated_standard_deviations_from_peaks_troughs(
                peaks, troughs
            )

            if result.success:
                return {
                    "positive_std": result.data.positive_std,
                    "negative_std": result.data.negative_std,
                    "positive_count": result.data.positive_count,
                    "negative_count": result.data.negative_count,
                }
            else:
                raise RuntimeError(
                    f"C++ standard deviation calculation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error calculating standard deviations: {e}")
            raise

    def prepare_vector_plot_data(
        self, indices: List[int], rebased_vector: List[float]
    ) -> List[Dict[str, Any]]:
        """Prepare vector plot data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare vector plot rendering data
            result = indicators.prepare_vector_plot_render_data(indices, rebased_vector)

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ vector plot preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing vector plot data: {e}")
            raise

    def calculate_crosshair_info(
        self,
        x_pos: float,
        rebased_data: List[Tuple],
        actual_dates: List,
        closes: List[float],
    ) -> Dict[str, Any]:
        """Calculate crosshair information - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for calculations")

        try:
            # Use C++ to calculate crosshair information
            result = indicators.calculate_crosshair_info(
                x_pos, rebased_data, actual_dates, closes
            )

            if result.success:
                return result.data
            else:
                return {}  # Return empty dict on failure

        except Exception as e:
            self.logger.error(f"Error calculating crosshair info: {e}")
            return {}

    def determine_donchian_color(
        self, donchian_midpoint: np.ndarray, closes: np.ndarray
    ) -> str:
        """Determine Donchian color based on price position - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            return "#CCCCCC"  # Gray fallback

        try:
            # Use C++ to determine color
            result = indicators.determine_donchian_color(donchian_midpoint, closes)

            if result.success:
                return result.data
            else:
                return "#CCCCCC"  # Gray fallback

        except Exception as e:
            self.logger.error(f"Error determining Donchian color: {e}")
            return "#CCCCCC"  # Gray fallback

    def prepare_donchian_stairstep_data(
        self, timestamps: List[float], donchian_values: List[float]
    ) -> Dict[str, Any]:
        """Prepare Donchian midpoint stairstep data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare stairstep data with NaN filtering
            result = indicators.prepare_donchian_stairstep_data(
                timestamps, donchian_values
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ Donchian stairstep preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing Donchian stairstep data: {e}")
            raise

    def prepare_rebased_vector_stairstep_data(
        self, indices: List[int], vector_values: List[float]
    ) -> Dict[str, Any]:
        """Prepare rebased vector stairstep data for UI rendering - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare stairstep data
            result = indicators.prepare_rebased_vector_stairstep_data(
                indices, vector_values
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ rebased vector stairstep preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing rebased vector stairstep data: {e}")
            raise

    def calculate_mean_peak_trough_levels(
        self, peaks: List, troughs: List
    ) -> Dict[str, float]:
        """Calculate mean peak and trough levels - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for calculations")

        try:
            # Use C++ to calculate mean levels
            result = indicators.calculate_mean_peak_trough_levels(peaks, troughs)

            if result.success:
                return {
                    "mean_peak_level": result.data.mean_peak_level,
                    "mean_trough_level": result.data.mean_trough_level,
                    "peak_count": result.data.peak_count,
                    "trough_count": result.data.trough_count,
                }
            else:
                raise RuntimeError(
                    f"C++ mean level calculation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error calculating mean peak/trough levels: {e}")
            raise

    def prepare_predictive_cycle_lines(
        self, peaks: List, troughs: List, rebased_data: List
    ) -> List[Dict[str, Any]]:
        """Prepare predictive cycle line data - all computation in backend."""
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels required for data preparation")

        try:
            # Use C++ to prepare predictive cycle data
            result = indicators.prepare_predictive_cycle_lines(
                peaks, troughs, rebased_data
            )

            if result.success:
                return result.data
            else:
                raise RuntimeError(
                    f"C++ predictive cycle preparation failed: {result.error_message}"
                )

        except Exception as e:
            self.logger.error(f"Error preparing predictive cycle lines: {e}")
            raise


def prepare_all_visualization_data(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare all visualization data in one backend call.
    This function should be called in the backend/ProcessPool to prepare
    all data that the UI needs for rendering.
    """
    viz_service = VisualizationService()

    try:
        # Extract data
        timestamps = market_data.get("timestamp", [])
        opens = market_data.get("open", np.array([]))
        highs = market_data.get("high", np.array([]))
        lows = market_data.get("low", np.array([]))
        closes = market_data.get("close", np.array([]))
        rebased_data = market_data.get("rebased_data", [])
        rebased_vector = market_data.get("rebased_vector", [])
        peaks = market_data.get("peaks", [])
        troughs = market_data.get("troughs", [])
        donchian_midpoint = market_data.get("donchian_midpoint", np.array([]))
        actual_dates = market_data.get("actual_dates", [])

        # Prepare all visualization data
        prepared_data = {
            "candlestick_data": viz_service.prepare_candlestick_data(
                timestamps, opens, highs, lows, closes
            ),
            "rebased_candlestick_data": viz_service.prepare_rebased_candlestick_data(
                rebased_data
            ),
            "peak_trough_display_data": viz_service.prepare_peak_trough_display_data(
                peaks, troughs
            ),
            "standard_deviations": viz_service.calculate_standard_deviations(
                rebased_data
            ),
            "vector_plot_data": viz_service.prepare_vector_plot_data(
                list(range(len(rebased_vector))), rebased_vector
            ),
            "donchian_color": viz_service.determine_donchian_color(
                donchian_midpoint, closes
            ),
            "donchian_stairstep_data": viz_service.prepare_donchian_stairstep_data(
                timestamps,
                (
                    list(donchian_midpoint)
                    if hasattr(donchian_midpoint, "tolist")
                    else donchian_midpoint
                ),
            ),
            "rebased_vector_stairstep_data": viz_service.prepare_rebased_vector_stairstep_data(
                list(range(len(rebased_vector))), rebased_vector
            ),
            "mean_levels": viz_service.calculate_mean_peak_trough_levels(
                peaks, troughs
            ),
            "predictive_cycle_lines": viz_service.prepare_predictive_cycle_lines(
                peaks, troughs, rebased_data
            ),
            "crosshair_lookup_data": {
                "rebased_data": rebased_data,
                "actual_dates": actual_dates,
                "closes": list(closes) if hasattr(closes, "tolist") else closes,
            },
        }

        return prepared_data

    except Exception as e:
        logging.error(f"Error preparing visualization data: {e}")
        raise

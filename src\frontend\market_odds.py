"""
Market Odds Tab - Zero-Computation UI Implementation
Pure PyQt6 visualization with all computations offloaded to worker threads.
Implements proper UI thread discipline and Model/View architecture.
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QSplitter,
    QPushButton,
    QScrollArea,
    QInputDialog,
    QDialog,
    QCheckBox,
)
from PyQt6.QtCore import (
    pyqtSignal,
    pyqtSlot,
    QThreadPool,
    QRunnable,
    QObject,
    Qt,
    QPointF,
)
from PyQt6.QtGui import QFont, QCursor, QPixmap, QPalette, QColor
import pyqtgraph as pg
from pyqtgraph import PlotWidget
from typing import Optional, Dict, Any
import asyncio

# Import zero-computation architecture components
from backend.signals import signal_manager, TypedSlotDecorator
from backend.models import ChartDataModel, ChartElement, ChartElementType
from .renderer import ChartRenderer
from backend.computation_offloader import computation_offloader, ComputationType


class DataFetchWorker(QRunnable):
    """
    Enterprise-grade worker for fetching data without blocking UI thread.
    Uses proper async integration and structured logging.
    """

    class Signals(QObject):
        # Well-typed signals for enterprise-grade communication
        finished = pyqtSignal(dict)  # market_data: Dict[str, Any]
        error = pyqtSignal(str)  # error_message: str
        progress = pyqtSignal(str)  # status_message: str

    def __init__(self, data_service, ticker, timeframe, dtl, length):
        super().__init__()
        self.signals = self.Signals()
        self.data_service = data_service
        self.ticker = ticker
        self.timeframe = timeframe
        self.dtl = dtl
        self.length = length
        self.logger = self._setup_logging()

    def _setup_logging(self):
        """Setup structured logging for worker thread."""
        import logging

        logger = logging.getLogger(f"DataFetchWorker.{self.ticker}")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s", '
                '"thread": "%(thread)d", "ticker": "' + self.ticker + '"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def run(self):
        """
        Execute data fetching using enterprise-grade async patterns.
        Avoids blocking event loops and provides proper error handling.
        """
        try:
            self.logger.info("Starting market data fetch")
            self.signals.progress.emit(f"Fetching data for {self.ticker}...")

            # Use asyncio.run for proper async execution without blocking
            # This creates a new event loop that doesn't interfere with Qt
            result = asyncio.run(self._fetch_data_async())

            if result:
                self.logger.info("Market data fetch completed successfully")
                self.signals.finished.emit(result)
            else:
                error_msg = "No data received from backend service"
                self.logger.warning(error_msg)
                self.signals.error.emit(error_msg)

        except Exception as e:
            error_msg = f"Data fetch failed: {str(e)}"
            self.logger.error(error_msg)
            self.signals.error.emit(error_msg)

    async def _fetch_data_async(self):
        """
        Async data fetching method that properly handles coroutines.
        Separated for better error handling and testing.
        """
        try:
            # Create coroutine for data fetching
            result = await self.data_service.fetch_market_data_with_rebasing(
                self.ticker, self.timeframe, self.dtl, self.length
            )
            return result

        except Exception as e:
            self.logger.error(f"Async data fetch error: {e}")
            raise


class ZeroComputationCandlestickItem(pg.GraphicsObject):
    """
    Zero-computation candlestick item that renders pre-computed data only.
    All mathematical operations are performed in backend worker threads.
    """

    def __init__(self, render_data: Dict[str, Any]):
        super().__init__()
        # Store pre-computed render data - zero calculations in UI
        self.render_data = render_data
        self.bounds = render_data.get("bounds", {})

        # Pre-rendered picture from backend
        self.picture = pg.QtGui.QPicture()
        self._render_from_data()

    def _render_from_data(self):
        """Render candlesticks from pre-computed backend data."""
        painter = pg.QtGui.QPainter(self.picture)
        painter.setRenderHint(pg.QtGui.QPainter.RenderHint.Antialiasing)

        # Get pre-computed candlestick elements
        candlesticks = self.render_data.get("candlesticks", [])

        for candle in candlesticks:
            # All data pre-computed in backend
            body_rect = candle["body_rect"]  # Pre-computed rectangle
            wick_line = candle["wick_line"]  # Pre-computed line
            body_color = candle["body_color"]
            wick_color = candle["wick_color"]

            # Draw wick (pre-computed coordinates)
            painter.setPen(pg.mkPen(wick_color, width=1))
            painter.drawLine(
                pg.QtCore.QPointF(wick_line["x1"], wick_line["y1"]),
                pg.QtCore.QPointF(wick_line["x2"], wick_line["y2"]),
            )

            # Draw body (pre-computed rectangle)
            if body_rect["height"] > 0:
                painter.setPen(pg.mkPen(body_color, width=1))
                painter.setBrush(pg.mkBrush(body_color))
                painter.drawRect(
                    pg.QtCore.QRectF(
                        body_rect["x"],
                        body_rect["y"],
                        body_rect["width"],
                        body_rect["height"],
                    )
                )
            else:
                # Doji - pre-computed line
                painter.setPen(pg.mkPen(body_color, width=2))
                painter.drawLine(
                    pg.QtCore.QPointF(body_rect["line_x1"], body_rect["line_y"]),
                    pg.QtCore.QPointF(body_rect["line_x2"], body_rect["line_y"]),
                )

        painter.end()

    def paint(self, painter, option, widget):
        """Paint using pre-computed picture."""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        """Return pre-computed bounding rectangle."""
        return pg.QtCore.QRectF(
            self.bounds.get("x_min", 0),
            self.bounds.get("y_min", 0),
            self.bounds.get("x_max", 1) - self.bounds.get("x_min", 0),
            self.bounds.get("y_max", 1) - self.bounds.get("y_min", 0),
        )


class RebasedCandlestickItem(pg.GraphicsObject):
    """Candlestick graphics item for rebased percentage data."""

    def __init__(self, rebased_data, bullish_color="#4CAF50", bearish_color="#F44336"):
        super().__init__()
        self.rebased_data = (
            rebased_data  # List of (idx, open_pct, high_pct, low_pct, close_pct)
        )
        self.bullish_color = bullish_color
        self.bearish_color = bearish_color

        # Pre-render the candlesticks
        self.picture = pg.QtGui.QPicture()
        self._generate_candlesticks()

    def _generate_candlesticks(self):
        """Generate candlestick graphics for rebased percentage data."""
        painter = pg.QtGui.QPainter(self.picture)
        painter.setRenderHint(pg.QtGui.QPainter.RenderHint.Antialiasing)

        # Candlestick width
        width = 0.6

        for idx, open_pct, high_pct, low_pct, close_pct in self.rebased_data:
            x = float(idx)
            o = float(open_pct)
            h = float(high_pct)
            l = float(low_pct)
            c = float(close_pct)

            # Skip invalid data - avoid numpy NaN checking
            if o != o or h != h or l != l or c != c:  # NaN != NaN in Python
                continue

            # Determine colors based on percentage change
            if c >= o:  # Bullish
                body_color = self.bullish_color
                wick_color = self.bullish_color
            else:  # Bearish
                body_color = self.bearish_color
                wick_color = self.bearish_color

            # Draw wick (high-low line)
            painter.setPen(pg.mkPen(wick_color, width=1))
            painter.drawLine(pg.QtCore.QPointF(x, l), pg.QtCore.QPointF(x, h))

            # Draw body (open-close rectangle)
            body_top = max(o, c)
            body_bottom = min(o, c)
            body_height = body_top - body_bottom

            if body_height > 0.01:  # Minimum height for visibility
                painter.setPen(pg.mkPen(body_color, width=1))
                painter.setBrush(pg.mkBrush(body_color))
                painter.drawRect(
                    pg.QtCore.QRectF(x - width / 2, body_bottom, width, body_height)
                )
            else:
                # Doji - draw a line
                painter.setPen(pg.mkPen(body_color, width=2))
                painter.drawLine(
                    pg.QtCore.QPointF(x - width / 2, o),
                    pg.QtCore.QPointF(x + width / 2, o),
                )

        painter.end()

    def paint(self, painter, option, widget):
        """Paint the candlesticks."""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self):
        """Return the bounding rectangle."""
        if not self.rebased_data:
            return pg.QtCore.QRectF()

        indices = [item[0] for item in self.rebased_data]
        highs = [item[2] for item in self.rebased_data]
        lows = [item[3] for item in self.rebased_data]

        x_min = min(indices) - 1
        x_max = max(indices) + 1
        y_min = min(lows)
        y_max = max(highs)

        return pg.QtCore.QRectF(x_min, y_min, x_max - x_min, y_max - y_min)


class PeakTroughAnnotations:
    """Optimized PyQt6 visualization for peak and trough annotations with rays"""

    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.annotation_items = []
        # Pre-create pen styles for performance
        self.dash_pen_cache = {}
        self.solid_pen_cache = {}

    def clear_annotations(self):
        """Optimized clearing of annotations."""
        # Batch remove for better performance
        for item in self.annotation_items:
            self.plot_widget.removeItem(item)
        self.annotation_items.clear()  # Use clear() instead of = []

    def _get_pen(self, color, width, style):
        """Cached pen creation for performance"""
        cache = self.dash_pen_cache if style == "dash" else self.solid_pen_cache
        key = (color, width)

        if key not in cache:
            qt_style = (
                pg.QtCore.Qt.PenStyle.DashLine
                if style == "dash"
                else pg.QtCore.Qt.PenStyle.SolidLine
            )
            cache[key] = pg.mkPen(color=color, width=width, style=qt_style)

        return cache[key]

    def draw_annotations(self, display_data):
        """Optimized drawing of annotations from backend-prepared display data"""
        # Clear existing annotations
        self.clear_annotations()

        # Pre-allocate list for better performance
        new_items = []

        # Batch process display data for performance
        for item in display_data:
            if item["type"] == "ray":
                # Draw ray line with cached pen
                pen = self._get_pen(item["color"], item["width"], item["style"])
                ray_line = self.plot_widget.plot(
                    [item["start_x"], item["end_x"]],
                    [item["start_y"], item["end_y"]],
                    pen=pen,
                )

                # Store annotation type for color updates
                ray_line.annotation_type = "peak" if item["color"] == "g" else "trough"

                new_items.append(ray_line)

            elif item["type"] == "label":
                # Create very small text label
                font_size = item.get("font_size", 6)  # Default to 6pt if not specified

                # Create QFont for very small text
                font = pg.QtGui.QFont()
                font.setPointSize(font_size)
                font.setBold(True)

                # Create optimized text item
                label = pg.TextItem(
                    text=item["text"], color=item["color"], anchor=item["anchor"]
                )
                label.setFont(font)
                label.setPos(item["x"], item["y"])

                # Store annotation type for color updates
                label.annotation_type = "peak" if item["color"] == "g" else "trough"

                self.plot_widget.addItem(label)
                new_items.append(label)

        # Batch assign for performance
        self.annotation_items = new_items

    def update_peaks_color(self, color):
        """Update the color of all peak annotations by triggering a refresh."""
        print(f"Peaks color updated to: {color} (will apply on next chart refresh)")

    def update_troughs_color(self, color):
        """Update the color of all trough annotations by triggering a refresh."""
        print(f"Troughs color updated to: {color} (will apply on next chart refresh)")


class DrawingTools:
    """Drawing tools for chart annotation."""

    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.current_tool = None
        self.drawing_items = []
        self.current_item = None
        self.start_pos = None
        self.drawing = False
        self.selected_item = None
        self.moving_item = False
        self.item_labels = {}
        self.deleted_items = []  # Stack for undo functionality
        self.redone_items = []  # Stack for redo functionality
        self.anchored_texts = []  # List to track anchored text items
        self.colors = {
            "White": "#FFFFFF",
            "Red": "#FF0000",
            "Green": "#00FF00",
            "Blue": "#0000FF",
            "Yellow": "#FFFF00",
            "Magenta": "#FF00FF",
            "Cyan": "#00FFFF",
            "Orange": "#FFA500",
        }

    def start_drawing(self, pos, tool_type):
        """Start drawing a new item."""
        self.current_tool = tool_type
        self.start_pos = pos
        self.drawing = True
        if tool_type == "line":
            self.current_item = pg.PlotDataItem(pen=pg.mkPen("w", width=2))
        elif tool_type == "rectangle":
            self.current_item = pg.PlotDataItem(pen=pg.mkPen("w", width=2))
        elif tool_type == "horizontal":
            self.current_item = pg.InfiniteLine(
                angle=0, movable=True, pen=pg.mkPen("w", width=2)
            )
        elif tool_type == "vertical":
            self.current_item = pg.InfiniteLine(
                angle=90, movable=True, pen=pg.mkPen("w", width=2)
            )
        elif tool_type == "text":
            self.current_item = pg.TextItem(text="", color="w")
        if self.current_item:
            self.plot_widget.addItem(self.current_item)

    def update_drawing(self, pos):
        """Update drawing in progress."""
        if not self.drawing or not self.current_item:
            return
        if self.current_tool == "line":
            self.current_item.setData(
                [self.start_pos.x(), pos.x()], [self.start_pos.y(), pos.y()]
            )
        elif self.current_tool == "rectangle":
            x = [
                self.start_pos.x(),
                self.start_pos.x(),
                pos.x(),
                pos.x(),
                self.start_pos.x(),
            ]
            y = [
                self.start_pos.y(),
                pos.y(),
                pos.y(),
                self.start_pos.y(),
                self.start_pos.y(),
            ]
            self.current_item.setData(x, y)
        elif self.current_tool == "horizontal":
            self.current_item.setPos(pos.y())
        elif self.current_tool == "vertical":
            self.current_item.setPos(pos.x())
        elif self.current_tool == "text":
            self.current_item.setPos(pos.x(), pos.y())

    def finish_drawing(self, pos=None):
        """Finish drawing current item."""
        if self.current_item:
            if pos:
                self.update_drawing(pos)
            self.drawing_items.append(self.current_item)
        self.current_item = None
        self.start_pos = None
        self.drawing = False

    def add_text(self, pos, text):
        """Add text item at position."""
        text_item = pg.TextItem(text=text, color="w")
        text_item.setPos(pos.x(), pos.y())
        self.plot_widget.addItem(text_item)
        self.drawing_items.append(text_item)

    def clear_all(self):
        """Clear all drawing items."""
        # Store all items for potential undo
        deleted_batch = []
        for item in self.drawing_items:
            deleted_info = {"item": item, "label": None}
            if item in self.item_labels:
                deleted_info["label"] = self.item_labels[item]
                self.plot_widget.removeItem(self.item_labels[item])
            self.plot_widget.removeItem(item)
            deleted_batch.append(deleted_info)

        # Add the batch as a single undo operation
        if deleted_batch:
            self.deleted_items.append(deleted_batch)

        # Clear redo stack
        self.redone_items.clear()

        self.drawing_items.clear()
        self.item_labels.clear()
        self.current_item = None
        self.start_pos = None
        self.drawing = False
        self.selected_item = None

    def undo(self):
        """Undo the last drawing operation."""
        if not self.deleted_items:
            return

        last_deleted = self.deleted_items.pop()

        # Check if it's a batch deletion (from clear_all)
        if isinstance(last_deleted, list):
            restored_batch = []
            for deleted_info in last_deleted:
                item = deleted_info["item"]
                label = deleted_info["label"]

                # Restore the item
                self.plot_widget.addItem(item)
                self.drawing_items.append(item)

                # Restore the label if it exists
                if label:
                    self.plot_widget.addItem(label)
                    self.item_labels[item] = label

                restored_batch.append(deleted_info)

            # Add to redo stack as a batch
            self.redone_items.append(restored_batch)
        else:
            # Single item deletion
            item = last_deleted["item"]
            label = last_deleted["label"]

            # Restore the item
            self.plot_widget.addItem(item)
            self.drawing_items.append(item)

            # Restore the label if it exists
            if label:
                self.plot_widget.addItem(label)
                self.item_labels[item] = label

            # Add to redo stack
            self.redone_items.append(last_deleted)

    def erase_item_at_position(self, pos, tolerance=2.0):
        """Erase drawing item at the given position with tolerance."""
        print(f"Eraser: Checking position {pos.x():.2f}, {pos.y():.2f}")
        print(f"Eraser: Found {len(self.drawing_items)} drawing items to check")

        # Check each drawing item to see if it's close to the click position
        for i, item in enumerate(
            self.drawing_items[:]
        ):  # Use slice to avoid modification during iteration
            print(f"Eraser: Checking item {i}: {type(item).__name__}")

            if self._is_item_at_position(item, pos, tolerance):
                print(f"Eraser: Hit detected on item {i}, removing it")

                # Remove the item
                self.plot_widget.removeItem(item)
                self.drawing_items.remove(item)

                # Handle associated label if exists
                label = None
                if item in self.item_labels:
                    label = self.item_labels[item]
                    self.plot_widget.removeItem(label)
                    del self.item_labels[item]

                # Add to undo stack
                deleted_info = {"item": item, "label": label}
                self.deleted_items.append(deleted_info)

                # Clear redo stack since we made a new change
                self.redone_items.clear()

                return True  # Successfully erased an item

        print("Eraser: No item found at click position")
        return False  # No item found at position

    def _is_item_at_position(self, item, view_pos, tolerance):
        """Check if a drawing item is at the given position within tolerance."""
        try:
            if isinstance(item, pg.PlotDataItem):
                # For lines and rectangles, check if click is near the line
                x_data = item.xData
                y_data = item.yData
                print(
                    f"Eraser: PlotDataItem has {len(x_data) if x_data is not None else 0} points"
                )

                if x_data is not None and y_data is not None and len(x_data) > 0:
                    # For lines, check distance to line segments
                    if len(x_data) == 2:  # Simple line
                        # Check distance from point to line segment
                        x1, y1 = x_data[0], y_data[0]
                        x2, y2 = x_data[1], y_data[1]
                        distance = self._point_to_line_distance(
                            view_pos.x(), view_pos.y(), x1, y1, x2, y2
                        )
                        print(
                            f"Eraser: Line distance = {distance:.3f}, tolerance = {tolerance}"
                        )
                        if distance <= tolerance:
                            return True
                    else:
                        # For rectangles or complex shapes, check proximity to any point
                        for i in range(len(x_data)):
                            distance = (
                                (view_pos.x() - x_data[i]) ** 2
                                + (view_pos.y() - y_data[i]) ** 2
                            ) ** 0.5
                            print(f"Eraser: Point {i} distance = {distance:.3f}")
                            if distance <= tolerance:
                                return True

            elif isinstance(item, pg.InfiniteLine):
                # For infinite lines, check if click is near the line
                print(
                    f"Eraser: InfiniteLine angle = {item.angle}, value = {item.value()}"
                )
                if item.angle == 0:  # Horizontal line
                    line_y = item.value()
                    distance = abs(view_pos.y() - line_y)
                    print(f"Eraser: Horizontal line distance = {distance:.3f}")
                    if distance <= tolerance:
                        return True
                elif item.angle == 90:  # Vertical line
                    line_x = item.value()
                    distance = abs(view_pos.x() - line_x)
                    print(f"Eraser: Vertical line distance = {distance:.3f}")
                    if distance <= tolerance:
                        return True

            elif isinstance(item, pg.TextItem):
                # For text items, check if click is near the text position
                text_pos = item.pos()
                print(
                    f"Eraser: TextItem at position ({text_pos.x():.2f}, {text_pos.y():.2f})"
                )
                print(
                    f"Eraser: Click at position ({view_pos.x():.2f}, {view_pos.y():.2f})"
                )

                # Use a larger tolerance for text items since they can be small
                text_tolerance = max(tolerance * 2, 1.0)  # At least 1.0 units tolerance
                distance = (
                    (view_pos.x() - text_pos.x()) ** 2
                    + (view_pos.y() - text_pos.y()) ** 2
                ) ** 0.5
                print(
                    f"Eraser: Text distance = {distance:.3f}, tolerance = {text_tolerance:.3f}"
                )
                if distance <= text_tolerance:
                    return True

        except Exception as e:
            print(f"Error checking item position: {e}")

        return False

    def _point_to_line_distance(self, px, py, x1, y1, x2, y2):
        """Calculate the distance from a point to a line segment."""
        # Vector from line start to end
        dx = x2 - x1
        dy = y2 - y1

        # If the line segment is actually a point
        if dx == 0 and dy == 0:
            return ((px - x1) ** 2 + (py - y1) ** 2) ** 0.5

        # Parameter t represents position along the line segment
        t = max(0, min(1, ((px - x1) * dx + (py - y1) * dy) / (dx * dx + dy * dy)))

        # Find the closest point on the line segment
        closest_x = x1 + t * dx
        closest_y = y1 + t * dy

        # Return distance from point to closest point on line
        return ((px - closest_x) ** 2 + (py - closest_y) ** 2) ** 0.5

    def redo(self):
        """Redo the last undone drawing operation."""
        if not self.redone_items:
            return

        last_redone = self.redone_items.pop()

        # Check if it's a batch operation
        if isinstance(last_redone, list):
            deleted_batch = []
            for redone_info in last_redone:
                item = redone_info["item"]
                label = redone_info["label"]

                # Remove the item
                self.plot_widget.removeItem(item)
                self.drawing_items.remove(item)

                # Remove the label if it exists
                if label:
                    self.plot_widget.removeItem(label)
                    if item in self.item_labels:
                        del self.item_labels[item]

                deleted_batch.append(redone_info)

            # Add to deleted items stack as a batch
            self.deleted_items.append(deleted_batch)
        else:
            # Single item operation
            item = last_redone["item"]
            label = last_redone["label"]

            # Remove the item
            self.plot_widget.removeItem(item)
            self.drawing_items.remove(item)

            # Remove the label if it exists
            if label:
                self.plot_widget.removeItem(label)
                if item in self.item_labels:
                    del self.item_labels[item]

            # Add to deleted items stack
            self.deleted_items.append(last_redone)


class CrosshairManager:
    """Manages unified crosshair display across multiple charts."""

    def __init__(self, main_chart, secondary_chart=None, parent_widget=None):
        self.main_chart = main_chart
        self.secondary_chart = secondary_chart
        self.parent_widget = (
            parent_widget  # Reference to MarketOddsTab for information updates
        )
        self.is_enabled = True
        self.current_mouse_pos = None

        # Create invisible cursor for chart areas
        self.invisible_cursor = self._create_invisible_cursor()
        self.original_main_cursor = self.main_chart.cursor()
        if self.secondary_chart:
            self.original_secondary_cursor = self.secondary_chart.cursor()

        # Create single unified crosshair lines that extend across both charts
        # Vertical line extends through both charts
        self.v_line_main = pg.InfiniteLine(
            angle=90,
            movable=False,
            pen=pg.mkPen("#FFFFFF", width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
        )
        self.v_line_secondary = pg.InfiniteLine(
            angle=90,
            movable=False,
            pen=pg.mkPen("#FFFFFF", width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
        )

        # Horizontal lines for each chart (separate Y coordinates)
        self.h_line_main = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen("#FFFFFF", width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
        )
        self.h_line_secondary = pg.InfiniteLine(
            angle=0,
            movable=False,
            pen=pg.mkPen("#FFFFFF", width=1, style=pg.QtCore.Qt.PenStyle.DashLine),
        )

        # Single solid cross at the mouse position (only on the chart where mouse is)
        self.cross_item = pg.PlotDataItem(
            pen=pg.mkPen("#FFFFFF", width=1),
            symbol="+",
            symbolSize=10,
            symbolBrush="#FFFFFF",
            symbolPen="#FFFFFF",
        )

        # Add vertical lines to both charts for unified appearance
        self.main_chart.addItem(self.v_line_main, ignoreBounds=True)
        self.main_chart.addItem(self.h_line_main, ignoreBounds=True)

        if self.secondary_chart:
            self.secondary_chart.addItem(self.v_line_secondary, ignoreBounds=True)
            self.secondary_chart.addItem(self.h_line_secondary, ignoreBounds=True)

        # Cross will be added to whichever chart the mouse is over
        self.cross_chart = None

        # Connect mouse events with specific handlers for each chart
        self.main_chart.scene().sigMouseMoved.connect(self.mouse_moved_main)
        if self.secondary_chart:
            self.secondary_chart.scene().sigMouseMoved.connect(
                self.mouse_moved_secondary
            )

        # Also connect to leave events for immediate hiding
        self.main_chart.leaveEvent = self.create_leave_handler("main")
        if self.secondary_chart:
            self.secondary_chart.leaveEvent = self.create_leave_handler("secondary")

        # Initially hide crosshair
        self.hide_crosshair()

    def _create_invisible_cursor(self):
        """Create an invisible cursor for chart areas."""
        # Create a 1x1 transparent pixmap
        pixmap = QPixmap(1, 1)
        pixmap.fill(Qt.GlobalColor.transparent)
        return QCursor(pixmap)

    def create_leave_handler(self, chart_name):
        """Create a leave event handler for the specified chart."""

        def leave_handler(event):
            # Restore original cursor
            if chart_name == "main":
                self.main_chart.setCursor(self.original_main_cursor)
            elif chart_name == "secondary" and hasattr(
                self, "original_secondary_cursor"
            ):
                self.secondary_chart.setCursor(self.original_secondary_cursor)

            self.hide_crosshair()
            # Call original leave event if it exists
            if hasattr(event, "ignore"):
                event.ignore()

        return leave_handler

    def mouse_moved_main(self, evt):
        """Handle mouse movement on main chart."""
        if not self.is_enabled:
            return

        try:
            pos = evt
            if self.main_chart.sceneBoundingRect().contains(pos):
                # Set invisible cursor for chart area
                self.main_chart.setCursor(self.invisible_cursor)

                mouse_point = self.main_chart.getViewBox().mapSceneToView(pos)
                self.update_crosshair_position(mouse_point.x(), mouse_point.y(), "main")
                self.show_crosshair()

                # Update information panel with crosshair data
                if self.parent_widget and hasattr(
                    self.parent_widget, "_update_crosshair_info"
                ):
                    self.parent_widget._update_crosshair_info(
                        mouse_point.x(), mouse_point.y()
                    )
            else:
                # Mouse left main chart - restore original cursor and hide crosshair
                self.main_chart.setCursor(self.original_main_cursor)
                self.hide_crosshair()

                # Clear information panel
                if self.parent_widget and hasattr(
                    self.parent_widget, "_clear_crosshair_info"
                ):
                    self.parent_widget._clear_crosshair_info()
        except Exception as e:
            print(f"Main chart mouse error: {e}")
            self.main_chart.setCursor(self.original_main_cursor)
            self.hide_crosshair()

    def mouse_moved_secondary(self, evt):
        """Handle mouse movement on secondary chart."""
        if not self.is_enabled:
            return

        try:
            pos = evt
            if (
                self.secondary_chart
                and self.secondary_chart.sceneBoundingRect().contains(pos)
            ):
                # Set invisible cursor for chart area
                self.secondary_chart.setCursor(self.invisible_cursor)

                mouse_point = self.secondary_chart.getViewBox().mapSceneToView(pos)
                self.update_crosshair_position(
                    mouse_point.x(), mouse_point.y(), "secondary"
                )
                self.show_crosshair()

                # Update information panel with crosshair data (use X from secondary, but get candle data)
                if self.parent_widget and hasattr(
                    self.parent_widget, "_update_crosshair_info"
                ):
                    self.parent_widget._update_crosshair_info(
                        mouse_point.x(), mouse_point.y()
                    )
            else:
                # Mouse left secondary chart - restore original cursor and hide crosshair
                if hasattr(self, "original_secondary_cursor"):
                    self.secondary_chart.setCursor(self.original_secondary_cursor)
                self.hide_crosshair()

                # Clear information panel
                if self.parent_widget and hasattr(
                    self.parent_widget, "_clear_crosshair_info"
                ):
                    self.parent_widget._clear_crosshair_info()
        except Exception as e:
            print(f"Secondary chart mouse error: {e}")
            if hasattr(self, "original_secondary_cursor"):
                self.secondary_chart.setCursor(self.original_secondary_cursor)
            self.hide_crosshair()

    def mouse_moved(self, evt):
        """Handle mouse movement to update unified crosshair position."""
        if not self.is_enabled:
            return

        try:
            # Get mouse position in scene coordinates
            pos = evt

            # Debug: Print which chart is receiving the event
            is_main_chart_event = False
            is_secondary_chart_event = False

            # Check if mouse is within main chart bounds
            try:
                if self.main_chart.sceneBoundingRect().contains(pos):
                    mouse_point = self.main_chart.getViewBox().mapSceneToView(pos)
                    self.update_crosshair_position(
                        mouse_point.x(), mouse_point.y(), "main"
                    )
                    self.show_crosshair()
                    is_main_chart_event = True
                    print(
                        f"Main chart: x={mouse_point.x():.2f}, y={mouse_point.y():.2f}"
                    )
            except Exception as e:
                print(f"Main chart error: {e}")

            # Check if mouse is within secondary chart bounds (only if not in main chart)
            if not is_main_chart_event and self.secondary_chart:
                try:
                    if self.secondary_chart.sceneBoundingRect().contains(pos):
                        mouse_point = self.secondary_chart.getViewBox().mapSceneToView(
                            pos
                        )
                        self.update_crosshair_position(
                            mouse_point.x(), mouse_point.y(), "secondary"
                        )
                        self.show_crosshair()
                        is_secondary_chart_event = True
                        print(
                            f"Secondary chart: x={mouse_point.x():.2f}, y={mouse_point.y():.2f}"
                        )
                except Exception as e:
                    print(f"Secondary chart error: {e}")

            # Hide crosshair if not over either chart
            if not is_main_chart_event and not is_secondary_chart_event:
                self.hide_crosshair()

        except Exception as e:
            print(f"Mouse moved error: {e}")

    def update_crosshair_position(self, x, y, chart_type):
        """Update unified crosshair position across both charts."""
        # Update vertical lines on both charts (same X position)
        self.v_line_main.setPos(x)
        if self.secondary_chart:
            self.v_line_secondary.setPos(x)

        # Update horizontal lines based on which chart the mouse is over
        if chart_type == "main":
            # Mouse is over main chart
            self.h_line_main.setPos(y)
            self.h_line_main.show()

            # Hide secondary chart horizontal line
            if self.secondary_chart:
                self.h_line_secondary.hide()

            # Remove cross from secondary chart if it was there
            if self.cross_chart == "secondary":
                try:
                    self.secondary_chart.removeItem(self.cross_item)
                except:
                    pass

            # Add cross to main chart
            if self.cross_chart != "main":
                try:
                    self.main_chart.addItem(self.cross_item, ignoreBounds=True)
                    self.cross_chart = "main"
                except Exception as e:
                    print(f"Error adding cross to main chart: {e}")

            # Update cross position
            self.cross_item.setData([x], [y])

        elif chart_type == "secondary" and self.secondary_chart:
            # Mouse is over secondary chart
            self.h_line_secondary.setPos(y)
            self.h_line_secondary.show()

            # Hide main chart horizontal line
            self.h_line_main.hide()

            # Remove cross from main chart if it was there
            if self.cross_chart == "main":
                try:
                    self.main_chart.removeItem(self.cross_item)
                except:
                    pass

            # Add cross to secondary chart
            if self.cross_chart != "secondary":
                try:
                    self.secondary_chart.addItem(self.cross_item, ignoreBounds=True)
                    self.cross_chart = "secondary"
                except Exception as e:
                    print(f"Error adding cross to secondary chart: {e}")

            # Update cross position
            self.cross_item.setData([x], [y])

    def show_crosshair(self):
        """Show unified crosshair lines."""
        # Always show vertical lines on both charts
        self.v_line_main.show()
        if self.secondary_chart:
            self.v_line_secondary.show()

        # Horizontal lines are shown/hidden individually based on mouse position
        # (handled in update_crosshair_position method)

        # Cross is shown automatically when added to the appropriate chart
        if hasattr(self.cross_item, "show"):
            self.cross_item.show()

    def hide_crosshair(self):
        """Hide unified crosshair lines immediately."""
        # Hide all crosshair elements immediately
        try:
            self.v_line_main.hide()
            self.h_line_main.hide()

            if self.secondary_chart:
                self.v_line_secondary.hide()
                self.h_line_secondary.hide()

            # Hide cross and remove from whichever chart it's on
            if hasattr(self.cross_item, "hide"):
                self.cross_item.hide()

            # Remove cross from both charts to ensure it's gone
            if self.cross_chart == "main":
                try:
                    self.main_chart.removeItem(self.cross_item)
                except:
                    pass
            elif self.cross_chart == "secondary" and self.secondary_chart:
                try:
                    self.secondary_chart.removeItem(self.cross_item)
                except:
                    pass

            self.cross_chart = None

        except Exception as e:
            print(f"Error hiding crosshair: {e}")

    def set_enabled(self, enabled):
        """Enable or disable crosshair."""
        self.is_enabled = enabled
        if not enabled:
            self.hide_crosshair()


class BollingerBandwidthChart(PlotWidget):
    """Chart widget for displaying Bollinger Bandwidth indicator."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_chart()
        self.bandwidth_line = None
        self.donchian_strip_items = []  # Store Donchian strip items

    def setup_chart(self):
        """Setup the Bollinger Bandwidth chart widget."""
        # Configure appearance
        self.setBackground("#1e1e1e")
        # Remove axis titles - no labels for cleaner appearance
        self.setLabel("left", "")
        # Keep bottom axis visible since this will be the only chart showing X-axis
        self.setLabel("bottom", "Time")

        # Enable mouse events for crosshair detection but disable panning/zooming
        # We need mouse events for crosshair but don't want user interaction
        self.enableAutoRange("xy", False)

        # Disable grid
        self.showGrid(x=False, y=False)

        # Configure axes - keep Y-axis structure but make it invisible for perfect alignment
        self.getAxis("left").setStyle(showValues=False)  # Hide Y-axis values
        self.getAxis("left").setPen(
            color="#cccccc", width=1
        )  # White Y-axis line (same as main chart)
        self.getAxis("left").setTextPen("transparent")  # Hide Y-axis text
        self.getAxis("bottom").setPen(color="#cccccc", width=1)  # Keep X-axis
        self.getAxis("bottom").setTextPen("#cccccc")  # Keep X-axis text

        # Keep the same margins as main chart for perfect alignment
        # Don't remove margins - this ensures the plot areas align perfectly

        # Set smaller font for axis text
        font = QFont("Segoe UI", 8)
        self.getAxis("left").setTickFont(font)
        self.getAxis("bottom").setTickFont(font)

        # Disable right-click context menu and all interactions
        self.setMenuEnabled(False)
        plot_item = self.getPlotItem()
        plot_item.setMenuEnabled(False)
        plot_item.hideButtons()  # Hide all buttons including anchor
        view_box = plot_item.getViewBox()
        view_box.setMenuEnabled(False)

        # Also disable the auto-range button specifically
        try:
            plot_item.autoBtn.hide()
            plot_item.vb.setMenuEnabled(False)
        except:
            pass  # Ignore if buttons don't exist

        # Disable ViewBox interactions but allow mouse events for crosshair
        # Don't disable mouse events completely - we need them for crosshair detection
        view_box.enableAutoRange(enable=False)

        # Disable specific interactions while keeping mouse events
        view_box.setMouseMode(view_box.RectMode)  # Disable pan mode
        view_box.setMenuEnabled(False)

        # Store reference to view box for synchronization
        self.view_box = view_box

    def update_bandwidth(
        self, timestamps, bandwidth_values, donchian_strip_segments=None
    ):
        """Update the Bollinger Bandwidth chart with optional Donchian strip."""
        # Clear existing items
        if self.bandwidth_line is not None:
            self.removeItem(self.bandwidth_line)
            self.bandwidth_line = None

        for item in self.donchian_strip_items:
            self.removeItem(item)
        self.donchian_strip_items.clear()

        # Clear existing percentage levels
        if hasattr(self, "percentage_level_lines"):
            for line in self.percentage_level_lines:
                self.removeItem(line)
            self.percentage_level_lines.clear()
        else:
            self.percentage_level_lines = []

        if len(timestamps) == 0 or len(bandwidth_values) == 0:
            self.setVisible(False)  # Hide chart when no data
            return

        # Filter valid data - Pure PyQt6 visualization logic
        valid_timestamps = []
        valid_bandwidth = []

        for i, bandwidth_val in enumerate(bandwidth_values):
            # Check for NaN using Python comparison (NaN != NaN)
            if bandwidth_val == bandwidth_val:  # Not NaN
                valid_timestamps.append(timestamps[i])
                valid_bandwidth.append(bandwidth_val)

        if len(valid_bandwidth) == 0:
            self.setVisible(False)  # Hide chart when no valid data
            return

        # Always show chart, but handle no movement case differently
        self.setVisible(True)

        # Check if there's any movement in the bandwidth values - Pure PyQt6 visualization
        has_movement = True
        if len(valid_bandwidth) > 1:
            # Pure visualization logic - use iteration instead of mathematical functions
            bandwidth_min = valid_bandwidth[0]
            bandwidth_max = valid_bandwidth[0]
            for value in valid_bandwidth:
                if value < bandwidth_min:
                    bandwidth_min = value
                if value > bandwidth_max:
                    bandwidth_max = value

            bandwidth_range = bandwidth_max - bandwidth_min
            # If the range is very small (no significant movement), don't plot bandwidth line
            if bandwidth_range < 0.001:  # Threshold for "no movement"
                has_movement = False

        # Plot the bandwidth line as simple white line
        if has_movement:
            self.bandwidth_line = self.plot(
                valid_timestamps,
                valid_bandwidth,
                pen=pg.mkPen("#FFFFFF", width=2),  # White line
                name="Bollinger Bandwidth",
            )

        # Add Donchian strip - position based on whether there's bandwidth movement
        if donchian_strip_segments:
            if has_movement and len(valid_bandwidth) > 0:
                # Position strip 10% above the highest bandwidth point when there's movement
                self._add_donchian_strip(
                    timestamps, donchian_strip_segments, valid_bandwidth
                )
            else:
                # Position strip at y=0 when there's no bandwidth movement
                self._add_donchian_strip_at_zero(timestamps, donchian_strip_segments)

        # Set proper view range - only set X range if not synchronized with main chart
        if len(valid_timestamps) > 0:
            if not hasattr(self, "main_chart_view_box"):
                # Not synchronized, set our own X range
                self.setXRange(valid_timestamps[0], valid_timestamps[-1], padding=0)
            # If synchronized, the main chart will control our X range

        # Set Y range based on whether there's movement
        if has_movement:
            # Auto-range Y only, preserve X range
            self.enableAutoRange("y", True)
            self.autoRange()
            self.enableAutoRange("y", False)  # Disable after setting

            # Add percentage levels after setting the range
            self._add_percentage_levels(valid_bandwidth)
        else:
            # When no movement, set a small range around y=0 for the strip
            self.setYRange(-0.5, 0.5)

    def _add_donchian_strip(self, timestamps, strip_segments, bandwidth_values):
        """Add Donchian strip 10% above the highest bandwidth point."""
        if len(bandwidth_values) == 0:
            return

        # Find the highest bandwidth value - Pure PyQt6 visualization logic
        max_bandwidth = bandwidth_values[0]
        for value in bandwidth_values:
            if value > max_bandwidth:
                max_bandwidth = value

        # Position strip 10% above the highest point - Pure visualization calculation
        strip_y_position = max_bandwidth * 1.1

        # Draw each segment as a continuous line
        for i, segment in enumerate(strip_segments):
            start_x = timestamps[segment["start_idx"]]
            end_x = timestamps[segment["end_idx"]]

            # For continuous appearance, extend to connect with next segment
            if i < len(strip_segments) - 1:
                next_segment = strip_segments[i + 1]
                next_start_x = timestamps[next_segment["start_idx"]]
                # Extend current segment to meet the next one
                end_x = next_start_x

            # Create a 1px thick line for the segment
            strip_item = self.plot(
                [start_x, end_x],
                [strip_y_position, strip_y_position],
                pen=pg.mkPen(color=segment["color"], width=1),  # 1px thick line
            )
            self.donchian_strip_items.append(strip_item)

    def _add_donchian_strip_at_zero(self, timestamps, strip_segments):
        """Add Donchian strip at y=0 when there's no bandwidth movement."""
        if not strip_segments:
            return

        # Position strip at y=0
        strip_y_position = 0.0

        # Draw each segment as a continuous line at y=0
        for i, segment in enumerate(strip_segments):
            start_x = timestamps[segment["start_idx"]]
            end_x = timestamps[segment["end_idx"]]

            # For continuous appearance, extend to connect with next segment
            if i < len(strip_segments) - 1:
                next_segment = strip_segments[i + 1]
                next_start_x = timestamps[next_segment["start_idx"]]
                # Extend current segment to meet the next one
                end_x = next_start_x

            # Create a 1px thick line for the segment at y=0
            strip_item = self.plot(
                [start_x, end_x],
                [strip_y_position, strip_y_position],
                pen=pg.mkPen(color=segment["color"], width=1),  # 1px thick line
            )
            self.donchian_strip_items.append(strip_item)

    def _add_continuous_bandwidth_segment(self, timestamps, bandwidth_values, color):
        """Add a continuous bandwidth segment with the specified color."""
        if len(timestamps) < 2 or len(bandwidth_values) < 2:
            return

        try:
            # Create a continuous line segment
            segment_item = self.plot(
                timestamps,
                bandwidth_values,
                pen=pg.mkPen(color=color, width=2),  # 2px continuous line
                name=f"Bandwidth Segment",
            )
            self.donchian_strip_items.append(segment_item)  # Store for cleanup

        except Exception as e:
            print(f"Warning: Could not create bandwidth segment: {e}")

    def _add_percentage_levels(self, _):
        """Add percentage levels using pre-computed data from backend - eliminates UI computation."""
        # Use pre-computed percentage levels from backend
        if hasattr(self, "parent") and hasattr(self.parent(), "current_data"):
            current_data = self.parent().current_data
            if "percentage_levels" in current_data:
                levels_data = current_data["percentage_levels"]
                if not levels_data.get("is_valid", False):
                    return

                # Use pre-computed levels
                for level_data in levels_data.get("levels", []):
                    level_value = level_data["level_value"]
                    pct = level_data["percentage"]

                    try:
                        # Create horizontal line
                        line_item = pg.InfiniteLine(
                            pos=level_value,
                            angle=0,  # Horizontal line
                            pen=pg.mkPen(
                                color="#808080",
                                width=1,
                                style=pg.QtCore.Qt.PenStyle.SolidLine,
                            ),  # Grey solid line
                            movable=False,
                        )

                        # Add to chart
                        self.addItem(line_item)
                        self.percentage_level_lines.append(line_item)

                        # Create text label positioned on the right side
                        text_item = pg.TextItem(
                            text=f"{pct}%",
                            color=(128, 128, 128),  # Grey text
                            anchor=(1, 0.5),  # Right-aligned, vertically centered
                        )

                        # Set font size to 8px
                        font = QFont("Segoe UI", 8)
                        text_item.setFont(font)

                        # Position the text on the right side of the chart
                        self._position_percentage_label(text_item, level_value)

                        # Set high z-value to ensure text appears on top
                        text_item.setZValue(1000)

                        self.addItem(text_item)
                        self.percentage_level_lines.append(text_item)

                    except Exception as e:
                        print(f"Warning: Could not create percentage level {pct}%: {e}")
            else:
                # No fallback - UI should never compute
                return
        else:
            # No fallback - UI should never compute
            return

    def _position_percentage_label(self, text_item, y_value):
        """Position a percentage label on the right side of the chart."""
        try:
            # Get the current view range
            view_range = self.viewRange()
            x_range = view_range[0]  # [x_min, x_max]

            # Position the text slightly to the left of the right edge
            x_pos = x_range[1] - (x_range[1] - x_range[0]) * 0.02  # 2% from right edge
            text_item.setPos(x_pos, y_value)

        except Exception:
            # Fallback positioning
            text_item.setPos(0, y_value)

    def sync_with_main_chart(self, main_chart_widget):
        """Synchronize this bandwidth chart's X-axis and Y-axis positioning with the main chart."""
        if main_chart_widget and hasattr(self, "view_box"):
            # Get the main chart's view box
            main_view_box = main_chart_widget.getViewBox()

            # Connect the main chart's range changes to update this chart
            main_view_box.sigRangeChanged.connect(self._on_main_chart_range_changed)

            # Store reference to main chart for synchronization
            self.main_chart_view_box = main_view_box
            self.main_chart_widget = main_chart_widget

    def _on_main_chart_range_changed(self):
        """Handle main chart range changes to synchronize X-axis and Y-axis positioning."""
        try:
            if hasattr(self, "main_chart_view_box") and hasattr(self, "view_box"):
                # Get the main chart's current range
                main_range = self.main_chart_view_box.viewRange()
                main_x_range = main_range[0]  # [x_min, x_max]

                # Apply the same X range to the bandwidth chart, but keep our own Y range
                current_range = self.view_box.viewRange()
                current_y_range = current_range[1]  # Keep current Y range

                # Set the new range with synchronized X but independent Y
                self.view_box.setRange(
                    xRange=main_x_range, yRange=current_y_range, padding=0
                )

                # Synchronize Y-axis positioning (not range, but visual positioning)
                self._sync_y_axis_positioning()

                # Update percentage label positions
                self._update_percentage_label_positions()

        except Exception as e:
            print(f"Warning: Could not synchronize bandwidth chart range: {e}")

    def _sync_y_axis_positioning(self):
        """Synchronize the Y-axis positioning with the main chart."""
        try:
            if hasattr(self, "main_chart_widget"):
                # Get both axes
                main_left_axis = self.main_chart_widget.getPlotItem().getAxis("left")
                bandwidth_left_axis = self.getPlotItem().getAxis("left")

                # Ensure the bandwidth chart's Y-axis has the same width as main chart
                main_axis_width = main_left_axis.width()
                bandwidth_left_axis.setWidth(main_axis_width)

                # Force geometry update to maintain alignment
                self.getPlotItem().getViewBox().updateViewRange()

        except Exception as e:
            print(f"Warning: Could not sync Y-axis positioning: {e}")

    def _update_percentage_label_positions(self):
        """Update the positions of percentage labels when the view range changes."""
        try:
            if hasattr(self, "percentage_level_lines"):
                # Get current view range
                view_range = self.viewRange()
                x_range = view_range[0]  # [x_min, x_max]

                # Update position for text items only
                for item in self.percentage_level_lines:
                    if isinstance(item, pg.TextItem):
                        # Get the current Y position
                        current_pos = item.pos()
                        y_pos = current_pos.y()

                        # Calculate new X position (2% from right edge)
                        x_pos = x_range[1] - (x_range[1] - x_range[0]) * 0.02
                        item.setPos(x_pos, y_pos)

        except Exception as e:
            print(f"Warning: Could not update percentage label positions: {e}")


class MarketOddsTab(QWidget):
    """
    Zero-computation Market Odds tab implementing proper UI thread discipline.
    Uses Model/View architecture with all computations offloaded to worker threads.
    """

    # Backward compatibility signals for main window integration
    data_fetch_completed = pyqtSignal()
    data_updated = pyqtSignal(dict)

    def __init__(self, data_service, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.current_data = None

        # Zero-computation architecture components
        self.chart_model = ChartDataModel(self)
        self.chart_renderer = None  # Initialized after UI setup

        # Use signal manager for all communications
        self.signals = signal_manager

        # Setup structured logging
        self.logger = self._setup_logging()

        # Initialize all components
        self._initialize_components()

        # Setup signal forwarding for backward compatibility
        self._setup_signal_forwarding()

        # Setup computation offloading
        self._setup_computation_callbacks()

    def _setup_logging(self):
        """Setup structured logging for market odds tab."""
        import logging

        logger = logging.getLogger("MarketOddsTab")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _setup_signal_forwarding(self):
        """Setup signal forwarding and centralized data reception."""
        # Connect to centralized data updates from Universal Controls
        self.signals.connect_threadsafe(
            self.signals.market_data.chart_data_updated,
            self.on_data_received,
            Qt.ConnectionType.QueuedConnection,
        )

        # Forward signals to legacy signals for backward compatibility
        self.signals.connect_threadsafe(
            self.signals.market_data.chart_data_updated,
            lambda data: self.data_updated.emit(data),
            Qt.ConnectionType.QueuedConnection,
        )

    def _setup_computation_callbacks(self):
        """Setup callbacks for computation offloader."""
        # Connect to computation offloader signals
        computation_offloader.signals.computation_completed.connect(
            self._on_computation_completed, Qt.ConnectionType.QueuedConnection
        )
        computation_offloader.signals.computation_failed.connect(
            self._on_computation_failed, Qt.ConnectionType.QueuedConnection
        )

    def _initialize_components(self):
        """Initialize all chart components and state variables."""
        self.donchian_line = None
        self.indicator_strip_items = []  # Store indicator strip items
        self.mean_lines = []  # Store mean horizontal lines
        self.rebased_vector_line = None  # Store rebased vector line
        self.predictive_cycle_lines = []  # Store predictive cycle lines
        self.thread_pool = QThreadPool()
        # Always use rebasing - no toggle needed

        # Chart settings
        self.chart_settings = {
            "show_stdev": True,
            "show_candles": True,
            "show_donchian": True,
            "show_mean_horizontal": True,
            "show_mean_line": True,
            "show_second_chart": True,
            "show_peaks_troughs": True,
            "show_right_chart": True,
        }

        # Chart colors
        self.chart_colors = {
            "Donchian midpoint color": "#800080",
            "Peaks color": "#00FF00",
            "Troughs color": "#FF0000",
            "Bullish candle color": "#4CAF50",
            "Bearish candle color": "#F44336",
            "Positive StDev lines color": "#00FF00",
            "Negative StDev lines color": "#FF0000",
            "Mean horizontal lines color": "#FFFF00",
            "Mean Wave Color": "#FFFFFF",
        }

        # Settings persistence - these will be updated when settings change
        self.persistent_settings = self.chart_settings.copy()
        self.persistent_colors = self.chart_colors.copy()

        self.setup_ui()

    def setup_ui(self):
        """Initialize the user interface."""
        # Main vertical layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(
            0
        )  # Remove spacing between charts to merge them visually

        # Horizontal layout to divide chart (3/4) and empty space (1/4)
        horizontal_layout = QHBoxLayout()
        horizontal_layout.setContentsMargins(0, 0, 0, 0)
        horizontal_layout.setSpacing(0)

        # Create main chart widget
        self.setup_chart()

        # Add main chart widget to horizontal layout (5/6 of space)
        horizontal_layout.addWidget(self.chart_widget, 5)  # 5/6 of the space

        # Create right side chart (small fixed width)
        self.right_chart = self._create_right_chart()
        horizontal_layout.addWidget(
            self.right_chart, 0
        )  # Fixed width, not proportional
        self.right_chart.setFixedWidth(15)  # Very small fixed width

        # Right box will fill the remaining 1/4 space (no stretch needed)

        # Create separate bandwidth chart below main chart
        self.bandwidth_chart = BollingerBandwidthChart()
        self.bandwidth_chart.setMaximumHeight(150)  # Fixed height for bandwidth chart

        # Synchronize bandwidth chart with main chart
        self.bandwidth_chart.sync_with_main_chart(self.chart_widget)

        # Create horizontal layout for bandwidth chart (same 3/4 sizing as main chart)
        bandwidth_horizontal_layout = QHBoxLayout()
        bandwidth_horizontal_layout.setContentsMargins(0, 0, 0, 0)
        bandwidth_horizontal_layout.setSpacing(0)

        # Add bandwidth chart to fill the FULL width (matching the entire charts layout above)
        bandwidth_horizontal_layout.addWidget(
            self.bandwidth_chart, 1
        )  # Fill all available space

        # Add matching 15px spacer to align with the right chart in main layout
        spacer_widget = QWidget()
        spacer_widget.setFixedWidth(15)  # Same width as right_chart
        bandwidth_horizontal_layout.addWidget(
            spacer_widget, 0
        )  # Fixed width, not proportional

        # No stretch needed - bandwidth chart should extend under the right box area

        # Create vertical layout for charts (main + bandwidth)
        charts_layout = QVBoxLayout()
        charts_layout.setContentsMargins(0, 0, 0, 0)
        charts_layout.setSpacing(0)
        charts_layout.addLayout(horizontal_layout)
        charts_layout.addLayout(bandwidth_horizontal_layout)

        # Create main horizontal layout to hold charts and right box
        main_horizontal_layout = QHBoxLayout()
        main_horizontal_layout.setContentsMargins(0, 0, 0, 0)
        main_horizontal_layout.setSpacing(0)

        # Add charts layout and right box to main horizontal layout
        main_horizontal_layout.addLayout(charts_layout, 5)  # Charts take 5/6 of space
        # Right box will be added after it's created in _setup_right_box() with 1/6 proportion

        # Add main horizontal layout to main layout
        main_layout.addLayout(main_horizontal_layout)

        # Store reference to main horizontal layout for adding right box later
        self.main_horizontal_layout = main_horizontal_layout

        # Create a unified Y-axis appearance by aligning the charts
        self._align_chart_axes()

        # Initialize crosshair after all charts are created
        self._setup_crosshair()

        # Create right box with information, drawing tools, and settings
        self._setup_right_box()

        # Initialize drawing tools
        self.drawing_tools = DrawingTools(self.chart_widget)
        self.current_drawing_tool = None

        # Initialize chart renderer after UI setup
        self.chart_renderer = ChartRenderer(self.chart_widget, self.chart_model, self)

        # Lazy-loaded heavy components (created on first use)
        self._settings_dialog = None
        self._heavy_dialogs = {}

    def _create_right_chart(self):
        """Create the right side chart with no axes, Y-axis matches main chart, X-axis 0-1."""
        right_chart = PlotWidget()

        # Basic configuration
        right_chart.setBackground("#1e1e1e")

        # Hide both axes completely
        right_chart.setLabel("left", "")
        right_chart.setLabel("bottom", "")

        # Configure axes to be completely invisible
        left_axis = right_chart.getAxis("left")
        left_axis.setStyle(showValues=False)
        left_axis.setPen(color="transparent", width=0)
        left_axis.setTextPen("transparent")
        left_axis.hide()  # Completely hide the left axis

        bottom_axis = right_chart.getAxis("bottom")
        bottom_axis.setStyle(showValues=False)
        bottom_axis.setPen(color="transparent", width=0)
        bottom_axis.setTextPen("transparent")
        bottom_axis.hide()  # Completely hide the bottom axis

        # Disable grid
        right_chart.showGrid(x=False, y=False)

        # Disable mouse interaction
        right_chart.setMouseEnabled(x=False, y=False)
        right_chart.enableAutoRange("xy", False)

        # Remove all buttons and menus
        right_chart.setMenuEnabled(False)
        plot_item = right_chart.getPlotItem()
        plot_item.setMenuEnabled(False)
        plot_item.hideButtons()
        view_box = plot_item.getViewBox()
        view_box.setMenuEnabled(False)

        # Remove any borders or frames
        plot_item.hideAxis("left")
        plot_item.hideAxis("bottom")
        plot_item.hideAxis("top")
        plot_item.hideAxis("right")

        # Set fixed X-axis range from 0 to 1
        right_chart.setXRange(0, 1, padding=0)

        # Store reference for Y-axis synchronization
        self.right_chart_widget = right_chart

        return right_chart

    @pyqtSlot(str, dict, float)
    def _on_computation_completed(
        self, task_id: str, result: Dict[str, Any], execution_time: float
    ):
        """Handle completed computation from worker thread."""
        try:
            self.logger.debug(
                f"Computation completed: {task_id} in {execution_time:.2f}ms"
            )

            # Process result based on task type
            if "bounds" in result:
                # Update chart bounds
                self.chart_model.bounds_changed.emit(result)

            if "render_data" in result:
                # Update chart elements
                element_id = result.get("element_id", task_id)
                element_type = ChartElementType(
                    result.get("element_type", "candlestick")
                )

                element = ChartElement(
                    element_id=element_id,
                    element_type=element_type,
                    render_data=result["render_data"],
                    bounds=result.get("bounds", {}),
                    visible=result.get("visible", True),
                    z_order=result.get("z_order", 0),
                )

                # Add or update element in model
                if self.chart_model.get_element(element_id):
                    self.chart_model.update_element(element_id, result["render_data"])
                else:
                    self.chart_model.add_element(element)

        except Exception as e:
            self.logger.error(f"Error processing computation result {task_id}: {e}")

    @pyqtSlot(str, str)
    def _on_computation_failed(self, task_id: str, error_message: str):
        """Handle failed computation from worker thread."""
        self.logger.error(f"Computation failed: {task_id} - {error_message}")
        # Could emit error signal or show user notification

    def _align_chart_axes(self):
        """Align the charts to create a unified Y-axis appearance."""
        try:
            # Get the plot items
            main_plot_item = self.chart_widget.getPlotItem()
            bandwidth_plot_item = self.bandwidth_chart.getPlotItem()

            # Get the axes
            main_left_axis = main_plot_item.getAxis("left")
            bandwidth_left_axis = bandwidth_plot_item.getAxis("left")

            # Force both Y-axes to have identical width for perfect alignment
            # The bandwidth chart Y-axis will be invisible but take up the same space
            bandwidth_left_axis.setWidth(main_left_axis.width())

            # Ensure both view boxes have identical margins
            main_view_box = main_plot_item.getViewBox()
            bandwidth_view_box = bandwidth_plot_item.getViewBox()
            main_view_box.setContentsMargins(0, 0, 0, 0)
            bandwidth_view_box.setContentsMargins(0, 0, 0, 0)

            # Connect main chart's geometry changes to update bandwidth chart alignment
            main_left_axis.geometryChanged.connect(self._on_main_axis_geometry_changed)

            # Synchronize right chart's Y-axis with main chart
            if hasattr(self, "right_chart_widget"):
                self._sync_right_chart_y_axis()

        except Exception as e:
            print(f"Warning: Could not align chart axes: {e}")
            # Fallback - just ensure view boxes are aligned
            try:
                main_view_box = self.chart_widget.getPlotItem().getViewBox()
                bandwidth_view_box = self.bandwidth_chart.getPlotItem().getViewBox()
                main_view_box.setContentsMargins(0, 0, 0, 0)
                bandwidth_view_box.setContentsMargins(0, 0, 0, 0)
            except:
                pass

    def _on_main_axis_geometry_changed(self):
        """Handle main chart Y-axis geometry changes to maintain alignment."""
        try:
            # Get the axes
            main_left_axis = self.chart_widget.getPlotItem().getAxis("left")
            bandwidth_left_axis = self.bandwidth_chart.getPlotItem().getAxis("left")

            # Update bandwidth chart's Y-axis width to match main chart
            main_axis_width = main_left_axis.width()
            bandwidth_left_axis.setWidth(main_axis_width)

            # Force update
            self.bandwidth_chart.getPlotItem().getViewBox().updateViewRange()

        except Exception as e:
            print(f"Warning: Could not update axis alignment: {e}")

    def _sync_right_chart_y_axis(self):
        """Synchronize the right chart's Y-axis range with the main chart."""
        try:
            # Get main chart's Y range
            main_view_box = self.chart_widget.getPlotItem().getViewBox()
            main_range = main_view_box.viewRange()
            main_y_range = main_range[1]  # [y_min, y_max]

            # Apply the same Y range to the right chart
            right_view_box = self.right_chart_widget.getPlotItem().getViewBox()
            right_view_box.setYRange(main_y_range[0], main_y_range[1], padding=0)

            # Connect main chart's range changes to update right chart
            main_view_box.sigRangeChanged.connect(self._on_main_chart_y_range_changed)

        except Exception as e:
            print(f"Warning: Could not sync right chart Y-axis: {e}")

    def _on_main_chart_y_range_changed(self):
        """Handle main chart Y-axis range changes to update right chart."""
        try:
            if hasattr(self, "right_chart_widget"):
                # Get main chart's current Y range
                main_view_box = self.chart_widget.getPlotItem().getViewBox()
                main_range = main_view_box.viewRange()
                main_y_range = main_range[1]  # [y_min, y_max]

                # Apply the same Y range to the right chart, keep X range 0-1
                right_view_box = self.right_chart_widget.getPlotItem().getViewBox()
                right_view_box.setYRange(main_y_range[0], main_y_range[1], padding=0)

        except Exception as e:
            print(f"Warning: Could not update right chart Y-axis: {e}")

    def _add_peak_trough_lines(self):
        """Add lines from right chart (0-1) to peaks and troughs using existing peak/trough data."""
        if not hasattr(self, "right_chart_widget") or not self.current_data:
            return

        try:
            # Clear existing peak/trough lines
            if hasattr(self, "peak_trough_lines"):
                for line in self.peak_trough_lines:
                    self.right_chart_widget.removeItem(line)
            self.peak_trough_lines = []

            # Get existing peaks and troughs data from the system
            peaks = self.current_data.get("peaks", [])
            troughs = self.current_data.get("troughs", [])

            # Draw lines for each peak (green)
            for peak in peaks:
                if hasattr(peak, "level"):
                    y_value = peak.level  # Use the actual peak level
                    line_item = self.right_chart_widget.plot(
                        [0, 1],
                        [y_value, y_value],
                        pen=pg.mkPen(color="#00FF00", width=1),  # Green for peaks
                        name="Peak Line",
                    )
                    self.peak_trough_lines.append(line_item)

            # Draw lines for each trough (red)
            for trough in troughs:
                if hasattr(trough, "level"):
                    y_value = trough.level  # Use the actual trough level
                    line_item = self.right_chart_widget.plot(
                        [0, 1],
                        [y_value, y_value],
                        pen=pg.mkPen(color="#FF0000", width=1),  # Red for troughs
                        name="Trough Line",
                    )
                    self.peak_trough_lines.append(line_item)

        except Exception as e:
            print(f"Warning: Could not add peak/trough lines: {e}")

    def _add_zero_percent_label(self):
        """Add a 0% label showing the real OHLC price as a custom Y-axis tick."""
        if not self.current_data:
            return

        try:
            # Use pre-computed zero percent price from backend (eliminates all UI computation)
            if "zero_percent_price" in self.current_data:
                self.zero_percent_price = self.current_data["zero_percent_price"]

                # Create a custom axis item that adds the 0% price label
                self._setup_custom_y_axis_with_price_label()
            else:
                # No fallback - UI should never compute
                return

        except Exception as e:
            print(f"Warning: Could not add 0% label to Y-axis: {e}")

    def _setup_custom_y_axis_with_price_label(self):
        """Setup a custom Y-axis that shows the 0% price label while preserving dynamic ticks."""
        try:
            # Create a custom axis item class
            class CustomYAxis(pg.AxisItem):
                def __init__(self, parent_widget, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    self.parent_widget = parent_widget

                def tickStrings(self, values, scale, spacing):
                    """Override tick strings to add 0% price label."""
                    # Get the default tick strings
                    strings = super().tickStrings(values, scale, spacing)

                    # Find if 0 is in the values and replace its label
                    if hasattr(self.parent_widget, "zero_percent_price"):
                        for i, value in enumerate(values):
                            if abs(value) < 0.01:  # Close to 0 (within 0.01%)
                                strings[i] = (
                                    f"${self.parent_widget.zero_percent_price:.2f}"
                                )
                                break

                    return strings

            # Replace the left axis with our custom axis
            plot_item = self.chart_widget.getPlotItem()
            custom_axis = CustomYAxis(self, orientation="left")

            # Copy styling from the original axis
            original_axis = plot_item.getAxis("left")
            custom_axis.setPen(original_axis.pen())
            custom_axis.setTextPen(original_axis.textPen())
            custom_axis.setStyle(showValues=True)

            # Set font size to match bandwidth chart (8px)
            font = QFont("Segoe UI", 8)
            custom_axis.setTickFont(font)

            # Replace the axis
            plot_item.setAxisItems({"left": custom_axis})

        except Exception as e:
            print(f"Warning: Could not setup custom Y-axis: {e}")

    def setup_chart(self):
        """Setup a completely new chart widget."""
        # Create fresh PlotWidget
        self.chart_widget = PlotWidget()

        # Basic configuration
        self.chart_widget.setBackground("#1e1e1e")
        # Remove axis titles for cleaner appearance
        self.chart_widget.setLabel("left", "")
        self.chart_widget.setLabel("bottom", "")

        # Enable mouse interaction
        self.chart_widget.setMouseEnabled(x=True, y=True)
        self.chart_widget.enableAutoRange("xy", True)

        # Disable grid
        self.chart_widget.showGrid(x=False, y=False)

        # Configure axes - Y-axis visible, bottom axis hidden for seamless connection
        self.chart_widget.getAxis("left").setPen(color="#cccccc", width=1)
        self.chart_widget.getAxis("bottom").setPen(
            color="transparent", width=0
        )  # Hide bottom border
        self.chart_widget.getAxis("left").setTextPen("#cccccc")
        self.chart_widget.getAxis("bottom").setTextPen("transparent")

        # Hide X-axis values and ticks for main chart (bandwidth chart will show X-axis)
        self.chart_widget.getAxis("bottom").setStyle(showValues=False)
        self.chart_widget.getAxis("bottom").setTicks([])

        # Configure Y-axis to extend properly
        left_axis = self.chart_widget.getAxis("left")
        left_axis.setStyle(showValues=True)  # Keep Y-axis values
        left_axis.setZValue(100)  # Bring to front

        # Remove anchor (A) button and context menu from main chart
        self.chart_widget.setMenuEnabled(False)
        plot_item = self.chart_widget.getPlotItem()
        plot_item.setMenuEnabled(False)
        plot_item.hideButtons()  # Hide all buttons including anchor
        view_box = plot_item.getViewBox()
        view_box.setMenuEnabled(False)

        # Also disable the auto-range button specifically
        plot_item.autoBtn.hide()
        plot_item.vb.setMenuEnabled(False)

        # Set smaller font for axis text
        font = QFont("Segoe UI", 8)
        self.chart_widget.getAxis("left").setTickFont(font)
        self.chart_widget.getAxis("bottom").setTickFont(font)

        # Disable right-click context menu to prevent bugs
        self.chart_widget.setMenuEnabled(False)

        # Get the plot item and disable its context menu too
        plot_item = self.chart_widget.getPlotItem()
        plot_item.setMenuEnabled(False)

        # Disable context menu on view box
        view_box = plot_item.getViewBox()
        view_box.setMenuEnabled(False)

        # Connect view range changes to update label positions
        view_box.sigRangeChanged.connect(self._update_stdev_label_positions)

        # Connect mouse events for drawing tools
        self.chart_widget.scene().sigMouseClicked.connect(self._on_mouse_clicked)
        self.chart_widget.scene().sigMouseMoved.connect(self._on_mouse_moved)

    def _setup_crosshair(self):
        """Setup crosshair that extends across main chart and bandwidth chart."""
        try:
            # Initialize crosshair manager with main chart, bandwidth chart, and parent reference
            if hasattr(self, "bandwidth_chart"):
                self.crosshair_manager = CrosshairManager(
                    self.chart_widget, self.bandwidth_chart, self
                )
            else:
                self.crosshair_manager = CrosshairManager(self.chart_widget, None, self)
        except Exception as e:
            print(f"Warning: Could not setup crosshair: {e}")

    def _reinitialize_crosshair(self):
        """Re-initialize crosshair after chart update (since chart.clear() removes all items)."""
        try:
            if hasattr(self, "crosshair_manager") and self.crosshair_manager:
                # Re-add crosshair items to main chart (only if not already added)
                if (
                    self.crosshair_manager.v_line_main
                    not in self.chart_widget.listDataItems()
                ):
                    self.chart_widget.addItem(
                        self.crosshair_manager.v_line_main, ignoreBounds=True
                    )
                if (
                    self.crosshair_manager.h_line_main
                    not in self.chart_widget.listDataItems()
                ):
                    self.chart_widget.addItem(
                        self.crosshair_manager.h_line_main, ignoreBounds=True
                    )

                # Reset cross chart tracking since items were removed
                self.crosshair_manager.cross_chart = None

                # Hide crosshair initially (will show when mouse moves over chart)
                self.crosshair_manager.hide_crosshair()
        except Exception as e:
            print(f"Warning: Could not reinitialize crosshair: {e}")

    def _setup_right_box(self):
        """Setup the right box with information, drawing tools, and settings."""
        # Create a box widget to the right of the chart
        self.right_box = QWidget()
        # Remove fixed width - let it fill the available space proportionally
        self.right_box.setStyleSheet(
            "background-color: #1e1e1e; border: 1px solid #3e3e3e;"
        )

        # Create a static layout for the right box sections
        self.right_box_content_widget = QWidget()
        self.right_box_content_layout = QVBoxLayout(self.right_box_content_widget)
        self.right_box_content_layout.setSpacing(5)  # Add spacing between sections

        # Create a layout for the right box
        right_box_layout = QVBoxLayout(self.right_box)
        right_box_layout.setContentsMargins(5, 5, 5, 5)
        right_box_layout.addWidget(self.right_box_content_widget)

        # Create Information section
        self._create_info_section()

        # Create Drawing Tools section
        self._create_drawing_tools_section()

        # Create Settings section
        self._create_settings_section()

        # Set fixed heights for each section to maintain consistent layout
        self.info_scroll.setMinimumHeight(300)
        self.drawing_tools_scroll.setMinimumHeight(200)
        self.settings_scroll.setMinimumHeight(150)

        # Add stretch to push sections to the top
        self.right_box_content_layout.addStretch()

        # Add stretch at the bottom to push all content to the top
        right_box_layout.addStretch()

        # Add the right box to the main horizontal layout with 1/4 proportion
        self.main_horizontal_layout.addWidget(
            self.right_box, 1
        )  # Right box gets 1/4 of the space (1 out of 4 total)

    def _create_info_section(self):
        """Create the information section of the right box."""
        # Create Information section with scroll area
        self.info_scroll = QScrollArea()
        self.info_scroll.setWidgetResizable(True)
        self.info_scroll.setHorizontalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAlwaysOff
        )
        self.info_scroll.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.info_scroll.setStyleSheet(
            """
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """
        )

        self.info_section = QWidget()
        self.info_section.setStyleSheet("background-color: #1e1e1e;")
        info_section_layout = QVBoxLayout(self.info_section)
        info_section_layout.setContentsMargins(5, 5, 5, 5)
        info_section_layout.setSpacing(2)

        # Add "Information" title
        info_title = QLabel("Information")
        info_title.setStyleSheet(
            "color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; padding-bottom: 5px; border: none;"
        )
        info_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_section_layout.addWidget(info_title)

        # Create labels for candle information in the requested order
        self.idx_label = QLabel("IDX: --")
        self.date_label = QLabel("Date: --")
        self.category_label = QLabel("Category: --")
        self.open_label = QLabel("Open: --")
        self.high_label = QLabel("High: --")
        self.low_label = QLabel("Low: --")
        self.close_label = QLabel("Close: --")
        self.volume_label = QLabel("Volume: --")
        self.crosshair_price_label = QLabel("Crosshair: --")

        # Set text color to white for all labels with 11px font size in the requested order
        label_style = "color: white; font-family: 'Consolas', 'Courier New', monospace; font-size: 11px;"
        for label in [
            self.idx_label,
            self.date_label,
            self.category_label,
            self.open_label,
            self.high_label,
            self.low_label,
            self.close_label,
            self.volume_label,
        ]:
            label.setStyleSheet(label_style)
            info_section_layout.addWidget(label)

        # Add the crosshair price label with bold styling and 11px font size
        self.crosshair_price_label.setStyleSheet(
            "color: #FFFFFF; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px;"
        )
        info_section_layout.addWidget(self.crosshair_price_label)

        # Set the info section as the scroll area's widget
        self.info_scroll.setWidget(self.info_section)

        # Add the scroll area to the content layout
        self.right_box_content_layout.addWidget(self.info_scroll)

    def _create_drawing_tools_section(self):
        """Create the drawing tools section of the right box."""
        # Create Drawing Tools section with scroll area
        self.drawing_tools_scroll = QScrollArea()
        self.drawing_tools_scroll.setWidgetResizable(True)
        self.drawing_tools_scroll.setHorizontalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAlwaysOff
        )
        self.drawing_tools_scroll.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.drawing_tools_scroll.setStyleSheet(
            """
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """
        )

        self.drawing_tools_section = QWidget()
        self.drawing_tools_section.setStyleSheet("background-color: #1e1e1e;")
        drawing_tools_layout = QVBoxLayout(self.drawing_tools_section)
        drawing_tools_layout.setContentsMargins(5, 5, 5, 5)
        drawing_tools_layout.setSpacing(2)

        # Add "Drawing Tools" title
        drawing_tools_title = QLabel("Drawing Tools")
        drawing_tools_title.setStyleSheet(
            "color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; padding-top: 5px; padding-bottom: 5px; border: none;"
        )
        drawing_tools_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        drawing_tools_layout.addWidget(drawing_tools_title)

        # Create drawing tool buttons with 11px font size
        button_style = """
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """

        # Create drawing tool buttons
        self.mouse_button = QPushButton("Mouse")
        self.mouse_button.setStyleSheet(button_style)
        self.mouse_button.clicked.connect(lambda: self.set_drawing_tool(None))
        drawing_tools_layout.addWidget(self.mouse_button)

        self.line_button = QPushButton("Line")
        self.line_button.setStyleSheet(button_style)
        self.line_button.clicked.connect(lambda: self.set_drawing_tool("line"))
        drawing_tools_layout.addWidget(self.line_button)

        self.vertical_line_button = QPushButton("Vertical Line")
        self.vertical_line_button.setStyleSheet(button_style)
        self.vertical_line_button.clicked.connect(
            lambda: self.set_drawing_tool("vertical")
        )
        drawing_tools_layout.addWidget(self.vertical_line_button)

        self.horizontal_line_button = QPushButton("Horizontal Line")
        self.horizontal_line_button.setStyleSheet(button_style)
        self.horizontal_line_button.clicked.connect(
            lambda: self.set_drawing_tool("horizontal")
        )
        drawing_tools_layout.addWidget(self.horizontal_line_button)

        self.rectangle_button = QPushButton("Rectangle")
        self.rectangle_button.setStyleSheet(button_style)
        self.rectangle_button.clicked.connect(
            lambda: self.set_drawing_tool("rectangle")
        )
        drawing_tools_layout.addWidget(self.rectangle_button)

        self.text_button = QPushButton("Text")
        self.text_button.setStyleSheet(button_style)
        self.text_button.clicked.connect(lambda: self.set_drawing_tool("text"))
        drawing_tools_layout.addWidget(self.text_button)

        self.eraser_button = QPushButton("Eraser")
        self.eraser_button.setStyleSheet(button_style)
        self.eraser_button.clicked.connect(lambda: self.set_drawing_tool("eraser"))
        drawing_tools_layout.addWidget(self.eraser_button)

        self.undo_button = QPushButton("Undo")
        self.undo_button.setStyleSheet(button_style)
        self.undo_button.clicked.connect(self.undo_drawing)
        drawing_tools_layout.addWidget(self.undo_button)

        self.redo_button = QPushButton("Redo")
        self.redo_button.setStyleSheet(button_style)
        self.redo_button.clicked.connect(self.redo_drawing)
        drawing_tools_layout.addWidget(self.redo_button)

        self.clear_all_button = QPushButton("Clear All")
        self.clear_all_button.setStyleSheet(button_style)
        self.clear_all_button.clicked.connect(self.clear_all_drawings)
        drawing_tools_layout.addWidget(self.clear_all_button)

        # Set the drawing tools section as the scroll area's widget
        self.drawing_tools_scroll.setWidget(self.drawing_tools_section)

        # Add the drawing tools scroll area to the content layout
        self.right_box_content_layout.addWidget(self.drawing_tools_scroll)

    def _create_settings_section(self):
        """Create the settings section of the right box."""
        # Create Settings section with scroll area
        self.settings_scroll = QScrollArea()
        self.settings_scroll.setWidgetResizable(True)
        self.settings_scroll.setHorizontalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAlwaysOff
        )
        self.settings_scroll.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.settings_scroll.setStyleSheet(
            """
            QScrollArea {
                background-color: #1e1e1e;
                border: none;
            }
            QScrollBar:vertical {
                background-color: #2e2e2e;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #777777;
            }
        """
        )

        self.settings_section = QWidget()
        self.settings_section.setStyleSheet("background-color: #1e1e1e;")
        settings_layout = QVBoxLayout(self.settings_section)
        settings_layout.setContentsMargins(5, 5, 5, 5)
        settings_layout.setSpacing(2)

        # Add "Settings" title
        settings_title = QLabel("Settings")
        settings_title.setStyleSheet(
            "color: white; font-family: 'Consolas', 'Courier New', monospace; font-weight: bold; font-size: 11px; padding-top: 5px; padding-bottom: 5px; border: none;"
        )
        settings_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(settings_title)

        # Add "Chart Settings" subtitle
        chart_settings_subtitle = QLabel("Chart Settings")
        chart_settings_subtitle.setStyleSheet(
            "color: white; font-family: 'Consolas', 'Courier New', monospace; font-style: italic; font-size: 11px; padding-bottom: 5px; border: none;"
        )
        chart_settings_subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        settings_layout.addWidget(chart_settings_subtitle)

        # Create settings button with theme-matching colors and 11px font size
        self.settings_button_panel = QPushButton("Settings")
        self.settings_button_panel.setStyleSheet(
            """
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """
        )
        # Connect to settings dialog
        self.settings_button_panel.clicked.connect(self._open_settings_dialog)
        settings_layout.addWidget(self.settings_button_panel)

        # Add stretch at the bottom to push all content to the top
        settings_layout.addStretch()

        # Set the settings section as the scroll area's widget
        self.settings_scroll.setWidget(self.settings_section)

        # Add the settings scroll area to the content layout
        self.right_box_content_layout.addWidget(self.settings_scroll)

    def set_drawing_tool(self, tool_type):
        """Set the current drawing tool."""
        self.current_drawing_tool = tool_type
        # Update button styles to show active tool
        button_style_active = """
            QPushButton {
                background-color: #4d4d4d;
                color: white;
                border: 1px solid #777777;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
            }
        """
        button_style_normal = """
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 5px;
                text-align: left;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }
        """

        # Reset all buttons to normal style
        for button in [
            self.mouse_button,
            self.line_button,
            self.vertical_line_button,
            self.horizontal_line_button,
            self.rectangle_button,
            self.text_button,
            self.eraser_button,
        ]:
            button.setStyleSheet(button_style_normal)

        # Highlight active tool
        if tool_type == "line":
            self.line_button.setStyleSheet(button_style_active)
        elif tool_type == "vertical":
            self.vertical_line_button.setStyleSheet(button_style_active)
        elif tool_type == "horizontal":
            self.horizontal_line_button.setStyleSheet(button_style_active)
        elif tool_type == "rectangle":
            self.rectangle_button.setStyleSheet(button_style_active)
        elif tool_type == "text":
            self.text_button.setStyleSheet(button_style_active)
        elif tool_type == "eraser":
            self.eraser_button.setStyleSheet(button_style_active)
        else:
            self.mouse_button.setStyleSheet(button_style_active)

    def undo_drawing(self):
        """Undo the last drawing operation."""
        if hasattr(self, "drawing_tools"):
            self.drawing_tools.undo()

    def redo_drawing(self):
        """Redo the last undone drawing operation."""
        if hasattr(self, "drawing_tools"):
            self.drawing_tools.redo()

    def clear_all_drawings(self):
        """Clear all drawing items."""
        if hasattr(self, "drawing_tools"):
            self.drawing_tools.clear_all()
        self.set_drawing_tool(None)

    def _open_settings_dialog(self):
        """Open the settings dialog with lazy loading."""
        try:
            # Lazy load settings dialog on first use
            if self._settings_dialog is None:
                self.logger.debug("Creating settings dialog on first use")
                self._settings_dialog = ChartSettingsDialog(self)

            self._settings_dialog.exec()
        except Exception as e:
            self.logger.error(f"Error opening settings dialog: {e}")

    def _apply_visibility_settings(self, settings):
        """Apply visibility settings to chart elements."""
        try:
            # Update stored settings AND persistent settings
            self.chart_settings.update(settings)
            self.persistent_settings.update(settings)
            print(f"Applying visibility settings: {settings}")  # Debug output
            # Handle StDev levels visibility
            if hasattr(self, "stdev_infinity_lines"):
                for line in self.stdev_infinity_lines:
                    if settings["show_stdev"]:
                        line.show()
                    else:
                        line.hide()

            # Handle candles visibility
            if hasattr(self, "candlestick_item") and self.candlestick_item:
                if settings["show_candles"]:
                    # Show candles - only add if not already added
                    if self.candlestick_item not in self.chart_widget.listDataItems():
                        self.chart_widget.addItem(self.candlestick_item)
                    self.candlestick_item.show()
                    self.candlestick_item.setVisible(True)
                    print("Candles shown")
                else:
                    # Hide candles completely
                    try:
                        self.chart_widget.removeItem(self.candlestick_item)
                        print("Candles hidden (removed from chart)")
                    except:
                        pass
                    self.candlestick_item.hide()
                    self.candlestick_item.setVisible(False)

            # Handle Donchian midpoint visibility (the rebased vector line on main chart)
            if hasattr(self, "rebased_vector_line") and self.rebased_vector_line:
                if settings["show_donchian"]:
                    self.rebased_vector_line.show()
                    self.rebased_vector_line.setVisible(True)
                    print("Donchian midpoint shown")
                else:
                    self.rebased_vector_line.hide()
                    self.rebased_vector_line.setVisible(False)
                    print("Donchian midpoint hidden")
            else:
                print("No Donchian midpoint line found to toggle")

            # Handle second chart visibility and main chart expansion
            if hasattr(self, "bandwidth_chart"):
                if settings["show_second_chart"]:
                    self.bandwidth_chart.show()
                    # Restore normal main chart size
                    if hasattr(self, "chart_widget"):
                        self.chart_widget.setMaximumHeight(
                            16777215
                        )  # Remove height restriction
                else:
                    self.bandwidth_chart.hide()
                    # Expand main chart to take the space of the second chart
                    if hasattr(self, "chart_widget"):
                        # The main chart will automatically expand to fill available space
                        pass

            # Handle right chart visibility
            if hasattr(self, "right_chart"):
                if settings["show_right_chart"]:
                    self.right_chart.show()
                else:
                    self.right_chart.hide()

            # Handle mean horizontal lines visibility (mean peak and trough lines)
            if hasattr(self, "mean_peak_trough_lines") and self.mean_peak_trough_lines:
                for line in self.mean_peak_trough_lines:
                    if settings["show_mean_horizontal"]:
                        line.show()
                    else:
                        line.hide()

            # Handle mean line from previous trough/peak visibility (predictive cycle lines)
            if hasattr(self, "predictive_cycle_lines") and self.predictive_cycle_lines:
                for line in self.predictive_cycle_lines:
                    if settings["show_mean_line"]:
                        line.show()
                    else:
                        line.hide()

            # Handle peaks and troughs visibility
            if (
                hasattr(self, "peak_trough_annotations")
                and self.peak_trough_annotations
            ):
                try:
                    if settings["show_peaks_troughs"]:
                        # Show all annotation items
                        for item in self.peak_trough_annotations.annotation_items:
                            item.show()
                            item.setVisible(True)
                    else:
                        # Hide all annotation items
                        for item in self.peak_trough_annotations.annotation_items:
                            item.hide()
                            item.setVisible(False)
                except Exception as e:
                    print(f"Error toggling peaks/troughs visibility: {e}")

            print(f"Applied visibility settings: {settings}")
        except Exception as e:
            print(f"Error applying visibility settings: {e}")

    def _apply_color_settings(self, colors):
        """Apply color settings to chart elements."""
        try:
            # Store colors for use in chart updates AND persistent storage
            if not hasattr(self, "chart_colors"):
                self.chart_colors = {}
            if not hasattr(self, "persistent_colors"):
                self.persistent_colors = {}
            self.chart_colors.update(colors)
            self.persistent_colors.update(colors)

            # Apply colors to existing elements
            for color_name, color_value in colors.items():
                if (
                    color_name == "Donchian midpoint color"
                    and hasattr(self, "rebased_vector_line")
                    and self.rebased_vector_line
                ):
                    # Update Donchian midpoint line color (rebased vector line)
                    self.rebased_vector_line.setPen(pg.mkPen(color_value, width=2))
                    print(f"Updated Donchian midpoint color to: {color_value}")

                elif color_name == "Positive StDev lines color" and hasattr(
                    self, "stdev_infinity_lines"
                ):
                    # Update positive StDev lines - only update InfiniteLine objects, skip TextItems
                    updated_count = 0
                    for line in self.stdev_infinity_lines:
                        try:
                            # Check if it's an InfiniteLine object (not TextItem)
                            if (
                                hasattr(line, "setPen")
                                and hasattr(line, "pos")
                                and hasattr(line, "angle")
                            ):
                                pos_value = line.pos()
                                # Handle different position types
                                if hasattr(pos_value, "y"):
                                    pos_y = pos_value.y()
                                else:
                                    pos_y = float(pos_value)

                                if pos_y > 0:
                                    line.setPen(
                                        pg.mkPen(
                                            color_value,
                                            width=1,
                                            style=pg.QtCore.Qt.PenStyle.DashLine,
                                        )
                                    )
                                    updated_count += 1
                        except Exception:
                            pass
                    print(
                        f"Updated {updated_count} positive StDev lines to color {color_value}"
                    )

                elif color_name == "Negative StDev lines color" and hasattr(
                    self, "stdev_infinity_lines"
                ):
                    # Update negative StDev lines - only update InfiniteLine objects, skip TextItems
                    updated_count = 0
                    for line in self.stdev_infinity_lines:
                        try:
                            # Check if it's an InfiniteLine object (not TextItem)
                            if (
                                hasattr(line, "setPen")
                                and hasattr(line, "pos")
                                and hasattr(line, "angle")
                            ):
                                pos_value = line.pos()
                                # Handle different position types
                                if hasattr(pos_value, "y"):
                                    pos_y = pos_value.y()
                                else:
                                    pos_y = float(pos_value)

                                if pos_y < 0:
                                    line.setPen(
                                        pg.mkPen(
                                            color_value,
                                            width=1,
                                            style=pg.QtCore.Qt.PenStyle.DashLine,
                                        )
                                    )
                                    updated_count += 1
                        except Exception:
                            pass
                    print(
                        f"Updated {updated_count} negative StDev lines to color {color_value}"
                    )

                elif color_name == "Mean horizontal lines color" and hasattr(
                    self, "mean_peak_trough_lines"
                ):
                    # Update mean horizontal lines (mean peak and trough lines)
                    for line in self.mean_peak_trough_lines:
                        line.setPen(
                            pg.mkPen(
                                color_value,
                                width=1,
                                style=pg.QtCore.Qt.PenStyle.DashLine,
                            )
                        )

                elif color_name == "Mean Wave Color" and hasattr(
                    self, "predictive_cycle_lines"
                ):
                    # Update ONLY predictive cycle lines (mean wave from previous peak/trough)
                    for line in self.predictive_cycle_lines:
                        line.setPen(pg.mkPen(color_value, width=2))

                elif color_name == "Peaks color":
                    # Update peaks color and refresh annotations
                    if (
                        hasattr(self, "peak_trough_annotations")
                        and self.peak_trough_annotations
                    ):
                        self.peak_trough_annotations.update_peaks_color(color_value)
                    # Trigger annotation refresh to apply new color
                    self._refresh_peak_trough_annotations()

                elif color_name == "Troughs color":
                    # Update troughs color and refresh annotations
                    if (
                        hasattr(self, "peak_trough_annotations")
                        and self.peak_trough_annotations
                    ):
                        self.peak_trough_annotations.update_troughs_color(color_value)
                    # Trigger annotation refresh to apply new color
                    self._refresh_peak_trough_annotations()

            # For candle colors and peaks/troughs, we need to refresh the chart
            candle_colors_changed = any(
                name in colors
                for name in ["Bullish candle color", "Bearish candle color"]
            )
            peak_trough_colors_changed = any(
                name in colors for name in ["Peaks color", "Troughs color"]
            )

            if candle_colors_changed or peak_trough_colors_changed:
                if hasattr(self, "current_data") and self.current_data:
                    self.update_chart()

            print(f"Applied color settings: {colors}")
        except Exception as e:
            print(f"Error applying color settings: {e}")

    def _update_crosshair_info(self, x_pos, y_pos):
        """Update information panel based on crosshair position."""
        try:
            # Check if we have data to work with
            if not hasattr(self, "current_data") or not self.current_data:
                self._clear_crosshair_info()
                return

            # Check if crosshair lookup data is available
            if "crosshair_info_lookup" not in self.current_data:
                self._clear_crosshair_info()
                return

            # Get data arrays for display (no computation)
            actual_dates = self.current_data.get("actual_dates", [])
            opens = self.current_data.get("open", [])
            highs = self.current_data.get("high", [])
            lows = self.current_data.get("low", [])
            closes = self.current_data.get("close", [])
            volumes = self.current_data.get("volume", [])

            # Use pre-computed crosshair info from backend (eliminates all UI computation)
            if "crosshair_info_lookup" in self.current_data:
                crosshair_lookup = self.current_data["crosshair_info_lookup"]

                # Get pre-computed info for this position
                candle_index = int(round(x_pos))  # Only rounding, no other math
                if str(candle_index) in crosshair_lookup:
                    info = crosshair_lookup[str(candle_index)]

                    # Update labels with pre-computed data
                    self.idx_label.setText(f"IDX: {info['candle_index']}")

                    # Apply candle skip logic for category (same as DataTab)
                    length_setting = self.current_data.get("length", 20)  # Default to 20 if not specified
                    skip_count = length_setting + 1  # Skip first N+1 candles (0 to N inclusive)

                    if info['candle_index'] < skip_count:
                        # Empty category for skipped candles
                        self.category_label.setText(f"Category: --")
                    else:
                        # Show actual category for non-skipped candles
                        self.category_label.setText(f"Category: {info['category']}")

                    actual_data_index = info["actual_data_index"]
                else:
                    self._clear_crosshair_info()
                    return
            else:
                # No fallback - UI should never compute
                self._clear_crosshair_info()
                return

            # Format date - use actual dates from the data
            try:
                # Check if actual_dates exists and has data (handle DatetimeIndex properly)
                has_dates = False
                if actual_dates is not None:
                    if hasattr(actual_dates, '__len__'):
                        has_dates = len(actual_dates) > 0
                    else:
                        has_dates = True

                if has_dates and actual_data_index < len(actual_dates):
                    date_value = actual_dates[actual_data_index]
                    if hasattr(date_value, "strftime"):
                        # pandas Timestamp object
                        date_str = date_value.strftime("%Y-%m-%d")
                    elif hasattr(date_value, "date"):
                        # datetime object
                        date_str = date_value.date().strftime("%Y-%m-%d")
                    else:
                        # Try to convert to string and parse
                        import pandas as pd

                        try:
                            parsed_date = pd.to_datetime(date_value)
                            date_str = parsed_date.strftime("%Y-%m-%d")
                        except:
                            date_str = str(date_value)[
                                :10
                            ]  # Take first 10 chars if it looks like a date
                    self.date_label.setText(f"Date: {date_str}")
                else:
                    self.date_label.setText(f"Date: --")
            except Exception as e:
                print(f"Error formatting date: {e}")
                self.date_label.setText(f"Date: --")

            # Update OHLCV data from original data using actual data index (not rebased percentages)
            if actual_data_index < len(opens):
                self.open_label.setText(f"Open: {opens[actual_data_index]:.4f}")
            else:
                self.open_label.setText(f"Open: --")

            if actual_data_index < len(highs):
                self.high_label.setText(f"High: {highs[actual_data_index]:.4f}")
            else:
                self.high_label.setText(f"High: --")

            if actual_data_index < len(lows):
                self.low_label.setText(f"Low: {lows[actual_data_index]:.4f}")
            else:
                self.low_label.setText(f"Low: --")

            if actual_data_index < len(closes):
                self.close_label.setText(f"Close: {closes[actual_data_index]:.4f}")
            else:
                self.close_label.setText(f"Close: --")

            if actual_data_index < len(volumes):
                volume_formatted = (
                    f"{int(volumes[actual_data_index]):,}"
                    if volumes[actual_data_index] > 0
                    else "0"
                )
                self.volume_label.setText(f"Volume: {volume_formatted}")
            else:
                self.volume_label.setText(f"Volume: --")

            # Update crosshair price with both percentage and actual price
            try:
                # Get zero percent price from current data
                zero_percent_price = self._get_zero_percent_price()
                if zero_percent_price > 0:
                    # Calculate actual price from percentage: zero_percent_price * (1 + percentage/100)
                    actual_price = zero_percent_price * (1 + y_pos / 100)
                    self.crosshair_price_label.setText(
                        f"Crosshair: {y_pos:.2f}% (${actual_price:.2f})"
                    )
                else:
                    self.crosshair_price_label.setText(f"Crosshair: {y_pos:.2f}%")
            except Exception as e:
                print(f"Error calculating crosshair price: {e}")
                self.crosshair_price_label.setText(f"Crosshair: {y_pos:.2f}%")

        except Exception as e:
            print(f"Error updating crosshair info: {e}")
            self._clear_crosshair_info()

    def _calculate_cycle_position(self, candle_index, cycles):
        """Calculate the cycle position (H1, H2, L1, L2, etc.) for the given candle index."""
        try:
            if not cycles or candle_index >= len(cycles):
                return "--"

            # Get the current cycle type from the cycles data
            current_cycle = cycles[candle_index]
            cycle_type = current_cycle.cycle_type  # "bullish" or "bearish"

            # Find the start of the current cycle by looking backwards for a direction change
            cycle_start_index = 0
            for i in range(candle_index, -1, -1):
                if i < len(cycles) and cycles[i].direction_changed:
                    cycle_start_index = i
                    break

            # Calculate position within the cycle (1-based)
            position_in_cycle = candle_index - cycle_start_index + 1

            # Determine the prefix based on cycle type
            if cycle_type == "bullish":
                return f"H{position_in_cycle}"
            elif cycle_type == "bearish":
                return f"L{position_in_cycle}"
            else:
                return "--"

        except Exception as e:
            print(f"Error calculating cycle position: {e}")
            return "--"

    def _get_zero_percent_price(self):
        """Get the zero percent price for converting percentages to actual prices."""
        try:
            if not hasattr(self, "current_data") or not self.current_data:
                return 0.0

            # Check if zero percent price is available in the data
            peak_trough_data = self.current_data.get("peak_trough_business_data", {})
            if "current_pivot" in peak_trough_data:
                return float(peak_trough_data["current_pivot"])

            # Use pre-computed zero percent price from backend
            if "zero_percent_price" in self.current_data:
                return float(self.current_data["zero_percent_price"])

            # No fallback - UI should never compute

            return 0.0
        except Exception as e:
            print(f"Error getting zero percent price: {e}")
            return 0.0

    def _clear_crosshair_info(self):
        """Clear all information labels."""
        self.idx_label.setText("IDX: --")
        self.date_label.setText("Date: --")
        self.category_label.setText("Category: --")
        self.open_label.setText("Open: --")
        self.high_label.setText("High: --")
        self.low_label.setText("Low: --")
        self.close_label.setText("Close: --")
        self.volume_label.setText("Volume: --")
        self.crosshair_price_label.setText("Crosshair: --")

    def _on_mouse_clicked(self, event):
        """Handle mouse clicks for drawing tools."""
        if not hasattr(self, "drawing_tools") or not self.current_drawing_tool:
            return

        # Only handle left clicks
        if event.button() != Qt.MouseButton.LeftButton:
            return

        # Get the position in plot coordinates
        pos = self.chart_widget.plotItem.vb.mapSceneToView(event.scenePos())

        # Handle eraser tool
        if self.current_drawing_tool == "eraser":
            print(
                f"ERASER ACTIVATED: Trying to erase at position {pos.x():.2f}, {pos.y():.2f}"
            )
            if self.drawing_tools.erase_item_at_position(pos):
                print("SUCCESS: Drawing item erased")
            else:
                print("FAILED: No drawing item found at click position")
            return

        # Handle drawing based on the current state and tool
        if not self.drawing_tools.drawing:
            # Starting a new drawing
            self.drawing_tools.start_drawing(pos, self.current_drawing_tool)

            # Special handling for text tool (immediate completion)
            if self.current_drawing_tool == "text":
                text, ok = QInputDialog.getText(self, "Add Text", "Enter text:")
                if ok and text:
                    self.drawing_tools.add_text(pos, text)
                    self.set_drawing_tool(None)  # Reset tool after adding text
                else:
                    # Cancel drawing if user cancels text entry
                    self.drawing_tools.drawing = False
                    if self.drawing_tools.current_item:
                        self.chart_widget.removeItem(self.drawing_tools.current_item)
                    self.drawing_tools.current_item = None
                    self.set_drawing_tool(None)

            # Special handling for horizontal and vertical lines (immediate completion)
            elif self.current_drawing_tool in ["horizontal", "vertical"]:
                self.drawing_tools.finish_drawing(pos)
                self.set_drawing_tool(None)  # Reset tool after drawing

            # For line and rectangle, we need a second click to finish
            # So we don't do anything else here
        else:
            # We're already drawing, so this click finishes the drawing
            self.drawing_tools.finish_drawing(pos)
            self.set_drawing_tool(None)  # Reset tool after drawing

    def _on_mouse_moved(self, event):
        """Handle mouse movement for drawing tools."""
        if not hasattr(self, "drawing_tools"):
            return

        # Convert event position to plot coordinates
        if isinstance(event, QPointF):
            # Direct QPointF from SignalProxy
            pos = self.chart_widget.plotItem.vb.mapSceneToView(event)
        else:
            # List from SignalProxy
            pos = self.chart_widget.plotItem.vb.mapSceneToView(event[0])

        # Update drawing if in progress
        if self.drawing_tools.drawing:
            # For text tool, just update the position
            if self.current_drawing_tool == "text" and self.drawing_tools.current_item:
                self.drawing_tools.current_item.setPos(pos.x(), pos.y())
            # For other tools, use the update_drawing method
            elif self.current_drawing_tool in [
                "line",
                "rectangle",
                "horizontal",
                "vertical",
            ]:
                self.drawing_tools.update_drawing(pos)

            # Show a different cursor when drawing
            self.chart_widget.setCursor(Qt.CursorShape.CrossCursor)

    # REMOVED: Data fetching is now handled centrally by Universal Controls
    # MarketOddsTab now only receives data via the centralized signal system
    # The on_fetch_data_requested method is no longer needed

    @TypedSlotDecorator.typed_slot(dict)
    def on_data_received(self, data: Dict[str, Any]):
        """
        Handle received market data with enterprise-grade error handling.
        MarketOddsTab now only receives data - does NOT broadcast to other tabs.
        Broadcasting is handled centrally by Universal Controls.
        """
        try:
            self.logger.info("Market data received successfully")
            self.current_data = data
            self.update_chart()

            # REMOVED: No longer broadcasts to other tabs - Universal Controls handles this
            # self.signals.market_data.chart_data_updated.emit(data)

            # Emit completion signal for backward compatibility
            self.data_fetch_completed.emit()

        except Exception as e:
            error_msg = f"Error processing received data: {str(e)}"
            self.logger.error(error_msg)
            self.on_data_error(error_msg)

    @TypedSlotDecorator.typed_slot(str)
    def on_data_error(self, error_msg: str):
        """
        Handle data fetch error with structured logging.
        Uses well-typed slot decorator for type safety.
        """
        self.logger.error(f"Data fetch error: {error_msg}")

        # Emit error signal for other components
        ticker = getattr(self, "_current_ticker", "unknown")
        self.signals.market_data.data_fetch_failed.emit(ticker, error_msg)

        # Emit completion signal for backward compatibility
        self.data_fetch_completed.emit()

    @TypedSlotDecorator.typed_slot(str)
    def on_data_progress(self, status_msg: str):
        """
        Handle data fetch progress updates.
        Uses well-typed slot decorator for type safety.
        """
        self.logger.debug(f"Data fetch progress: {status_msg}")

        # Emit progress signal for UI updates
        ticker = getattr(self, "_current_ticker", "unknown")
        self.signals.market_data.data_fetch_progress.emit(ticker, status_msg)

    def update_chart(self):
        """Completely rebuild and update the chart with rebased percentage data only."""
        if not self.current_data:
            return

        # Clear everything
        self.chart_widget.clear()

        # Clear infinity lines list (items are already removed by chart.clear())
        if hasattr(self, "stdev_infinity_lines"):
            self.stdev_infinity_lines.clear()

        # Always use rebased percentage data - no fallback to regular prices
        if "rebased_data" in self.current_data and self.current_data["rebased_data"]:
            # Get custom colors if available
            bullish_color = "#4CAF50"  # Default
            bearish_color = "#F44336"  # Default

            if hasattr(self, "chart_colors"):
                bullish_color = self.chart_colors.get(
                    "Bullish candle color", bullish_color
                )
                bearish_color = self.chart_colors.get(
                    "Bearish candle color", bearish_color
                )

            # Use rebased percentage data with custom colors
            self.candlestick_item = RebasedCandlestickItem(
                self.current_data["rebased_data"],
                bullish_color=bullish_color,
                bearish_color=bearish_color,
            )
            self.chart_widget.addItem(self.candlestick_item)

            # Add rebased vector line
            self._add_rebased_vector()

            # Add peak and trough annotations
            self._add_peak_trough_annotations()

            # Add 0% label with real OHLC price (calculate zero percent price first)
            self._add_zero_percent_label()

            # Add standard deviation infinity lines (after zero percent price is available)
            self._add_stdev_infinity_lines()

            # Add mean peak and trough infinity lines
            self._add_mean_peak_trough_lines()

            # Add predictive cycle lines
            self._add_predictive_cycle_lines()

            # Remove chart labels for cleaner appearance
            self.chart_widget.setLabel("left", "")
            self.chart_widget.setLabel("bottom", "")

            # Auto-range Y axis only
            self.chart_widget.enableAutoRange("y", True)
            self.chart_widget.autoRange()

            # Add the Donchian indicator strip overlay
            self._add_donchian_indicator_strip()

            # Update the separate Bollinger Bandwidth chart
            self._update_separate_bandwidth_chart()

            # Set proper chart ranges after all items are added
            self._set_final_chart_ranges()

            # Re-initialize crosshair after chart update (since chart.clear() removed it)
            self._reinitialize_crosshair()
        else:
            # No rebased data available - show error
            print("Error: No rebased data available. C++ kernels may have failed.")
            # Could add error display widget here if needed

    def _add_donchian_midpoint(self):
        """Add Donchian midpoint line with stairstep pattern."""
        if "donchian_midpoint" not in self.current_data:
            return

        timestamps = self.current_data["timestamp"]
        donchian_values = self.current_data["donchian_midpoint"]

        # Use pre-computed visualization data from backend
        if "donchian_stairstep_data" in self.current_data:
            stairstep_data = self.current_data["donchian_stairstep_data"]
            if stairstep_data.get("is_valid", False):
                valid_times = stairstep_data["x_values"]
                valid_values = stairstep_data["y_values"]
            else:
                return
        else:
            # Fallback: basic filtering (should be eliminated)
            valid_times = [
                timestamps[i] for i, val in enumerate(donchian_values) if val == val
            ]
            valid_values = [val for val in donchian_values if val == val]

        if len(valid_values) == 0:
            return

        # Create stairstep pattern with 0.5 left offset
        step_x = []
        step_y = []

        # Use pre-computed stairstep data if available (eliminates UI computation)
        if "donchian_stairstep_data" in self.current_data:
            stairstep_data = self.current_data["donchian_stairstep_data"]
            if stairstep_data.get("is_valid", False):
                step_x = stairstep_data["x_values"]
                step_y = stairstep_data["y_values"]
            else:
                return
        else:
            # Fallback: basic stairstep creation (should be eliminated)
            for i in range(len(valid_times)):
                t = float(valid_times[i])
                v = float(valid_values[i])
                step_x.append(t - 0.5)
                step_y.append(v)
                if i < len(valid_times) - 1:
                    next_t = float(valid_times[i + 1])
                    step_x.append(next_t - 0.5)
                    step_y.append(v)
                else:
                    step_x.append(t + 0.5)
                    step_y.append(v)

        # Plot the Donchian midpoint line
        # Get custom color if available - ONLY for Donchian midpoint
        line_color = "#831483"  # Default color
        if hasattr(self, "chart_colors"):
            line_color = self.chart_colors.get("Donchian midpoint color", line_color)

        self.donchian_line = self.chart_widget.plot(
            step_x, step_y, pen=pg.mkPen(line_color, width=2), name="Donchian Midpoint"
        )

    def _add_rebased_vector(self):
        """Add rebased vector line for percentage view."""
        if (
            "rebased_vector" not in self.current_data
            or "rebased_data" not in self.current_data
        ):
            return

        rebased_vector = self.current_data["rebased_vector"]
        rebased_data = self.current_data["rebased_data"]

        if not rebased_vector or not rebased_data:
            return

        # Extract indices from rebased data
        indices = [item[0] for item in rebased_data]

        # Ensure we have matching lengths
        min_length = min(len(indices), len(rebased_vector))
        if min_length == 0:
            return

        plot_indices = indices[:min_length]
        plot_vector = rebased_vector[:min_length]

        # Filter valid data - avoid numpy NaN checking
        valid_data = [
            (idx, vec) for idx, vec in zip(plot_indices, plot_vector) if vec == vec
        ]  # NaN != NaN in Python

        if not valid_data:
            return

        valid_indices, valid_vector = zip(*valid_data)

        # Create stairstep pattern with 0.5 left offset
        step_x = []
        step_y = []

        # Use pre-computed vector stairstep data if available (eliminates UI computation)
        if "rebased_vector_stairstep_data" in self.current_data:
            stairstep_data = self.current_data["rebased_vector_stairstep_data"]
            if stairstep_data.get("is_valid", False):
                step_x = stairstep_data["x_values"]
                step_y = stairstep_data["y_values"]
            else:
                return
        else:
            # Fallback: basic stairstep creation (should be eliminated)
            for i in range(len(valid_indices)):
                idx = float(valid_indices[i])
                vec = float(valid_vector[i])
                step_x.append(idx - 0.5)
                step_y.append(vec)
                if i < len(valid_indices) - 1:
                    next_idx = float(valid_indices[i + 1])
                    step_x.append(next_idx - 0.5)
                    step_y.append(vec)
                else:
                    step_x.append(idx + 0.5)
                    step_y.append(vec)

        # Plot the rebased vector line (this IS the Donchian midpoint on percentage chart)
        line_color = "#9C27B0"  # Default Material Design Purple
        if hasattr(self, "chart_colors"):
            line_color = self.chart_colors.get("Donchian midpoint color", line_color)

        # Store the rebased vector line for visibility control
        self.rebased_vector_line = self.chart_widget.plot(
            step_x, step_y, pen=pg.mkPen(line_color, width=2), name="Donchian Midpoint"
        )

    def _prepare_peak_trough_display_data(self, business_data):
        """Convert business data to display data - Frontend Visualization Layer."""
        display_data = []

        # Get custom colors if available
        peak_color = "#00FF00"  # Default green
        trough_color = "#FF0000"  # Default red
        if hasattr(self, "chart_colors"):
            peak_color = self.chart_colors.get("Peaks color", peak_color)
            trough_color = self.chart_colors.get("Troughs color", trough_color)

        # Process peaks for visualization
        for peak_data in business_data["peaks"]:
            # Ray line data
            display_data.append(
                {
                    "type": "ray",
                    "start_x": peak_data["index"],
                    "end_x": peak_data["ray_end"],
                    "start_y": peak_data["level"],
                    "end_y": peak_data["level"],
                    "color": peak_color,
                    "width": 2,
                    "style": "dash",
                }
            )

            # H label with candle count (small text)
            display_data.append(
                {
                    "type": "label",
                    "text": peak_data["label"],
                    "x": peak_data["index"],
                    "y": peak_data["position"],
                    "color": peak_color,
                    "anchor": (0.5, 0.5),
                    "font_size": 6,  # Small font
                }
            )

            # Ray end label (closed/target price) - Pure PyQt6 visualization only
            if "actual_price" in peak_data:
                text_content = (
                    "closed"
                    if peak_data["is_closed"]
                    else f"{peak_data['actual_price']:,.2f} (Target)"
                )
                display_data.append(
                    {
                        "type": "label",
                        "text": text_content,
                        "x": peak_data["ray_end"],
                        "y": peak_data["level"],
                        "color": peak_color,
                        "anchor": (0, 0.5),
                        "font_size": 6,  # Small font
                    }
                )
            elif "current_pivot" in peak_data and "level_percentage" in peak_data:
                # Fallback: show level percentage when C++ calculation not available
                text_content = (
                    "closed"
                    if peak_data["is_closed"]
                    else f"{peak_data['level_percentage']:.1f}% (Target)"
                )
                display_data.append(
                    {
                        "type": "label",
                        "text": text_content,
                        "x": peak_data["ray_end"],
                        "y": peak_data["level"],
                        "color": peak_color,
                        "anchor": (0, 0.5),
                        "font_size": 6,  # Small font
                    }
                )

        # Process troughs for visualization
        for trough_data in business_data["troughs"]:
            # Ray line data
            display_data.append(
                {
                    "type": "ray",
                    "start_x": trough_data["index"],
                    "end_x": trough_data["ray_end"],
                    "start_y": trough_data["level"],
                    "end_y": trough_data["level"],
                    "color": trough_color,
                    "width": 2,
                    "style": "dash",
                }
            )

            # L label with candle count (small text)
            display_data.append(
                {
                    "type": "label",
                    "text": trough_data["label"],
                    "x": trough_data["index"],
                    "y": trough_data["position"],
                    "color": trough_color,
                    "anchor": (0.5, 0.5),
                    "font_size": 6,  # Small font
                }
            )

            # Ray end label (closed/target price) - Pure PyQt6 visualization only
            if "actual_price" in trough_data:
                text_content = (
                    "closed"
                    if trough_data["is_closed"]
                    else f"{trough_data['actual_price']:,.2f} (Target)"
                )
                display_data.append(
                    {
                        "type": "label",
                        "text": text_content,
                        "x": trough_data["ray_end"],
                        "y": trough_data["level"],
                        "color": trough_color,
                        "anchor": (0, 0.5),
                        "font_size": 6,  # Small font
                    }
                )
            elif "current_pivot" in trough_data and "level_percentage" in trough_data:
                # Fallback: show level percentage when C++ calculation not available
                text_content = (
                    "closed"
                    if trough_data["is_closed"]
                    else f"{trough_data['level_percentage']:.1f}% (Target)"
                )
                display_data.append(
                    {
                        "type": "label",
                        "text": text_content,
                        "x": trough_data["ray_end"],
                        "y": trough_data["level"],
                        "color": trough_color,
                        "anchor": (0, 0.5),
                        "font_size": 6,  # Small font
                    }
                )

        return display_data

    def _add_peak_trough_annotations(self):
        """Add peak and trough annotations using business data - Frontend Visualization Layer."""
        if "peak_trough_business_data" not in self.current_data:
            return

        business_data = self.current_data["peak_trough_business_data"]
        if not business_data:
            return

        # Convert business data to display data (frontend visualization logic)
        display_data = self._prepare_peak_trough_display_data(business_data)

        # Initialize the annotation system if not already done (cached)
        if not hasattr(self, "peak_trough_annotations"):
            self.peak_trough_annotations = PeakTroughAnnotations(self.chart_widget)

        # Draw annotations using pure visualization logic
        self.peak_trough_annotations.draw_annotations(display_data)

    def _refresh_peak_trough_annotations(self):
        """Refresh peak and trough annotations to apply new colors."""
        if (
            hasattr(self, "current_data")
            and "peak_trough_business_data" in self.current_data
        ):
            # Re-run the annotation creation with updated colors
            self._add_peak_trough_annotations()

    def _add_stdev_infinity_lines(self):
        """Add standard deviation infinity lines based on actual data distribution - Pure PyQt6 visualization."""
        if not self.current_data or "rebased_data" not in self.current_data:
            return

        # Initialize infinity lines list if not exists
        if not hasattr(self, "stdev_infinity_lines"):
            self.stdev_infinity_lines = []

        # Clear existing infinity lines and labels
        for line_item in self.stdev_infinity_lines:
            self.chart_widget.removeItem(line_item)
        self.stdev_infinity_lines.clear()

        # Clear label tracking
        if hasattr(self, "stdev_labels"):
            self.stdev_labels.clear()

        # Use pre-computed standard deviation lines from backend (eliminates all UI computation)
        if "stdev_infinity_lines" in self.current_data:
            stdev_lines_data = self.current_data["stdev_infinity_lines"]
            if not stdev_lines_data.get("is_valid", False):
                return
        else:
            # No fallback - UI should never compute
            return

        # Use pre-computed standard deviation levels from backend
        stdev_levels = stdev_lines_data.get("stdev_levels", [])

        # Add each standard deviation level as an infinity line with custom right-aligned label
        for level_data in stdev_levels:
            try:
                level_value = level_data["level_value"]
                percentile_label = level_data["percentile_label"]
                multiplier = level_data["multiplier"]

                # Use pre-computed label from backend
                label = level_data.get(
                    "label", f"{percentile_label}: {level_value:.2f}%"
                )

                # Create infinite horizontal line without built-in label
                line_item = pg.InfiniteLine(
                    pos=level_value,
                    angle=0,  # Horizontal line
                    pen=pg.mkPen(
                        color=pg.QtGui.QColor(128, 128, 128),
                        width=1,
                        style=pg.QtCore.Qt.PenStyle.SolidLine,
                    ),
                    movable=False,
                )

                # Add to chart
                self.chart_widget.addItem(line_item)
                self.stdev_infinity_lines.append(line_item)

                # Create custom right-aligned label with color coding
                self._add_right_aligned_stdev_label(level_value, label, multiplier)

            except Exception as e:
                # Skip this line if there's any error creating it
                print(f"Warning: Could not create infinity line at {level_value}: {e}")
                continue

    def _add_right_aligned_stdev_label(self, level_value, label_text, multiplier):
        """Add right-aligned infinity zone label with color coding based on positive/negative standard deviation."""
        try:
            # Determine color based on percentile value (special handling for 2% levels)
            if abs(multiplier) == 2.0:
                # 2% levels - purple zone
                text_color = pg.QtGui.QColor(128, 0, 128)  # Purple text
                fill_color = pg.QtGui.QColor(
                    64, 0, 64, 120
                )  # Semi-transparent purple background
            elif multiplier >= 0:
                # Positive percentile levels (peaks) - green zone
                text_color = pg.QtGui.QColor(0, 255, 0)  # Green text
                fill_color = pg.QtGui.QColor(
                    0, 100, 0, 120
                )  # Semi-transparent green background
            else:
                # Negative percentile levels (troughs) - red zone
                text_color = pg.QtGui.QColor(255, 0, 0)  # Red text
                fill_color = pg.QtGui.QColor(
                    100, 0, 0, 120
                )  # Semi-transparent red background

            # Create truly infinite background zone using InfiniteLine with custom pen
            zone_line = pg.InfiniteLine(
                pos=level_value,
                angle=0,  # Horizontal line
                pen=pg.mkPen(
                    color=fill_color, width=5
                ),  # 5px line to create zone effect
                movable=False,
            )
            self.chart_widget.addItem(zone_line)
            self.stdev_infinity_lines.append(zone_line)

            # Create 8px font for the text
            font = pg.QtGui.QFont()
            font.setPixelSize(8)  # Exactly 8px font size
            font.setBold(True)

            # Create text item that stays at the right edge of the chart
            text_item = pg.TextItem(
                text=label_text,
                color=text_color,
                fill=pg.mkBrush(
                    color=pg.QtGui.QColor(0, 0, 0, 180)
                ),  # Semi-transparent black background for readability
                anchor=(1.0, 0.5),  # Right-aligned
            )
            text_item.setFont(font)

            # Position the text at a fixed screen position (right edge)
            # This will be updated when the view changes
            self._position_stdev_label_at_right_edge(text_item, level_value)

            # Add text item to chart
            self.chart_widget.addItem(text_item)

            # Store both the text item and its level for repositioning
            self.stdev_infinity_lines.append(text_item)

            # Store label info for repositioning on view changes
            if not hasattr(self, "stdev_labels"):
                self.stdev_labels = []
            self.stdev_labels.append((text_item, level_value))

        except Exception as e:
            print(
                f"Warning: Could not create right-aligned infinity label for {label_text}: {e}"
            )

    def _position_stdev_label_at_right_edge(self, text_item, level_value):
        """Position a standard deviation label at the right edge of the visible chart area."""
        try:
            # Get the current view range
            view_range = self.chart_widget.getViewBox().viewRange()
            right_x = view_range[0][1] - 0.5  # Slightly inside the right edge
            text_item.setPos(right_x, level_value)
        except Exception as e:
            print(f"Warning: Could not position label at right edge: {e}")

    def _update_stdev_label_positions(self):
        """Update positions of all standard deviation labels to keep them at the right edge."""
        if hasattr(self, "stdev_labels"):
            for text_item, level_value in self.stdev_labels:
                self._position_stdev_label_at_right_edge(text_item, level_value)

    def _add_mean_peak_trough_lines(self):
        """Add mean peak and mean trough as infinity lines on the main chart."""
        if not self.current_data:
            return

        try:
            # Clear existing mean peak/trough lines
            if hasattr(self, "mean_peak_trough_lines"):
                for line in self.mean_peak_trough_lines:
                    self.chart_widget.removeItem(line)
            self.mean_peak_trough_lines = []

            # Mean levels are now pre-computed in backend

            # Use pre-computed mean peak level from backend (eliminates UI computation)
            if "mean_peak_trough_levels" in self.current_data:
                levels_data = self.current_data["mean_peak_trough_levels"]
                mean_peak = levels_data.get("mean_peak_level")

                if mean_peak is not None and abs(mean_peak) < float("inf"):
                    # Get custom color if available
                    line_color = "#00FF00"  # Default green
                    if hasattr(self, "chart_colors"):
                        line_color = self.chart_colors.get(
                            "Mean horizontal lines color", line_color
                        )

                    # Create mean peak infinity line (dashed)
                    mean_peak_line = pg.InfiniteLine(
                        pos=float(mean_peak),
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(
                            color=line_color,
                            width=1,
                            style=pg.QtCore.Qt.PenStyle.DashLine,
                        ),
                        movable=False,
                    )
                    self.chart_widget.addItem(mean_peak_line)
                    self.mean_peak_trough_lines.append(mean_peak_line)

            # Use pre-computed mean trough level from backend (eliminates UI computation)
            if "mean_peak_trough_levels" in self.current_data:
                levels_data = self.current_data["mean_peak_trough_levels"]
                mean_trough = levels_data.get("mean_trough_level")

                if mean_trough is not None and abs(mean_trough) < float("inf"):
                    # Get custom color if available
                    line_color = "#FF0000"  # Default red
                    if hasattr(self, "chart_colors"):
                        line_color = self.chart_colors.get(
                            "Mean horizontal lines color", line_color
                        )

                    # Create mean trough infinity line (dashed)
                    mean_trough_line = pg.InfiniteLine(
                        pos=float(mean_trough),
                        angle=0,  # Horizontal line
                        pen=pg.mkPen(
                            color=line_color,
                            width=1,
                            style=pg.QtCore.Qt.PenStyle.DashLine,
                        ),
                        movable=False,
                    )
                    self.chart_widget.addItem(mean_trough_line)
                    self.mean_peak_trough_lines.append(mean_trough_line)

        except Exception as e:
            print(f"Warning: Could not add mean peak/trough lines: {e}")

    def _add_predictive_cycle_lines(self):
        """Add predictive cycle lines based on mean H/L strengths and last peak/trough type."""
        if not self.current_data:
            return

        try:
            # Clear existing predictive cycle lines
            if hasattr(self, "predictive_cycle_lines"):
                for line in self.predictive_cycle_lines:
                    self.chart_widget.removeItem(line)
            self.predictive_cycle_lines = []

            # Get peaks and troughs data
            peaks = self.current_data.get("peaks", [])
            troughs = self.current_data.get("troughs", [])
            rebased_data = self.current_data.get("rebased_data", [])

            if not peaks or not troughs or not rebased_data:
                return

            # Strengths are now pre-computed in backend - no UI computation needed

            # Use pre-computed predictive cycle data from backend (eliminates all UI computation)
            if "predictive_cycle_lines" in self.current_data:
                cycle_lines_data = self.current_data["predictive_cycle_lines"]
                if not cycle_lines_data.get("is_valid", False):
                    return
            else:
                # No fallback - UI should never compute
                return

            # Use pre-computed predictive cycle line segments from backend
            line_segments = cycle_lines_data.get("line_segments", [])

            for segment in line_segments:
                x1, y1, x2, y2 = (
                    segment["x1"],
                    segment["y1"],
                    segment["x2"],
                    segment["y2"],
                )

                # Get custom color if available
                line_color = "white"  # Default white
                if hasattr(self, "chart_colors"):
                    line_color = self.chart_colors.get("Mean Wave Color", line_color)

                # Create line segment
                line = pg.PlotDataItem(
                    x=[x1, x2],
                    y=[y1, y2],
                    pen=pg.mkPen(color=line_color, width=2),  # Solid 2px line
                    connect="all",
                )

                self.chart_widget.addItem(line)
                self.predictive_cycle_lines.append(line)

        except Exception as e:
            print(f"Warning: Could not add predictive cycle lines: {e}")

    def _add_donchian_indicator_strip(self):
        """Add white infinity line at y=0 on the main chart."""
        if not self.current_data:
            return

        # Clear existing indicator strip items
        for item in self.indicator_strip_items:
            self.chart_widget.removeItem(item)
        self.indicator_strip_items.clear()

        # Add white infinity line at y=0 with 2px thickness
        try:
            zero_line = pg.InfiniteLine(
                pos=0,
                angle=0,  # Horizontal line
                pen=pg.mkPen(
                    color=pg.QtGui.QColor(255, 255, 255), width=2
                ),  # White 2px line using PyQt6
                movable=False,
            )

            # Add to main chart
            self.chart_widget.addItem(zero_line)
            self.indicator_strip_items.append(zero_line)

        except Exception as e:
            print(f"Warning: Could not create white infinity line at y=0: {e}")

    def _update_separate_bandwidth_chart(self):
        """Update the separate Bollinger Bandwidth chart widget."""
        if not self.current_data or not hasattr(self, "bandwidth_chart"):
            return

        # Check if we have the required data
        if (
            "timestamp" not in self.current_data
            or "bollinger_bandwidth" not in self.current_data
        ):
            return

        timestamps = self.current_data["timestamp"]
        bandwidth_values = self.current_data["bollinger_bandwidth"]

        # Get Donchian strip segments if available
        donchian_strip_segments = self.current_data.get("donchian_strip_segments", None)

        # Update the separate bandwidth chart with simple white line
        self.bandwidth_chart.update_bandwidth(
            timestamps, bandwidth_values, donchian_strip_segments
        )

        # Add peak/trough lines to right chart
        self._add_peak_trough_lines()

    def _get_bandwidth_color_by_price_position(self):
        """Get Bollinger Bandwidth line color based on current price vs main chart Donchian midpoint."""
        try:
            if not hasattr(self, "current_data") or not self.current_data:
                return "#CCCCCC"  # Gray fallback

            # Use pre-computed color from backend (eliminates UI computation)
            if "donchian_color" in self.current_data:
                return self.current_data["donchian_color"]

            # Fallback to gray if no pre-computed color available
            return "#CCCCCC"

        except Exception as e:
            print(f"Exception in _get_bandwidth_color_by_price_position: {e}")
            return "#CCCCCC"  # Gray fallback

    def _set_final_chart_ranges(self):
        """Set final chart ranges to show all data from index 0."""
        if not self.current_data:
            return

        # Calculate the total data length
        total_data_length = len(self.current_data["timestamp"])

        # Set X range to show all data from 0 to the end
        self.chart_widget.setXRange(-1, total_data_length, padding=0)


class ChartSettingsDialog(QDialog):
    """Settings dialog for chart customization."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_widget = parent
        self.setWindowTitle("Chart Settings")
        self.setModal(True)
        self.setFixedSize(600, 700)
        self.setStyleSheet(
            """
            QDialog {
                background-color: #1e1e1e;
                color: white;
            }
            QLabel {
                color: white;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
            }
            QPushButton {
                background-color: #2d2d2d;
                color: white;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #3d3d3d;
                border: 1px solid #777777;
            }
            QPushButton:pressed {
                background-color: #1d1d1d;
            }

            QScrollArea {
                background-color: #1e1e1e;
                border: 1px solid #3e3e3e;
            }
        """
        )

        self._setup_dialog()

    def _setup_dialog(self):
        """Setup the dialog layout and controls."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # Create scroll area for the content
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # Add sections
        self._create_visibility_section(content_layout)
        self._create_color_section(content_layout)

        scroll.setWidget(content_widget)
        layout.addWidget(scroll)

        # Add buttons
        button_layout = QHBoxLayout()

        # Reset to Defaults button
        reset_button = QPushButton("Reset to Defaults")
        reset_button.clicked.connect(self._reset_to_defaults)
        button_layout.addWidget(reset_button)

        button_layout.addStretch()

        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

    def _create_visibility_section(self, layout):
        """Create the visibility toggles section."""
        # Section title
        title = QLabel("Visibility Settings")
        title.setStyleSheet("font-weight: bold; font-size: 13px; margin-bottom: 10px;")
        layout.addWidget(title)

        # Get current persistent settings from parent widget
        current_settings = {}
        if self.parent_widget and hasattr(self.parent_widget, "persistent_settings"):
            current_settings = self.parent_widget.persistent_settings
        elif self.parent_widget and hasattr(self.parent_widget, "chart_settings"):
            current_settings = self.parent_widget.chart_settings

        # Create checkboxes for visibility toggles with native PyQt6 styling
        self.stdev_checkbox = QCheckBox("Show StDev levels")
        self.stdev_checkbox.setChecked(current_settings.get("show_stdev", True))
        self.stdev_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.stdev_checkbox)
        layout.addWidget(self.stdev_checkbox)

        self.candles_checkbox = QCheckBox("Show Candles")
        self.candles_checkbox.setChecked(current_settings.get("show_candles", True))
        self.candles_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.candles_checkbox)
        layout.addWidget(self.candles_checkbox)

        self.donchian_checkbox = QCheckBox("Show Donchian midpoint")
        self.donchian_checkbox.setChecked(current_settings.get("show_donchian", True))
        self.donchian_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.donchian_checkbox)
        layout.addWidget(self.donchian_checkbox)

        self.mean_horizontal_checkbox = QCheckBox("Show Mean horizontal lines")
        self.mean_horizontal_checkbox.setChecked(
            current_settings.get("show_mean_horizontal", True)
        )
        self.mean_horizontal_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.mean_horizontal_checkbox)
        layout.addWidget(self.mean_horizontal_checkbox)

        self.mean_line_checkbox = QCheckBox("Show Mean Wave")
        self.mean_line_checkbox.setChecked(current_settings.get("show_mean_line", True))
        self.mean_line_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.mean_line_checkbox)
        layout.addWidget(self.mean_line_checkbox)

        self.second_chart_checkbox = QCheckBox("Show Second chart")
        self.second_chart_checkbox.setChecked(
            current_settings.get("show_second_chart", True)
        )
        self.second_chart_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.second_chart_checkbox)
        layout.addWidget(self.second_chart_checkbox)

        self.peaks_troughs_checkbox = QCheckBox("Show Peaks and troughs")
        self.peaks_troughs_checkbox.setChecked(
            current_settings.get("show_peaks_troughs", True)
        )
        self.peaks_troughs_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.peaks_troughs_checkbox)
        layout.addWidget(self.peaks_troughs_checkbox)

        self.right_chart_checkbox = QCheckBox("Show Right chart with peaks/troughs")
        self.right_chart_checkbox.setChecked(
            current_settings.get("show_right_chart", True)
        )
        self.right_chart_checkbox.toggled.connect(self._on_visibility_changed)
        self._style_native_checkbox(self.right_chart_checkbox)
        layout.addWidget(self.right_chart_checkbox)

    def _style_native_checkbox(self, checkbox):
        """Apply native PyQt6 styling to checkbox - no CSS."""
        # Remove all CSS styling to use pure native PyQt6 appearance
        checkbox.setStyleSheet("")

        # Set font using PyQt6 methods instead of CSS
        font = QFont("Consolas", 10)
        if not font.exactMatch():
            font = QFont("Courier New", 10)
        if not font.exactMatch():
            font = QFont("monospace", 10)
        checkbox.setFont(font)

        # Set minimum height for better spacing
        checkbox.setMinimumHeight(25)

        # Enable focus policy for keyboard navigation
        checkbox.setFocusPolicy(Qt.FocusPolicy.TabFocus)

        # Create a custom palette that matches the dark theme
        palette = checkbox.palette()

        # Set text color to white
        palette.setColor(QPalette.ColorRole.WindowText, QColor("white"))

        # Set checkbox background colors to match dark theme
        palette.setColor(
            QPalette.ColorRole.Base, QColor("#2d2d2d")
        )  # Unchecked background
        palette.setColor(
            QPalette.ColorRole.Window, QColor("#1e1e1e")
        )  # Widget background
        palette.setColor(
            QPalette.ColorRole.Button, QColor("#2d2d2d")
        )  # Button background
        palette.setColor(QPalette.ColorRole.ButtonText, QColor("white"))  # Button text

        # Set highlight colors for checked state
        palette.setColor(
            QPalette.ColorRole.Highlight, QColor("#4d4d4d")
        )  # Checked background
        palette.setColor(
            QPalette.ColorRole.HighlightedText, QColor("white")
        )  # Checked text

        # Apply the palette
        checkbox.setPalette(palette)

    def _create_color_section(self, layout):
        """Create the color customization section."""
        # Section title
        title = QLabel("Color Settings")
        title.setStyleSheet(
            "font-weight: bold; font-size: 13px; margin-top: 20px; margin-bottom: 10px;"
        )
        layout.addWidget(title)

        # Initialize color buttons storage
        self.color_buttons = {}

        # Get current persistent colors from parent widget
        current_colors = {}
        if self.parent_widget and hasattr(self.parent_widget, "persistent_colors"):
            current_colors = self.parent_widget.persistent_colors
        elif self.parent_widget and hasattr(self.parent_widget, "chart_colors"):
            current_colors = self.parent_widget.chart_colors

        # Create new color buttons with current colors
        self._create_new_color_button(
            layout,
            "Donchian midpoint color",
            current_colors.get("Donchian midpoint color", "#800080"),
        )
        self._create_new_color_button(
            layout, "Peaks color", current_colors.get("Peaks color", "#00FF00")
        )
        self._create_new_color_button(
            layout, "Troughs color", current_colors.get("Troughs color", "#FF0000")
        )
        self._create_new_color_button(
            layout,
            "Bullish candle color",
            current_colors.get("Bullish candle color", "#4CAF50"),
        )
        self._create_new_color_button(
            layout,
            "Bearish candle color",
            current_colors.get("Bearish candle color", "#F44336"),
        )
        self._create_new_color_button(
            layout,
            "Positive StDev lines color",
            current_colors.get("Positive StDev lines color", "#00FF00"),
        )
        self._create_new_color_button(
            layout,
            "Negative StDev lines color",
            current_colors.get("Negative StDev lines color", "#FF0000"),
        )
        self._create_new_color_button(
            layout,
            "Mean horizontal lines color",
            current_colors.get("Mean horizontal lines color", "#FFFF00"),
        )
        self._create_new_color_button(
            layout, "Mean Wave Color", current_colors.get("Mean Wave Color", "#FFFFFF")
        )

    def _create_new_color_button(self, layout, label_text, default_color):
        """Create a completely new color selection button with immediate updates."""
        from PyQt6.QtGui import QColor
        from PyQt6.QtWidgets import QColorDialog

        container = QWidget()
        container_layout = QHBoxLayout(container)
        container_layout.setContentsMargins(5, 2, 5, 2)

        # Label
        label = QLabel(label_text)
        label.setMinimumWidth(200)
        container_layout.addWidget(label)

        # Color button with immediate update functionality
        color_button = QPushButton("Choose Color")
        color_button.setFixedSize(120, 32)
        color_button.current_color = default_color
        color_button.label_text = label_text

        # Set initial color appearance
        self._update_button_color(color_button, default_color)

        # Connect to immediate color selection
        def select_color_immediately():
            current_color = QColor(color_button.current_color)
            new_color = QColorDialog.getColor(
                current_color, self, f"Select {label_text}"
            )

            if new_color.isValid():
                color_hex = new_color.name()
                color_button.current_color = color_hex

                # Update button appearance immediately
                self._update_button_color(color_button, color_hex)

                # Apply color change immediately (like visibility toggles)
                self._apply_single_color_immediately(label_text, color_hex)

                print(f"Color changed immediately: {label_text} = {color_hex}")

        color_button.clicked.connect(select_color_immediately)
        container_layout.addWidget(color_button)

        container_layout.addStretch()
        layout.addWidget(container)

        # Store reference
        self.color_buttons[label_text] = color_button

    def _update_button_color(self, button, color_hex):
        """Update button color appearance using PyQt6 palette."""
        from PyQt6.QtGui import QPalette, QColor

        palette = button.palette()
        palette.setColor(QPalette.ColorRole.Button, QColor(color_hex))
        # Use contrasting text color
        text_color = "white" if self._is_dark_color(color_hex) else "black"
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(text_color))
        button.setPalette(palette)
        button.setAutoFillBackground(True)

    def _is_dark_color(self, color_hex):
        """Check if a color is dark (for text contrast)."""
        try:
            color_hex = color_hex.lstrip("#")
            r = int(color_hex[0:2], 16)
            g = int(color_hex[2:4], 16)
            b = int(color_hex[4:6], 16)
            # Calculate luminance
            luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
            return luminance < 0.5
        except:
            return True

    def _apply_single_color_immediately(self, color_name, color_value):
        """Apply a single color change immediately (like visibility toggles)."""
        if self.parent_widget and hasattr(self.parent_widget, "_apply_color_settings"):
            # Create a single-color dictionary and apply it immediately
            colors = {color_name: color_value}
            self.parent_widget._apply_color_settings(colors)

    def _on_visibility_changed(self):
        """Handle visibility toggle changes - applies immediately."""
        if self.parent_widget and hasattr(
            self.parent_widget, "_apply_visibility_settings"
        ):
            settings = {
                "show_stdev": self.stdev_checkbox.isChecked(),
                "show_candles": self.candles_checkbox.isChecked(),
                "show_donchian": self.donchian_checkbox.isChecked(),
                "show_mean_horizontal": self.mean_horizontal_checkbox.isChecked(),
                "show_mean_line": self.mean_line_checkbox.isChecked(),
                "show_second_chart": self.second_chart_checkbox.isChecked(),
                "show_peaks_troughs": self.peaks_troughs_checkbox.isChecked(),
                "show_right_chart": self.right_chart_checkbox.isChecked(),
            }
            print(f"Visibility changed immediately: {settings}")  # Debug output
            self.parent_widget._apply_visibility_settings(settings)

    def _on_color_changed(self):
        """Handle color changes - applies immediately (legacy method for compatibility)."""
        if self.parent_widget and hasattr(self.parent_widget, "_apply_color_settings"):
            # Extract colors from color buttons
            colors = {}
            if hasattr(self, "color_buttons"):
                for label, button in self.color_buttons.items():
                    colors[label] = button.current_color

            print(f"Color changed immediately (legacy): {colors}")  # Debug output
            self.parent_widget._apply_color_settings(colors)

    def _reset_to_defaults(self):
        """Reset all settings to default values."""
        # Default visibility settings
        default_visibility = {
            "show_stdev": True,
            "show_candles": True,
            "show_donchian": True,
            "show_mean_horizontal": True,
            "show_mean_line": True,
            "show_second_chart": True,
            "show_peaks_troughs": True,
            "show_right_chart": True,
        }

        # Default color settings
        default_colors = {
            "Donchian midpoint color": "#800080",
            "Peaks color": "#00FF00",
            "Troughs color": "#FF0000",
            "Bullish candle color": "#4CAF50",
            "Bearish candle color": "#F44336",
            "Positive StDev lines color": "#00FF00",
            "Negative StDev lines color": "#FF0000",
            "Mean horizontal lines color": "#FFFF00",
            "Mean Wave Color": "#FFFFFF",
        }

        # Reset visibility checkboxes
        self.stdev_checkbox.setChecked(default_visibility["show_stdev"])
        self.candles_checkbox.setChecked(default_visibility["show_candles"])
        self.donchian_checkbox.setChecked(default_visibility["show_donchian"])
        self.mean_horizontal_checkbox.setChecked(
            default_visibility["show_mean_horizontal"]
        )
        self.mean_line_checkbox.setChecked(default_visibility["show_mean_line"])
        self.second_chart_checkbox.setChecked(default_visibility["show_second_chart"])
        self.peaks_troughs_checkbox.setChecked(default_visibility["show_peaks_troughs"])
        self.right_chart_checkbox.setChecked(default_visibility["show_right_chart"])

        # Reset color buttons
        if hasattr(self, "color_buttons"):
            for label, button in self.color_buttons.items():
                if label in default_colors:
                    default_color = default_colors[label]
                    button.current_color = default_color
                    self._update_button_color(button, default_color)

        # Apply default settings immediately
        if self.parent_widget:
            if hasattr(self.parent_widget, "_apply_visibility_settings"):
                self.parent_widget._apply_visibility_settings(default_visibility)
            if hasattr(self.parent_widget, "_apply_color_settings"):
                self.parent_widget._apply_color_settings(default_colors)

        print("Settings reset to defaults")

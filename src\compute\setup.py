"""
Setup script for C++ compute kernels
Builds high-performance indicators module using pybind11
"""

from pybind11.setup_helpers import Pybind11Extension, build_ext
from setuptools import setup, Extension
import pybind11
import platform

# Enterprise-grade build profiles with optimization levels
import os

BUILD_PROFILE = os.environ.get("BUILD_PROFILE", "release")


def get_compiler_flags(profile="release"):
    """Get compiler flags based on build profile."""

    if platform.system() == "Windows":
        # MSVC flags
        base_flags = ["/std:c++17", "/DWITH_THREAD"]

        if profile == "release":
            return base_flags + [
                "/O2",  # Optimization
                "/Oi",  # Enable intrinsic functions
                "/Ot",  # Favor fast code
                "/GL",  # Whole program optimization
                "/DNDEBUG",  # Release mode
                "/arch:AVX2",  # Enable AVX2 instructions
                "/fp:fast",  # Fast floating point
                "/Qpar",  # Auto-parallelization
            ], [
                "/LTCG"
            ]  # Link-time code generation

        elif profile == "debug":
            return (
                base_flags
                + [
                    "/Od",  # Disable optimization
                    "/Zi",  # Debug information
                    "/DDEBUG",  # Debug mode
                    "/RTC1",  # Runtime checks
                    "/MDd",  # Debug runtime
                ],
                [],
            )

        elif profile == "sanitizer":
            return (
                base_flags
                + [
                    "/Od",  # Disable optimization for better debugging
                    "/Zi",  # Debug information
                    "/DDEBUG",  # Debug mode
                    "/fsanitize=address",  # AddressSanitizer (if supported)
                ],
                [],
            )

    else:
        # GCC/Clang flags
        base_flags = ["-std=c++17", "-DWITH_THREAD"]

        if profile == "release":
            return base_flags + [
                "-O3",  # Maximum optimization
                "-march=native",  # Use native CPU instructions
                "-mtune=native",  # Tune for native CPU
                "-ffast-math",  # Fast math operations
                "-flto",  # Link-time optimization
                "-fomit-frame-pointer",  # Omit frame pointer
                "-funroll-loops",  # Unroll loops
                "-DNDEBUG",  # Release mode
                "-mavx2",  # Enable AVX2 instructions
                "-fopenmp",  # OpenMP support
                "-fno-signed-zeros",  # Optimize floating point
                "-fno-trapping-math",
            ], ["-flto", "-fopenmp"]

        elif profile == "debug":
            return (
                base_flags
                + [
                    "-O0",  # No optimization
                    "-g3",  # Maximum debug info
                    "-DDEBUG",  # Debug mode
                    "-Wall",  # All warnings
                    "-Wextra",  # Extra warnings
                    "-Wpedantic",  # Pedantic warnings
                    "-fno-omit-frame-pointer",
                ],
                [],
            )

        elif profile == "sanitizer":
            return base_flags + [
                "-O1",  # Light optimization for better debugging
                "-g3",  # Maximum debug info
                "-DDEBUG",  # Debug mode
                "-fsanitize=address",  # AddressSanitizer
                "-fsanitize=thread",  # ThreadSanitizer
                "-fsanitize=undefined",  # UndefinedBehaviorSanitizer
                "-fno-omit-frame-pointer",
                "-fno-optimize-sibling-calls",
            ], ["-fsanitize=address", "-fsanitize=thread", "-fsanitize=undefined"]

    return base_flags, []


# Get flags for current build profile
cpp_args, link_args = get_compiler_flags(BUILD_PROFILE)

# Debug flags (uncomment for debugging)
# if platform.system() == "Windows":
#     cpp_args.extend(['/Zi', '/DDEBUG'])
# else:
#     cpp_args.extend(['-g', '-DDEBUG', '-fsanitize=address'])

# Define the extension
ext_modules = [
    Pybind11Extension(
        "indicators",
        sources=["indicators.cpp"],
        include_dirs=[
            pybind11.get_include(),
        ],
        cxx_std=17,
        extra_compile_args=cpp_args,
        extra_link_args=link_args,
    ),
]

setup(
    name="indicators",
    ext_modules=ext_modules,
    cmdclass={"build_ext": build_ext},
    zip_safe=False,
    python_requires=">=3.8",
)

"""
Enterprise-grade shared memory manager for true zero-copy data hand-off.
Implements shared memory buffers for ProcessPoolExecutor and C++ communication.
"""

import logging
import numpy as np
from typing import Any, Union, Dict, Optional, List, Tuple
import multiprocessing as mp
from multiprocessing import shared_memory
import mmap
import struct
import weakref
from dataclasses import dataclass
from contextlib import contextmanager
import threading
import os
import sys
from pathlib import Path

# Import observability
from .observability import get_structured_logger, trace_operation, metrics_collector

# Try to import C++ kernels if available
try:
    compute_dir = Path(__file__).parent.parent / "compute"
    if compute_dir.exists() and str(compute_dir) not in sys.path:
        sys.path.insert(0, str(compute_dir))
    import indicators

    CPP_KERNELS_AVAILABLE = True
except ImportError:
    CPP_KERNELS_AVAILABLE = False


@dataclass
class SharedBuffer:
    """Shared memory buffer descriptor for zero-copy operations."""

    name: str
    size: int
    dtype: np.dtype
    shape: tuple
    shm: shared_memory.SharedMemory

    def get_array(self) -> np.ndarray:
        """Get NumPy array view of shared memory with zero-copy."""
        return np.ndarray(self.shape, dtype=self.dtype, buffer=self.shm.buf)

    def get_memoryview(self) -> memoryview:
        """Get memoryview for direct buffer access."""
        return memoryview(self.shm.buf)

    def cleanup(self):
        """Clean up shared memory resources."""
        try:
            self.shm.close()
            self.shm.unlink()
        except Exception:
            pass  # Ignore cleanup errors in case already cleaned


class SharedMemoryManager:
    """
    Enterprise-grade thread-safe shared memory manager for zero-copy data operations.
    Eliminates serialization overhead for ProcessPoolExecutor and C++ communication.
    """

    def __init__(self):
        self.logger = get_structured_logger("SharedMemoryManager")
        self._buffers: Dict[str, SharedBuffer] = {}

        # Thread-safe locking with timeout support
        self._lock = threading.RLock()  # Reentrant lock for nested calls
        self._buffer_creation_lock = threading.Lock()  # Separate lock for creation
        self._cleanup_registry = weakref.WeakValueDictionary()

        # Performance tracking with thread-safe access
        self._buffer_stats = {
            "created": 0,
            "reused": 0,
            "total_size_bytes": 0,
            "cleanup_count": 0,
            "lock_contentions": 0,
            "allocation_failures": 0,
        }
        self._stats_lock = threading.Lock()

    def create_shared_buffer(
        self, data: np.ndarray, name: Optional[str] = None, timeout: float = 5.0
    ) -> SharedBuffer:
        """
        Thread-safe creation of shared memory buffer from NumPy array with zero-copy.

        Args:
            data: NumPy array to share
            name: Optional buffer name for reuse
            timeout: Lock acquisition timeout in seconds

        Returns:
            SharedBuffer descriptor

        Raises:
            ValueError: If data is not a NumPy array
            TimeoutError: If lock acquisition times out
            MemoryError: If shared memory allocation fails
        """
        with trace_operation("create_shared_buffer"):
            if not isinstance(data, np.ndarray):
                raise ValueError("Data must be a NumPy array for zero-copy sharing")

            # Generate unique name if not provided
            if name is None:
                name = f"buffer_{id(data)}_{threading.get_ident()}_{time.time()}"

            # Thread-safe buffer existence check with timeout
            lock_acquired = self._lock.acquire(timeout=timeout)
            if not lock_acquired:
                with self._stats_lock:
                    self._buffer_stats["lock_contentions"] += 1
                raise TimeoutError(f"Failed to acquire lock within {timeout} seconds")

            try:
                if name in self._buffers:
                    self.logger.debug(f"Reusing existing shared buffer: {name}")
                    with self._stats_lock:
                        self._buffer_stats["reused"] += 1
                    return self._buffers[name]
            finally:
                self._lock.release()

            try:
                # Create shared memory buffer
                buffer_size = data.nbytes
                shm = shared_memory.SharedMemory(
                    create=True, size=buffer_size, name=name
                )

                # Copy data to shared memory with zero-copy when possible
                shared_array = np.ndarray(data.shape, dtype=data.dtype, buffer=shm.buf)
                shared_array[:] = (
                    data  # This is a memcpy, but unavoidable for initial setup
                )

                # Create buffer descriptor
                buffer = SharedBuffer(
                    name=name,
                    size=buffer_size,
                    dtype=data.dtype,
                    shape=data.shape,
                    shm=shm,
                )

                # Register for cleanup
                with self._lock:
                    self._buffers[name] = buffer
                    self._cleanup_registry[name] = buffer
                    self._buffer_stats["created"] += 1
                    self._buffer_stats["total_size_bytes"] += buffer_size

                self.logger.debug(
                    f"Created shared buffer: {name}, size: {buffer_size} bytes"
                )
                metrics_collector.increment_counter("shared_buffers_created")
                metrics_collector.record_histogram(
                    "shared_buffer_size_bytes", buffer_size
                )

                return buffer

            except Exception as e:
                self.logger.error(f"Failed to create shared buffer: {e}")
                raise

    def get_shared_buffer(self, name: str) -> Optional[SharedBuffer]:
        """Get existing shared buffer by name."""
        with self._lock:
            return self._buffers.get(name)

    def create_buffer_for_cpp(self, data: np.ndarray) -> Tuple[str, memoryview]:
        """
        Create shared buffer optimized for C++ kernel access.

        Returns:
            Tuple of (buffer_name, memoryview) for direct C++ access
        """
        with trace_operation("create_buffer_for_cpp"):
            buffer = self.create_shared_buffer(data)

            # Return memoryview for direct C++ buffer protocol access
            return buffer.name, buffer.get_memoryview()

    def expose_numpy_buffer_to_cpp(self, data: np.ndarray) -> memoryview:
        """
        Expose NumPy array buffer directly to C++ without copying.
        Uses buffer protocol for true zero-copy access.
        """
        if not isinstance(data, np.ndarray):
            raise ValueError("Data must be a NumPy array")

        # Ensure array is contiguous for C++ access
        if not data.flags.c_contiguous:
            self.logger.warning("Array not C-contiguous, creating contiguous copy")
            data = np.ascontiguousarray(data)

        # Return memoryview for zero-copy C++ access
        return memoryview(data)

    def optimize_for_process_pool(
        self, data: np.ndarray, threshold_mb: float = 10.0
    ) -> Union[np.ndarray, str]:
        """
        Optimize data for ProcessPoolExecutor serialization.
        Uses shared memory for large arrays to avoid pickle overhead.

        Args:
            data: NumPy array to optimize
            threshold_mb: Size threshold in MB for using shared memory

        Returns:
            Either original array (small) or shared buffer name (large)
        """
        if not isinstance(data, np.ndarray):
            return data

        size_mb = data.nbytes / (1024 * 1024)

        if size_mb > threshold_mb:
            # Use shared memory for large arrays
            buffer = self.create_shared_buffer(data)
            self.logger.debug(f"Using shared memory for {size_mb:.1f}MB array")
            return buffer.name
        else:
            # Keep small arrays as-is for normal serialization
            return data

    def get_array_from_buffer_name(self, buffer_name: str) -> Optional[np.ndarray]:
        """Get NumPy array from shared buffer name."""
        buffer = self.get_shared_buffer(buffer_name)
        if buffer:
            return buffer.get_array()
        return None

    def cleanup_buffer(self, name: str):
        """Clean up specific shared buffer."""
        with self._lock:
            buffer = self._buffers.pop(name, None)
            if buffer:
                buffer.cleanup()
                self._buffer_stats["cleanup_count"] += 1
                self.logger.debug(f"Cleaned up shared buffer: {name}")

    def cleanup_all_buffers(self):
        """Clean up all shared buffers."""
        with self._lock:
            buffer_names = list(self._buffers.keys())

        for name in buffer_names:
            self.cleanup_buffer(name)

        self.logger.info(f"Cleaned up {len(buffer_names)} shared buffers")

    def get_buffer_stats(self) -> Dict[str, Any]:
        """Get buffer usage statistics."""
        with self._lock:
            return {
                **self._buffer_stats,
                "active_buffers": len(self._buffers),
                "total_size_mb": self._buffer_stats["total_size_bytes"] / (1024 * 1024),
            }

    @contextmanager
    def temporary_shared_buffer(self, data: np.ndarray):
        """Context manager for temporary shared buffer."""
        buffer = self.create_shared_buffer(data)
        try:
            yield buffer
        finally:
            self.cleanup_buffer(buffer.name)


class ZeroCopyKernelInterface:
    """Interface for zero-copy C++ kernel calls."""

    def __init__(self, shared_memory_manager: SharedMemoryManager):
        self.shared_memory_manager = shared_memory_manager
        self.logger = get_structured_logger("ZeroCopyKernelInterface")

    def call_kernel_with_zero_copy(self, kernel_name: str, *args, **kwargs):
        """
        Call C++ kernel with zero-copy buffer passing.
        Automatically handles buffer conversion and cleanup.
        """
        if not CPP_KERNELS_AVAILABLE:
            raise RuntimeError("C++ kernels not available")

        with trace_operation(f"kernel_call_{kernel_name}"):
            # Convert NumPy arrays to memoryviews for zero-copy access
            processed_args = []
            buffer_names = []

            for arg in args:
                if isinstance(arg, np.ndarray):
                    # Use zero-copy memoryview
                    memview = self.shared_memory_manager.expose_numpy_buffer_to_cpp(arg)
                    processed_args.append(memview)
                    self.logger.debug(
                        f"Using zero-copy buffer for {arg.nbytes} byte array"
                    )
                else:
                    processed_args.append(arg)

            try:
                # Call C++ kernel with zero-copy buffers
                kernel_func = getattr(indicators, kernel_name)
                result = kernel_func(*processed_args, **kwargs)

                metrics_collector.increment_counter(
                    "cpp_kernel_calls", labels={"kernel": kernel_name}
                )
                return result

            except Exception as e:
                self.logger.error(f"C++ kernel call failed: {kernel_name}, error: {e}")
                metrics_collector.increment_counter(
                    "cpp_kernel_errors", labels={"kernel": kernel_name}
                )
                raise
            finally:
                # Cleanup any temporary buffers
                for buffer_name in buffer_names:
                    self.shared_memory_manager.cleanup_buffer(buffer_name)


# Global instances
shared_memory_manager = SharedMemoryManager()
zero_copy_kernel_interface = ZeroCopyKernelInterface(shared_memory_manager)


def cleanup_shared_memory():
    """Global cleanup function for shared memory."""
    shared_memory_manager.cleanup_all_buffers()


# Register cleanup on exit
import atexit

atexit.register(cleanup_shared_memory)

#!/usr/bin/env python3
"""
Minimal test to check if we can launch the platform
"""

import sys
import os

# Setup paths
debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
src_path = os.path.join(os.getcwd(), 'src')
sys.path.insert(0, debug_path)
sys.path.insert(0, src_path)

print("🧪 Minimal Platform Test")
print("=" * 30)

# Test 1: Check Python version
print(f"🐍 Python: {sys.version}")

# Test 2: Check indicators module
try:
    import indicators
    print("✅ Indicators module: OK")
except ImportError as e:
    print(f"❌ Indicators module: {e}")
    sys.exit(1)

# Test 3: Check PyQt6
try:
    from PyQt6.QtWidgets import QApplication
    print("✅ PyQt6: OK")
except ImportError as e:
    print(f"❌ PyQt6: {e}")
    print("Installing PyQt6...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6"])
    from PyQt6.QtWidgets import QApplication
    print("✅ PyQt6: Installed and OK")

# Test 4: Try to import main components
try:
    from main import TradingApplication
    print("✅ Main application: OK")
except ImportError as e:
    print(f"❌ Main application: {e}")
    print("Some dependencies may be missing. Trying to install...")
    
    # Install missing packages one by one
    packages = ["aiohttp", "structlog", "psutil", "pydantic", "rich"]
    for pkg in packages:
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", pkg, "--quiet"])
            print(f"✅ Installed {pkg}")
        except:
            print(f"⚠️  Failed to install {pkg}")
    
    # Try again
    try:
        from main import TradingApplication
        print("✅ Main application: OK after installing dependencies")
    except ImportError as e:
        print(f"❌ Main application still failing: {e}")
        sys.exit(1)

# Test 5: Try to create the application
print("\n🚀 Attempting to launch platform...")
try:
    app = TradingApplication()
    print("✅ Application created successfully!")
    print("🎯 Starting platform...")
    
    # Run the application
    exit_code = app.run()
    print(f"Platform exited with code: {exit_code}")
    
except Exception as e:
    print(f"❌ Failed to launch: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

cmake_minimum_required(VERSION 3.18)

# Set CMake policies
if(POLICY CMP0177)
    cmake_policy(SET CMP0177 NEW)  # install() DESTINATION paths are normalized
endif()

# Project configuration
project(DataDrivenTrading
    VERSION 1.0.0
    DESCRIPTION "Ultra-low-latency trading software with PyQt6 frontend and C++ compute kernels"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Build type configuration
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release CACHE STRING "Build type" FORCE)
endif()

# Build profiles
set(BUILD_PROFILES "Release;Debug;RelWithDebInfo;MinSizeRel;Sanitizer" CACHE STRING "Available build profiles")

# Platform detection
if(WIN32)
    set(PLATFORM_NAME "windows")
elseif(APPLE)
    set(PLATFORM_NAME "macos")
else()
    set(PLATFORM_NAME "linux")
endif()

# Architecture detection
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
    set(ARCH_NAME "x64")
else()
    set(ARCH_NAME "x86")
endif()

# Options
option(BUILD_PYTHON_BINDINGS "Build Python bindings" ON)
option(BUILD_BENCHMARKS "Build benchmark executables" ON)
option(BUILD_TESTS "Build unit tests" ON)
option(ENABLE_SIMD "Enable SIMD optimizations" ON)
option(ENABLE_OPENMP "Enable OpenMP parallelization" ON)
option(ENABLE_LTO "Enable Link Time Optimization" ON)
option(ENABLE_PROFILING "Enable profiling support" OFF)

# Find required packages
find_package(Python3 COMPONENTS Interpreter Development REQUIRED)

# Find optional packages
if(BUILD_PYTHON_BINDINGS)
    find_package(pybind11 QUIET)
    if(NOT pybind11_FOUND)
        # Download pybind11 if not found
        include(FetchContent)
        FetchContent_Declare(
            pybind11
            GIT_REPOSITORY https://github.com/pybind/pybind11.git
            GIT_TAG v2.11.1
        )
        FetchContent_MakeAvailable(pybind11)
    endif()
endif()

if(BUILD_BENCHMARKS)
    find_package(benchmark QUIET)
    if(NOT benchmark_FOUND)
        # Download Google Benchmark if not found
        include(FetchContent)
        FetchContent_Declare(
            benchmark
            GIT_REPOSITORY https://github.com/google/benchmark.git
            GIT_TAG v1.8.3
        )
        set(BENCHMARK_ENABLE_TESTING OFF CACHE BOOL "" FORCE)
        FetchContent_MakeAvailable(benchmark)
        set(benchmark_FOUND TRUE)  # Set this manually after FetchContent
    endif()
endif()

if(BUILD_TESTS)
    find_package(GTest QUIET)
    if(NOT GTest_FOUND)
        # Download GoogleTest if not found
        include(FetchContent)
        FetchContent_Declare(
            googletest
            GIT_REPOSITORY https://github.com/google/googletest.git
            GIT_TAG v1.14.0
        )
        set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
        FetchContent_MakeAvailable(googletest)
    endif()
endif()

# Compiler-specific flags
if(MSVC)
    # MSVC flags
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W4 /permissive-")
    
    # Release optimizations
    set(CMAKE_CXX_FLAGS_RELEASE "/O2 /Oi /Ot /GL /DNDEBUG")
    if(ENABLE_SIMD)
        set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /arch:AVX2")
    endif()
    
    # Debug flags
    set(CMAKE_CXX_FLAGS_DEBUG "/Od /Zi /DDEBUG /RTC1")
    
    # Link-time optimization
    if(ENABLE_LTO)
        set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} /LTCG")
        set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} /LTCG")
    endif()
    
else()
    # GCC/Clang flags
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -Wpedantic")
    
    # Release optimizations
    set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG -ffast-math -funroll-loops")
    if(ENABLE_SIMD)
        set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -march=native -mtune=native")
    endif()
    
    # Debug flags
    set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g3 -DDEBUG -fno-omit-frame-pointer")
    
    # Sanitizer build type
    set(CMAKE_CXX_FLAGS_SANITIZER "-O1 -g3 -DDEBUG -fsanitize=address -fsanitize=thread -fsanitize=undefined -fno-omit-frame-pointer")
    set(CMAKE_EXE_LINKER_FLAGS_SANITIZER "-fsanitize=address -fsanitize=thread -fsanitize=undefined")
    
    # Link-time optimization
    if(ENABLE_LTO)
        set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -flto")
        set(CMAKE_EXE_LINKER_FLAGS_RELEASE "${CMAKE_EXE_LINKER_FLAGS_RELEASE} -flto")
        set(CMAKE_SHARED_LINKER_FLAGS_RELEASE "${CMAKE_SHARED_LINKER_FLAGS_RELEASE} -flto")
    endif()
endif()

# OpenMP support
if(ENABLE_OPENMP)
    find_package(OpenMP)
    if(OpenMP_CXX_FOUND)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} ${OpenMP_CXX_FLAGS}")
        set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} ${OpenMP_EXE_LINKER_FLAGS}")
    endif()
endif()

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/src/compute)

# Source files
set(INDICATORS_SOURCES
    src/compute/indicators.cpp
)

set(INDICATORS_HEADERS
    # Add header files here when created
)

# Note: indicators_core removed to avoid duplicate compilation
# The Python bindings target (indicators) serves as the main library

# Python bindings
if(BUILD_PYTHON_BINDINGS)
    pybind11_add_module(indicators ${INDICATORS_SOURCES})
    
    # Set properties for Python module
    set_target_properties(indicators PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
    
    # Link libraries
    if(OpenMP_CXX_FOUND)
        target_link_libraries(indicators PRIVATE OpenMP::OpenMP_CXX)
        # MSVC requires special flag for OpenMP SIMD pragmas
        if(MSVC)
            target_compile_options(indicators PRIVATE /openmp:experimental)
        endif()
    endif()

    # Compiler-specific definitions
    target_compile_definitions(indicators PRIVATE VERSION_INFO=${PROJECT_VERSION})
endif()

# Benchmarks
if(BUILD_BENCHMARKS AND benchmark_FOUND)
    add_executable(indicators_benchmark src/compute/benchmark.cpp)
    target_link_libraries(indicators_benchmark PRIVATE benchmark::benchmark)
    
    set_target_properties(indicators_benchmark PROPERTIES
        CXX_STANDARD 17
        CXX_STANDARD_REQUIRED ON
    )
endif()

# Unit tests
if(BUILD_TESTS AND GTest_FOUND)
    enable_testing()
    
    # Create test executable if test file exists
    if(EXISTS "${CMAKE_CURRENT_SOURCE_DIR}/src/compute/test_indicators.cpp")
        add_executable(indicators_test src/compute/test_indicators.cpp)
        target_link_libraries(indicators_test PRIVATE GTest::gtest_main)
        
        set_target_properties(indicators_test PROPERTIES
            CXX_STANDARD 17
            CXX_STANDARD_REQUIRED ON
        )
        
        # Add test to CTest
        add_test(NAME IndicatorsUnitTests COMMAND indicators_test)
    endif()
endif()

# Installation for scikit-build-core
if(BUILD_PYTHON_BINDINGS)
    # Use SKBUILD for scikit-build-core compatibility
    if(DEFINED SKBUILD)
        # scikit-build-core handles the installation automatically
        install(TARGETS indicators
            COMPONENT Python
            DESTINATION .
        )

        # Install Python module files
        install(FILES
            ${CMAKE_CURRENT_SOURCE_DIR}/src/datadriven/__init__.py
            DESTINATION datadriven
            COMPONENT Python
        )
    else()
        # Traditional installation for manual builds
        if(NOT DEFINED Python3_SITEARCH OR Python3_SITEARCH STREQUAL "")
            execute_process(
                COMMAND ${Python3_EXECUTABLE} -c "import sysconfig; print(sysconfig.get_path('platlib'))"
                OUTPUT_VARIABLE Python3_SITEARCH
                OUTPUT_STRIP_TRAILING_WHITESPACE
            )
        endif()

        install(TARGETS indicators
            COMPONENT Python
            LIBRARY DESTINATION ${Python3_SITEARCH}/datadriven
            ARCHIVE DESTINATION ${Python3_SITEARCH}/datadriven
            RUNTIME DESTINATION ${Python3_SITEARCH}/datadriven
        )

        install(FILES
            ${CMAKE_CURRENT_SOURCE_DIR}/src/datadriven/__init__.py
            DESTINATION ${Python3_SITEARCH}/datadriven
            COMPONENT Python
        )
    endif()
endif()

# Note: Core library installation removed since we only build Python bindings

# Install headers
install(FILES ${INDICATORS_HEADERS}
    DESTINATION include/indicators
)

# Package configuration
set(CPACK_PACKAGE_NAME "DataDrivenTrading")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "DataDriven")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

# Platform-specific packaging
if(WIN32)
    set(CPACK_GENERATOR "ZIP;NSIS")
    set(CPACK_NSIS_DISPLAY_NAME "DataDriven Trading Platform")
elseif(APPLE)
    set(CPACK_GENERATOR "ZIP;DragNDrop")
else()
    set(CPACK_GENERATOR "TGZ;DEB;RPM")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER "DataDriven Team")
endif()

include(CPack)

# Print configuration summary
message(STATUS "")
message(STATUS "DataDriven Trading Platform Configuration Summary")
message(STATUS "================================================")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Platform: ${PLATFORM_NAME}-${ARCH_NAME}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
message(STATUS "Python: ${Python3_VERSION}")
message(STATUS "")
message(STATUS "Features:")
message(STATUS "  Python bindings: ${BUILD_PYTHON_BINDINGS}")
message(STATUS "  Benchmarks: ${BUILD_BENCHMARKS}")
message(STATUS "  Unit tests: ${BUILD_TESTS}")
message(STATUS "  SIMD optimizations: ${ENABLE_SIMD}")
message(STATUS "  OpenMP: ${ENABLE_OPENMP}")
message(STATUS "  Link-time optimization: ${ENABLE_LTO}")
message(STATUS "  Profiling: ${ENABLE_PROFILING}")
message(STATUS "")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "")

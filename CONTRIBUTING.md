# Contributing to DataDriven Trading Platform

Thank you for your interest in contributing to the DataDriven Trading Platform! This document provides comprehensive guidelines for contributors to ensure high-quality, maintainable code.

## 🚀 Quick Start for Contributors

### Prerequisites Checklist

Before you begin, ensure you have:
- [ ] Python 3.9+ installed
- [ ] Git configured with your name and email
- [ ] GitHub account with SSH keys set up
- [ ] C++ compiler (MSVC on Windows, GCC/Clang on Linux/macOS)
- [ ] CMake 3.18+ installed

### 5-Minute Contributor Setup

1. **Fork and Clone**
   ```bash
   # Fork the repository on GitHub first, then:
   <NAME_EMAIL>:YOUR-USERNAME/datadriven.git
   cd datadriven

   # Add upstream remote
   git remote <NAME_EMAIL>:your-org/datadriven.git
   ```

2. **Environment Setup**
   ```bash
   # Create virtual environment
   python -m venv venv

   # Activate (choose your platform)
   source venv/bin/activate          # Linux/macOS
   venv\Scripts\activate             # Windows CMD
   venv\Scripts\Activate.ps1         # Windows PowerShell

   # Install dependencies
   pip install -e ".[dev]"           # Install package with dev dependencies
   ```

3. **Development Tools Setup**
   ```bash
   # Install pre-commit hooks
   pre-commit install

   # Verify installation
   pre-commit run --all-files

   # Test the setup
   pytest tests/ -x --tb=short
   python main.py  # Should start successfully
   ```

4. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

You're ready to contribute! 🎉

## 📋 Development Workflow

### Step-by-Step Contribution Process

1. **Check Existing Issues**
   - Browse [open issues](https://github.com/your-org/datadriven/issues)
   - Comment on issues you'd like to work on
   - Wait for maintainer assignment before starting work

2. **Create Feature Branch**
   ```bash
   git fetch upstream
   git checkout main
   git merge upstream/main
   git checkout -b feature/descriptive-name
   ```

3. **Make Your Changes**
   - Follow our coding standards (see below)
   - Write tests for new functionality
   - Update documentation as needed
   - Commit frequently with clear messages

4. **Test Your Changes**
   ```bash
   # Run the full test suite
   pytest tests/ -v --cov=backend --cov=frontend

   # Run code quality checks
   pre-commit run --all-files

   # Test the application manually
   python main.py
   ```

5. **Submit Pull Request**
   ```bash
   git push origin feature/descriptive-name
   # Then create PR on GitHub
   ```

### Pull Request Checklist

Before submitting your PR, ensure:

- [ ] **Code Quality**
  - [ ] All tests pass (`pytest tests/ -v`)
  - [ ] Code coverage is maintained (`pytest --cov`)
  - [ ] Pre-commit hooks pass (`pre-commit run --all-files`)
  - [ ] No linting errors (`flake8 .`)
  - [ ] Type checking passes (`mypy backend/ frontend/`)

- [ ] **Documentation**
  - [ ] Docstrings added for new functions/classes
  - [ ] README updated if needed
  - [ ] CHANGELOG.md updated for user-facing changes
  - [ ] Type hints added for all new code

- [ ] **Testing**
  - [ ] Unit tests for new functionality
  - [ ] Integration tests for complex features
  - [ ] Performance tests for optimization changes
  - [ ] Manual testing completed

- [ ] **Git Hygiene**
  - [ ] Commits are atomic and well-described
  - [ ] Branch is up-to-date with main
  - [ ] No merge commits (use rebase)
  - [ ] Sensitive information not committed

- [ ] **Performance**
  - [ ] No performance regressions
  - [ ] Benchmarks run if applicable
  - [ ] Memory usage considered
  - [ ] C++ changes use SIMD when possible

## 📋 Coding Standards & Guidelines

### Python Code Standards

#### Code Formatting
```bash
# Automatic formatting (run before committing)
black .                    # Code formatting
isort .                    # Import sorting
```

**Configuration:**
- **Line length**: 88 characters (Black default)
- **String quotes**: Double quotes preferred
- **Import order**: stdlib, third-party, local (isort handles this)

#### Code Quality
```bash
# Linting and type checking
flake8 .                   # Style and error checking
mypy backend/ frontend/    # Type checking
bandit -r backend/         # Security scanning
```

**Requirements:**
- **Type hints**: Required for all public functions and methods
- **Docstrings**: Google-style for all public APIs
- **Error handling**: Explicit exception handling, no bare `except:`
- **Logging**: Use structured logging with correlation IDs

#### Python Code Example
```python
from typing import Dict, List, Optional, Union
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class IndicatorResult:
    """Result container for technical indicator calculations.

    Attributes:
        values: Calculated indicator values
        metadata: Additional calculation metadata
        errors: Any errors encountered during calculation
    """
    values: List[float]
    metadata: Dict[str, Union[str, int, float]]
    errors: Optional[List[str]] = None

def calculate_moving_average(
    data: List[float],
    window: int,
    *,
    min_periods: Optional[int] = None
) -> IndicatorResult:
    """Calculate simple moving average with validation.

    Args:
        data: Input price data
        window: Rolling window size for calculation
        min_periods: Minimum number of observations required

    Returns:
        IndicatorResult containing calculated values and metadata

    Raises:
        ValueError: If window size is invalid or data is insufficient
        TypeError: If data contains non-numeric values

    Example:
        >>> data = [1.0, 2.0, 3.0, 4.0, 5.0]
        >>> result = calculate_moving_average(data, window=3)
        >>> result.values
        [nan, nan, 2.0, 3.0, 4.0]
    """
    # Input validation
    if not isinstance(data, (list, tuple)):
        raise TypeError(f"Expected list or tuple, got {type(data)}")

    if window <= 0:
        raise ValueError(f"Window must be positive, got {window}")

    if len(data) < window:
        raise ValueError(f"Data length {len(data)} < window {window}")

    # Implementation with error handling
    try:
        values = []
        for i in range(len(data)):
            if i >= window - 1:
                window_data = data[i - window + 1:i + 1]
                avg = sum(window_data) / len(window_data)
                values.append(avg)
            else:
                values.append(float('nan'))

        metadata = {
            'window': window,
            'data_points': len(data),
            'valid_points': len([v for v in values if not math.isnan(v)])
        }

        logger.debug(f"Calculated moving average: {metadata}")
        return IndicatorResult(values=values, metadata=metadata)

    except Exception as e:
        logger.error(f"Moving average calculation failed: {e}")
        raise
```

### C++ Code Standards

#### Code Formatting
```bash
# Use clang-format with project configuration
clang-format -i compute/*.cpp compute/*.h
```

**Configuration:**
- **Standard**: C++17 minimum, C++20 features when beneficial
- **Style**: Google C++ Style Guide with modifications
- **Naming**: `snake_case` for functions/variables, `PascalCase` for classes
- **Headers**: `#pragma once` preferred over include guards

#### Performance Requirements
- **SIMD**: Use SIMD intrinsics for vectorizable operations
- **Memory**: Prefer stack allocation, use memory pools for large allocations
- **Algorithms**: Use STL algorithms with execution policies when possible
- **Exception Safety**: Strong exception safety guarantee

#### C++ Code Example
```cpp
#pragma once

#include <vector>
#include <memory>
#include <span>
#include <immintrin.h>  // For SIMD
#include "exception_handling.h"

namespace indicators {

/**
 * High-performance moving average calculator with SIMD optimization.
 *
 * This class provides enterprise-grade moving average calculations with:
 * - SIMD vectorization for maximum performance
 * - Memory pool allocation for large datasets
 * - Exception-safe error handling
 * - Zero-copy buffer access
 */
class MovingAverageCalculator {
public:
    /**
     * Calculate simple moving average with SIMD optimization.
     *
     * @param data Input price data (must be aligned for SIMD)
     * @param window Rolling window size
     * @return Result containing calculated values or error
     *
     * @throws InvalidParameterError if parameters are invalid
     * @throws MemoryAllocationError if allocation fails
     */
    static exception_handling::Result<std::vector<double>>
    calculate_simd(std::span<const double> data, int window);

private:
    static constexpr size_t SIMD_ALIGNMENT = 32;  // AVX2 alignment
    static constexpr size_t MIN_SIMD_SIZE = 8;    // Minimum for vectorization

    // Memory pool for large allocations
    static thread_local std::unique_ptr<MemoryPool> memory_pool_;

    // SIMD implementation for aligned data
    static void calculate_simd_impl(
        const double* __restrict__ input,
        double* __restrict__ output,
        size_t size,
        int window
    ) noexcept;
};

// Implementation
exception_handling::Result<std::vector<double>>
MovingAverageCalculator::calculate_simd(
    std::span<const double> data,
    int window
) {
    // Parameter validation
    exception_handling::validate_array_size(data.size());
    exception_handling::validate_window_size(window, static_cast<int>(data.size()));

    try {
        std::vector<double> result(data.size());

        // Use SIMD for large datasets
        if (data.size() >= MIN_SIMD_SIZE && window >= 4) {
            calculate_simd_impl(data.data(), result.data(), data.size(), window);
        } else {
            // Fallback to scalar implementation
            for (size_t i = 0; i < data.size(); ++i) {
                if (i >= static_cast<size_t>(window - 1)) {
                    double sum = 0.0;
                    for (int j = 0; j < window; ++j) {
                        sum += data[i - j];
                    }
                    result[i] = sum / window;
                } else {
                    result[i] = std::numeric_limits<double>::quiet_NaN();
                }
            }
        }

        return exception_handling::Result<std::vector<double>>(std::move(result));

    } catch (const std::exception& e) {
        exception_handling::ErrorLogger::instance().log_error(
            "MovingAverageCalculator::calculate_simd", e.what()
        );
        return exception_handling::Result<std::vector<double>>(e.what());
    }
}

void MovingAverageCalculator::calculate_simd_impl(
    const double* __restrict__ input,
    double* __restrict__ output,
    size_t size,
    int window
) noexcept {
    // SIMD implementation using AVX2
    const size_t simd_width = 4;  // 4 doubles per AVX2 register
    const size_t simd_end = size - (size % simd_width);

    for (size_t i = window - 1; i < simd_end; i += simd_width) {
        __m256d sum = _mm256_setzero_pd();

        // Calculate sum for window
        for (int j = 0; j < window; ++j) {
            __m256d values = _mm256_loadu_pd(&input[i - j]);
            sum = _mm256_add_pd(sum, values);
        }

        // Divide by window size
        __m256d window_size = _mm256_set1_pd(static_cast<double>(window));
        __m256d avg = _mm256_div_pd(sum, window_size);

        // Store result
        _mm256_storeu_pd(&output[i], avg);
    }

    // Handle remaining elements
    for (size_t i = simd_end; i < size; ++i) {
        if (i >= static_cast<size_t>(window - 1)) {
            double sum = 0.0;
            for (int j = 0; j < window; ++j) {
                sum += input[i - j];
            }
            output[i] = sum / window;
        } else {
            output[i] = std::numeric_limits<double>::quiet_NaN();
        }
    }
}

}  // namespace indicators
```

### Git Workflow

#### Branch Naming
- **Features**: `feature/description-of-feature`
- **Bug Fixes**: `fix/description-of-fix`
- **Documentation**: `docs/description-of-change`
- **Performance**: `perf/description-of-optimization`
- **Refactoring**: `refactor/description-of-refactor`

#### Commit Messages
Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `perf`: Performance improvements
- `test`: Adding or updating tests
- `ci`: CI/CD changes
- `chore`: Maintenance tasks

**Examples:**
```
feat(backend): add zero-copy shared memory manager

Implements SharedMemoryManager class for efficient data transfer
between Python and C++ without serialization overhead.

- Thread-safe buffer allocation and cleanup
- Automatic memory pool management
- Performance metrics collection

Closes #123
```

### Testing Requirements

#### Test Coverage
- **Minimum Coverage**: 80% for new code
- **Critical Paths**: 95% coverage for performance-critical code
- **Integration Tests**: Required for new features

#### Test Categories
```bash
# Unit tests (fast, isolated)
pytest tests/test_backend.py -m "not slow"

# Integration tests (slower, multiple components)
pytest tests/ -m integration

# GUI tests (require display)
pytest tests/test_frontend.py -m gui

# Performance tests (benchmarking)
pytest benchmarks/ -m performance
```

#### Writing Tests
- **Naming**: Test files should be `test_*.py`
- **Structure**: Use descriptive test class and method names
- **Fixtures**: Use pytest fixtures for common setup
- **Mocking**: Mock external dependencies (network, file system)
- **Assertions**: Use specific assertions with clear error messages

```python
class TestDataService:
    """Test suite for DataService functionality."""
    
    @pytest.mark.asyncio
    async def test_fetch_market_data_success(self, mock_yfinance):
        """Test successful market data fetching with mocked data."""
        service = DataService()
        await service.initialize()
        
        try:
            result = await service.fetch_market_data_with_rebasing(
                ticker="AAPL", timeframe="1d", dtl=1, length=50
            )
            
            assert "ohlc_data" in result
            assert len(result["ohlc_data"]) > 0
        finally:
            await service.shutdown()
```

### Performance Guidelines

#### Python Performance
- **Async/Await**: Use async patterns for I/O operations
- **NumPy**: Prefer NumPy operations over pure Python loops
- **Memory**: Avoid unnecessary data copies
- **Profiling**: Profile performance-critical code

#### C++ Performance
- **SIMD**: Use SIMD intrinsics for vectorizable operations
- **Memory**: Prefer stack allocation and memory pools
- **Algorithms**: Use STL algorithms and parallel execution
- **Benchmarking**: Benchmark all performance-critical functions

```cpp
// Good: SIMD-optimized loop
void process_data_simd(const float* input, float* output, size_t size) {
    const size_t simd_size = size - (size % 8);
    
    for (size_t i = 0; i < simd_size; i += 8) {
        __m256 data = _mm256_load_ps(&input[i]);
        __m256 result = _mm256_mul_ps(data, _mm256_set1_ps(2.0f));
        _mm256_store_ps(&output[i], result);
    }
    
    // Handle remaining elements
    for (size_t i = simd_size; i < size; ++i) {
        output[i] = input[i] * 2.0f;
    }
}
```

## 🧪 Testing Your Changes

### Before Submitting

1. **Run Full Test Suite**
   ```bash
   pytest tests/ -v --cov=backend --cov=frontend
   ```

2. **Check Code Quality**
   ```bash
   black --check .
   isort --check-only .
   flake8 .
   mypy backend/ frontend/
   ```

3. **Run Performance Tests**
   ```bash
   pytest benchmarks/ --benchmark-json=benchmark.json
   python scripts/check_performance_regression.py --current benchmark.json
   ```

4. **Build C++ Extensions**
   ```bash
   cd compute
   BUILD_PROFILE=release python setup.py build_ext --inplace
   cd ..
   ```

5. **Test GUI (if applicable)**
   ```bash
   pytest tests/test_frontend.py -m gui
   ```

### Continuous Integration

All pull requests are automatically tested with:
- **Multi-platform builds**: Windows, Linux, macOS
- **Multiple Python versions**: 3.9, 3.10, 3.11, 3.12
- **Code quality checks**: Linting, formatting, type checking
- **Security scanning**: Bandit, Safety, Trivy
- **Performance regression**: Automated benchmark comparison

## 📝 Documentation

### Code Documentation
- **Python**: Google-style docstrings with type hints
- **C++**: Doxygen-style comments
- **README**: Update README.md for significant changes
- **API Docs**: Update API documentation for public interfaces

### Documentation Build
```bash
cd docs
make html  # Build HTML documentation
make clean && make html  # Clean build
```

## 🐛 Bug Reports

### Before Reporting
1. **Search existing issues** to avoid duplicates
2. **Test with latest version** from main branch
3. **Minimal reproduction** case if possible

### Bug Report Template
```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Step one
2. Step two
3. Step three

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., Windows 10, Ubuntu 20.04]
- Python version: [e.g., 3.11.2]
- Package version: [e.g., 1.0.0]

**Additional Context**
Any other relevant information
```

## 💡 Feature Requests

### Feature Request Template
```markdown
**Feature Description**
Clear description of the proposed feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should this feature work?

**Alternatives Considered**
Other approaches you've considered

**Additional Context**
Any other relevant information
```

## 🔒 Security

### Reporting Security Issues
- **Do not** open public issues for security vulnerabilities
- **Email**: <EMAIL>
- **Include**: Detailed description and reproduction steps
- **Response**: We'll respond within 48 hours

### Security Guidelines
- **Dependencies**: Keep dependencies updated
- **Input Validation**: Validate all user inputs
- **Authentication**: Follow secure authentication practices
- **Logging**: Don't log sensitive information

## 📞 Getting Help

### Communication Channels
- **GitHub Discussions**: General questions and discussions
- **GitHub Issues**: Bug reports and feature requests
- **Email**: <EMAIL> for development questions

### Code Review Process
1. **Automated Checks**: All CI checks must pass
2. **Peer Review**: At least one maintainer review required
3. **Performance Review**: Performance-critical changes need benchmark review
4. **Documentation Review**: Documentation changes reviewed for clarity

## 🏆 Recognition

Contributors are recognized in:
- **CONTRIBUTORS.md**: All contributors listed
- **Release Notes**: Significant contributions highlighted
- **GitHub**: Contributor badges and statistics

Thank you for contributing to DataDriven Trading Platform! 🚀

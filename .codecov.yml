# Codecov configuration for DataDriven Trading Platform
# https://docs.codecov.com/docs/codecov-yaml

coverage:
  precision: 2
  round: down
  range: "70...100"
  
  status:
    project:
      default:
        target: 80%
        threshold: 1%
        base: auto
        branches:
          - main
          - develop
    patch:
      default:
        target: 75%
        threshold: 2%
        base: auto
    changes:
      default:
        target: auto
        threshold: 1%
        base: auto

  ignore:
    # Ignore test files
    - "tests/"
    - "benchmarks/"
    - "**/test_*.py"
    - "**/*_test.py"
    # Ignore setup and configuration files
    - "setup.py"
    - "setup_development.py"
    - "compile_*.py"
    - "scripts/"
    # Ignore generated files
    - "resources/*_rc.py"
    - "build/"
    - "dist/"
    - "*.egg-info/"

comment:
  layout: "reach,diff,flags,tree"
  behavior: default
  require_changes: false
  require_base: no
  require_head: yes

github_checks:
  annotations: true

flags:
  unittests:
    paths:
      - backend/
      - frontend/
      - compute/
  integration:
    paths:
      - tests/integration/
  backend:
    paths:
      - backend/
  frontend:
    paths:
      - frontend/
  compute:
    paths:
      - compute/

component_management:
  default_rules:
    statuses:
      - type: project
        target: 80%
      - type: patch
        target: 75%
  individual_components:
    - component_id: backend
      name: Backend Services
      paths:
        - backend/
      statuses:
        - type: project
          target: 85%
    - component_id: frontend
      name: Frontend GUI
      paths:
        - frontend/
      statuses:
        - type: project
          target: 75%
    - component_id: compute
      name: C++ Compute Kernels
      paths:
        - compute/
      statuses:
        - type: project
          target: 90%

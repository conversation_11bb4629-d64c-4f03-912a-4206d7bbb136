"""
Data Tab - Market Data Table Display

Implements enterprise-grade table view with zero-computation UI architecture.
Follows strict separation of concerns with UI layer only rendering pre-computed data.
All data processing is handled in C++ kernels or Python backend.
"""

# Standard library imports
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

# Third-party imports
import pandas as pd
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTableView,
    QLabel,
    QHeaderView,
    QAbstractItemView,
    QComboBox,
)
from PyQt6.QtCore import (
    Qt,
    QAbstractTableModel,
    QModelIndex,
    QVariant,
)
from PyQt6.QtGui import QFont, QColor

# Local imports - zero-computation architecture components
from src.backend.signals import signal_manager, TypedSlotDecorator
from src.backend.observability import get_structured_logger


@dataclass(frozen=True)
class ColumnDefinition:
    """Immutable column definition for type-safe table operations."""

    name: str
    data_type: type


class MarketDataTableModel(QAbstractTableModel):
    """
    Enterprise-grade table model for market data display.

    Implements zero-computation UI with all data pre-processed in backend.
    Follows strict separation of concerns with UI layer only rendering.
    Uses structured logging and enterprise-grade error handling.
    """

    # OHLC Mode column definitions with proper typing using dataclass
    # Note: Last two columns (bg_color, fg_color) are for internal use only
    OHLC_COLUMNS = [
        ColumnDefinition("Date", str),
        ColumnDefinition("Strength", str),
        ColumnDefinition("Open", float),
        ColumnDefinition("High", float),
        ColumnDefinition("Low", float),
        ColumnDefinition("Close", float),
        ColumnDefinition("Volume", int),
        ColumnDefinition("Peak/Trough", str),  # Peak/Trough identification
        ColumnDefinition("bg_color", str),  # Pre-computed background color
        ColumnDefinition("fg_color", str),  # Pre-computed foreground color
    ]

    # Projected OHLC Mode column definitions
    PROJECTED_OHLC_COLUMNS = [
        ColumnDefinition("Date", str),
        ColumnDefinition("Weekday", str),
        ColumnDefinition("Category", str),
        ColumnDefinition("Updated", str),
        ColumnDefinition("$Change High", float),
        ColumnDefinition("$Change Low", float),
        ColumnDefinition("%Change High", float),
        ColumnDefinition("%Change Low", float),
        ColumnDefinition("$Projected High Change", float),
        ColumnDefinition("$Projected Low Change", float),
        ColumnDefinition("Projected High", float),
        ColumnDefinition("Projected Low", float),
        ColumnDefinition("Open", float),  # Open ratio: next row open / current row open
        ColumnDefinition("Close", float),  # Close ratio: next row close / current row close
        ColumnDefinition("bg_color", str),  # Pre-computed background color
        ColumnDefinition("fg_color", str),  # Pre-computed foreground color
    ]

    # Visible columns (excluding internal color columns)
    OHLC_VISIBLE_COLUMNS = 8
    PROJECTED_OHLC_VISIBLE_COLUMNS = 14

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """Initialize table model with enterprise-grade logging."""
        super().__init__(parent)
        self.logger = get_structured_logger("MarketDataTableModel")
        self._data: List[List[Any]] = []
        self._mode: str = "OHLC Mode"  # Default mode
        # Initialize with OHLC mode columns
        self._update_columns_for_mode("OHLC Mode")

    def rowCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """Return number of rows in the model."""
        return len(self._data)

    def columnCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """Return number of columns in the model."""
        return len(self._headers)

    def data(
        self, index: QModelIndex, role: int = Qt.ItemDataRole.DisplayRole
    ) -> QVariant:
        """
        Return data for the given index and role.

        Implements enterprise-grade data formatting with proper type handling.
        All data is pre-computed in backend - UI only formats for display.
        """
        if not index.isValid() or index.row() >= len(self._data):
            return QVariant()

        row_data = self._data[index.row()]
        col_index = index.column()

        if role == Qt.ItemDataRole.DisplayRole:
            # Only show visible columns (exclude internal color columns)
            if col_index < self._visible_columns:
                # Projected OHLC Mode inserts an "Updated" column at index 3 without backend change
                if self._mode == "Projected OHLC Mode":
                    inserted_col_index = 3  # After Category
                    if col_index < inserted_col_index:
                        return str(row_data[col_index]) if col_index < len(row_data) else QVariant()
                    elif col_index == inserted_col_index:
                        # Show FWL Aggr info in Updated column
                        return self._get_fwl_aggr_display(index.row())
                    else:
                        mapped_index = col_index - 1
                        return str(row_data[mapped_index]) if mapped_index < len(row_data) else QVariant()
                # Default: direct mapping for OHLC Mode
                if col_index < len(row_data):
                    # ZERO-COMPUTATION UI: All formatting done in backend
                    # Simply return pre-formatted string from indicators.cpp
                    return str(row_data[col_index])
            return QVariant()

        elif role == Qt.ItemDataRole.TextAlignmentRole:
            # Center-align strength values and peak/trough, right-align numeric columns
            if col_index == 1:  # Category (Strength) column
                return (
                    Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter
                )
            elif col_index == 7:  # Peak/Trough column
                return (
                    Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter
                )
            elif col_index >= 2 and col_index <= 6:  # Open, High, Low, Close, Volume
                return (
                    Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter
                )
            return Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter

        elif role == Qt.ItemDataRole.BackgroundRole:
            # ZERO-COMPUTATION UI: Background colors pre-computed in indicators.cpp
            # Extract pre-computed background color from data
            row_index = index.row()
            if col_index >= 0 and row_index < len(self._data):
                row_data = self._data[row_index]
                # Background color is always at second-to-last position in the row data
                bg_color_index = len(row_data) - 2  # bg_color column index
                if bg_color_index >= 0 and len(row_data) > bg_color_index:
                    bg_color = row_data[bg_color_index]
                    if bg_color:
                        return QColor(bg_color)

        elif role == Qt.ItemDataRole.ForegroundRole:
            # ZERO-COMPUTATION UI: Foreground colors pre-computed in indicators.cpp
            # Extract pre-computed foreground color from data
            row_index = index.row()
            if row_index < len(self._data):
                row_data = self._data[row_index]
                # Foreground color is always at last position in the row data
                fg_color_index = len(row_data) - 1  # fg_color column index
                if fg_color_index >= 0 and len(row_data) > fg_color_index:
                    fg_color = row_data[fg_color_index]
                    if fg_color:
                        return QColor(fg_color)
                    else:
                        # Default to white text for dark theme if no color specified
                        return QColor("#FFFFFF")
            return QVariant()

        return QVariant()

    def headerData(
        self,
        section: int,
        orientation: Qt.Orientation,
        role: int = Qt.ItemDataRole.DisplayRole,
    ) -> QVariant:
        """
        Return header data for the given section and role.

        Implements enterprise-grade header formatting with proper fonts.
        """
        if role == Qt.ItemDataRole.DisplayRole:
            if (
                orientation == Qt.Orientation.Horizontal
                and section < len(self._headers)
            ):
                return self._headers[section]
            elif orientation == Qt.Orientation.Vertical:
                return str(section + 1)
        elif role == Qt.ItemDataRole.FontRole:
            font = QFont("Segoe UI", 9)
            font.setBold(True)
            return font
        return QVariant()

    def update_data(self, market_data: Dict[str, Any]) -> None:
        """
        Update table data from market data dictionary.

        ZERO-COMPUTATION UI: All data processing is pre-computed in backend.
        UI only renders pre-formatted data from C++ kernels.
        """
        try:
            self.logger.info("Updating table data from market data")

            # Store market data for FWL Aggr access
            self._market_data = market_data

            # Store current market data for mode switching
            self._current_market_data = market_data

            # Begin model reset for efficient bulk update
            self.beginResetModel()

            # Clear existing data
            self._data.clear()

            # Extract PRE-COMPUTED table data from backend based on current mode
            # All calculations, formatting, and processing done in indicators.cpp
            if self._mode == "Projected OHLC Mode":
                # Use projected OHLC data from backend
                table_rows = market_data.get("projected_ohlc_table_rows", [])
                data_type = "projected OHLC table rows"
            else:
                # Use regular OHLC data from backend
                table_rows = market_data.get("table_rows", [])
                data_type = "table rows"

            if not table_rows:
                self.logger.warning(f"No pre-computed {data_type} received from backend")
                self.endResetModel()
                return

            # Simply copy pre-computed rows - NO CALCULATIONS IN UI
            self._data = table_rows.copy()

            # End model reset
            self.endResetModel()

            self.logger.info(f"Table updated with {len(self._data)} {data_type}")

        except Exception as e:
            self.logger.error(f"Error updating table data: {e}")
            self.endResetModel()  # Ensure we end the reset even on error

    def set_mode(self, mode: str) -> None:
        """
        Set the table mode and update column structure.
        Triggers data reload to use the correct data source.

        Args:
            mode: "OHLC Mode" or "Projected OHLC Mode"
        """
        try:
            if mode != self._mode:
                self.logger.info(f"Changing table mode from {self._mode} to {mode}")

                self._mode = mode
                self._update_columns_for_mode(mode)

                # Reset model to update headers and clear data
                self.beginResetModel()
                self._data.clear()  # Clear data to force reload with new mode
                self.endResetModel()

                # Store reference to current market data for reload
                if hasattr(self, '_current_market_data') and self._current_market_data:
                    # Reload data with new mode
                    self.update_data(self._current_market_data)

        except Exception as e:
            self.logger.error(f"Error setting table mode: {e}")

    def _update_columns_for_mode(self, mode: str) -> None:
        """
        Update column definitions based on the selected mode.

        Args:
            mode: "OHLC Mode" or "Projected OHLC Mode"
        """
        if mode == "OHLC Mode":
            self._columns = self.OHLC_COLUMNS
            self._visible_columns = self.OHLC_VISIBLE_COLUMNS
        elif mode == "Projected OHLC Mode":
            self._columns = self.PROJECTED_OHLC_COLUMNS
            self._visible_columns = self.PROJECTED_OHLC_VISIBLE_COLUMNS
        else:
            self.logger.warning(f"Unknown mode: {mode}, defaulting to OHLC Mode")
            self._columns = self.OHLC_COLUMNS
            self._visible_columns = self.OHLC_VISIBLE_COLUMNS

        # Update headers (exclude internal color columns)
        self._headers = [col.name for col in self._columns[:self._visible_columns]]
        self.logger.debug(f"Updated headers for {mode}: {self._headers}")

    # REMOVED: _create_strength_mapping_from_crosshair method
    # All strength mapping calculations moved to indicators.cpp
    # UI only renders pre-computed data from backend

    def _get_fwl_aggr_display(self, row_index):
        """Get FWL Aggr display text for Updated column."""
        try:
            # Get FWL Aggr value from stored market data
            if hasattr(self, '_market_data') and self._market_data:
                fwl_aggr = self._market_data.get('fwl_aggr_value', 1)
                if fwl_aggr > 1:
                    # Try to get specific PH/PL values for this row from metadata
                    fwl_metadata = self._market_data.get('fwl_aggr_metadata', {})
                    if str(row_index) in fwl_metadata:
                        row_data = fwl_metadata[str(row_index)]
                        ph = row_data.get('ph', fwl_aggr)
                        pl = row_data.get('pl', fwl_aggr)
                        return f"PH:{ph}|PL:{pl}"
                    elif row_index in fwl_metadata:
                        row_data = fwl_metadata[row_index]
                        ph = row_data.get('ph', fwl_aggr)
                        pl = row_data.get('pl', fwl_aggr)
                        return f"PH:{ph}|PL:{pl}"
                    else:
                        # Fallback to default FWL Aggr value
                        return f"PH:{fwl_aggr}|PL:{fwl_aggr}"
            return ""
        except Exception as e:
            print(f"Error getting FWL Aggr display: {e}")
            return ""


class DataTab(QWidget):
    """
    Data tab with enterprise-grade market data table.

    Implements zero-computation UI architecture with proper Model/View separation.
    Follows strict separation of concerns with UI layer only rendering
    pre-computed data from backend. Uses structured logging and enterprise-grade
    error handling throughout.
    """

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """
        Initialize DataTab with enterprise-grade architecture.

        Args:
            parent: Optional parent widget for Qt hierarchy
        """
        super().__init__(parent)
        self.logger = get_structured_logger("DataTab")
        self.signals = signal_manager

        # Initialize table model with enterprise-grade patterns
        self.table_model = MarketDataTableModel(self)

        # Mode selector for different data views
        self.mode_selector: Optional[QComboBox] = None
        self.current_mode: str = "OHLC Mode"
        self.status_label: Optional[QLabel] = None
        self.table_view: Optional[QTableView] = None

        # Store current market data for mode switching
        self._current_market_data: Optional[Dict[str, Any]] = None

        self.setup_ui()
        self.setup_signals()

    def setup_ui(self) -> None:
        """
        Initialize the user interface with enterprise-grade table view.

        Implements proper Qt layout management and styling following
        enterprise-grade UI patterns with dark theme support.
        """
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header section
        header_layout = QHBoxLayout()

        title_label = QLabel("Market Data Table")
        title_font = QFont("Segoe UI", 16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 5px;")

        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Mode selector on the far right
        mode_label = QLabel("Mode:")
        mode_label.setStyleSheet("color: #ffffff; margin-right: 5px;")

        self.mode_selector = QComboBox()
        self.mode_selector.addItems([
            "OHLC Mode",
            "Projected OHLC Mode"
        ])
        self.mode_selector.setCurrentText(self.current_mode)
        self.mode_selector.setMinimumWidth(150)
        self.mode_selector.currentTextChanged.connect(self._on_mode_changed)

        header_layout.addWidget(mode_label)
        header_layout.addWidget(self.mode_selector)

        layout.addLayout(header_layout)

        # Table view with enterprise-grade configuration
        self.table_view = QTableView()
        self.table_view.setModel(self.table_model)

        # Configure table appearance and behavior
        self.setup_table_view()

        layout.addWidget(self.table_view)

        # Status section
        status_layout = QHBoxLayout()

        self.status_label = QLabel("No data loaded")
        status_font = QFont("Segoe UI", 9)
        self.status_label.setFont(status_font)
        self.status_label.setStyleSheet("color: #cccccc;")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        layout.addLayout(status_layout)

    def setup_table_view(self) -> None:
        """
        Configure table view with enterprise-grade settings.

        Implements proper Qt table configuration with optimal performance
        settings, dark theme styling, and responsive column management.
        """
        # Selection behavior
        self.table_view.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_view.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)

        # Appearance
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setShowGrid(True)
        self.table_view.setGridStyle(Qt.PenStyle.SolidLine)

        # Header configuration for evenly spread columns
        horizontal_header = self.table_view.horizontalHeader()
        horizontal_header.setStretchLastSection(True)

        # Set all columns to stretch evenly across the available width
        for i in range(len(self.table_model._headers)):
            horizontal_header.setSectionResizeMode(i, QHeaderView.ResizeMode.Stretch)

        # Vertical header
        vertical_header = self.table_view.verticalHeader()
        vertical_header.setVisible(True)
        vertical_header.setDefaultSectionSize(24)

        # Styling with dark theme
        self.table_view.setStyleSheet("""
            QTableView {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                gridline-color: #555555;
                selection-background-color: #404040;
                alternate-background-color: #333333;
            }
            QTableView::item {
                padding: 4px;
                border: none;
            }
            QTableView::item:selected {
                background-color: #404040;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                padding: 4px;
                font-weight: bold;
            }
            QHeaderView::section:horizontal {
                border-top: none;
            }
            QHeaderView::section:vertical {
                border-left: none;
            }
        """)

    def setup_signals(self) -> None:
        """
        Setup enterprise-grade signal connections.

        Note: Signal connections are now handled centrally by the main window
        in _setup_tab_connections() to avoid double connections and ensure
        proper centralized data management.
        """
        # Signal connections are handled by main window's centralized system
        # This prevents double connections and ensures proper data flow
        self.logger.info("Signal connections will be established by main window centralized system")

    @TypedSlotDecorator.typed_slot(dict)
    def on_market_data_updated(self, market_data: Dict[str, Any]) -> None:
        """
        Handle market data updates with enterprise-grade error handling.

        Uses well-typed slot decorator for type safety and implements
        comprehensive error handling with structured logging.
        All data is pre-computed in backend - UI only renders.

        Args:
            market_data: Pre-computed market data from backend
        """
        try:
            self.logger.info("Received market data update for table")

            # Update table model with new data
            self.table_model.update_data(market_data)

            # Update status
            row_count = self.table_model.rowCount()
            ticker = market_data.get("ticker", "Unknown")
            timeframe = market_data.get("timeframe", "Unknown")

            self.status_label.setText(
                f"Loaded {row_count} data points for {ticker} ({timeframe})"
            )

            # Columns are already set to stretch evenly, no need to resize

            # Scroll to bottom to show latest data
            if row_count > 0:
                last_index = self.table_model.index(row_count - 1, 0)
                self.table_view.scrollTo(
                    last_index, QAbstractItemView.ScrollHint.PositionAtBottom
                )

            self.logger.info(f"Table updated successfully with {row_count} rows")

        except Exception as e:
            error_msg = f"Error updating table with market data: {str(e)}"
            self.logger.error(error_msg)
            self.status_label.setText("Error loading data")

    def clear_data(self) -> None:
        """
        Clear all table data with enterprise-grade error handling.

        Implements proper Qt model reset patterns for optimal performance
        and maintains UI consistency during data clearing operations.
        """
        try:
            self.table_model.beginResetModel()
            self.table_model._data.clear()
            self.table_model.endResetModel()

            self.status_label.setText("No data loaded")
            self.logger.info("Table data cleared")

        except Exception as e:
            self.logger.error(f"Error clearing table data: {e}")

    def export_data(self) -> Optional[pd.DataFrame]:
        """
        Export current table data as pandas DataFrame.
        Returns None if no data is available.
        """
        try:
            if not self.table_model._data:
                self.logger.warning("No data available for export")
                return None

            # Convert table data to DataFrame
            df = pd.DataFrame(
                self.table_model._data,
                columns=self.table_model._headers
            )

            self.logger.info(f"Exported {len(df)} rows to DataFrame")
            return df

        except Exception as e:
            self.logger.error(f"Error exporting table data: {e}")
            return None

    def _on_mode_changed(self, mode: str) -> None:
        """
        Handle mode change for data display.

        Args:
            mode: Selected mode ("OHLC Mode" or "Projected OHLC Mode")
        """
        try:
            self.logger.info(f"Data table mode changed to: {mode}")
            self.current_mode = mode

            # Update table model to use new column structure
            self.table_model.set_mode(mode)

            # Reconfigure table view for new columns
            self.setup_table_view()

            # Update status to reflect current mode
            if mode == "Projected OHLC Mode":
                if len(self.table_model._data) > 0:
                    self.status_label.setText(f"Projected OHLC Mode - {len(self.table_model._data)} rows (calculations not implemented)")
                else:
                    self.status_label.setText("Projected OHLC Mode - No data loaded")
            else:
                if len(self.table_model._data) > 0:
                    self.status_label.setText(f"OHLC Mode - {len(self.table_model._data)} rows loaded")
                else:
                    self.status_label.setText("No data loaded")

        except Exception as e:
            self.logger.error(f"Error changing data table mode: {e}")

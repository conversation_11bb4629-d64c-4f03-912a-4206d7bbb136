/**
 * Enterprise-grade C++ exception handling for Python bindings.
 * Provides safe exception translation across language boundaries.
 */

#pragma once

#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/numpy.h>
#include <exception>
#include <stdexcept>
#include <string>
#include <memory>
#include <mutex>
#include <atomic>

namespace py = pybind11;

namespace exception_handling {

/**
 * Custom exception types for better error categorization
 */
class ComputationError : public std::runtime_error {
public:
    explicit ComputationError(const std::string& message) 
        : std::runtime_error("Computation Error: " + message) {}
};

class InvalidParameterError : public std::invalid_argument {
public:
    explicit InvalidParameterError(const std::string& message)
        : std::invalid_argument("Invalid Parameter: " + message) {}
};

class MemoryAllocationError : public std::bad_alloc {
public:
    explicit MemoryAllocationError(const std::string& message)
        : std::bad_alloc(), message_(message) {}
    
    const char* what() const noexcept override {
        return message_.c_str();
    }
    
private:
    std::string message_;
};

class ThreadSafetyError : public std::runtime_error {
public:
    explicit ThreadSafetyError(const std::string& message)
        : std::runtime_error("Thread Safety Error: " + message) {}
};

/**
 * Result wrapper for safe error handling
 */
template<typename T>
class Result {
public:
    // Default constructor for error case
    Result() : success_(false) {}

    // Success constructor
    explicit Result(T&& value)
        : success_(true), value_(std::forward<T>(value)) {}

    // Copy constructor for success case
    explicit Result(const T& value)
        : success_(true), value_(value) {}

    // Error constructor
    explicit Result(const std::string& error_message)
        : success_(false), error_message_(error_message) {}
    
    // Check if result is successful
    bool is_success() const { return success_; }
    bool is_error() const { return !success_; }
    
    // Get value (throws if error)
    const T& value() const {
        if (!success_) {
            throw ComputationError("Attempted to access value of failed result: " + error_message_);
        }
        return value_;
    }
    
    // Get value with move semantics
    T&& move_value() {
        if (!success_) {
            throw ComputationError("Attempted to access value of failed result: " + error_message_);
        }
        return std::move(value_);
    }
    
    // Get error message
    const std::string& error_message() const { return error_message_; }
    
    // Convert to Python dict for safe transport
    py::dict to_python_dict() const {
        py::dict result;
        result["success"] = success_;
        
        if (success_) {
            result["data"] = value_;
        } else {
            result["error_message"] = error_message_;
        }
        
        return result;
    }

private:
    bool success_ = false;
    T value_{};  // Default initialize
    std::string error_message_;
};

/**
 * Exception-safe function wrapper
 */
template<typename Func, typename... Args>
auto safe_call(Func&& func, Args&&... args) -> Result<decltype(func(args...))> {
    try {
        auto result = func(std::forward<Args>(args)...);
        return Result<decltype(result)>(std::move(result));
    }
    catch (const InvalidParameterError& e) {
        return Result<decltype(func(args...))>(std::string("Invalid Parameter: ") + e.what());
    }
    catch (const ComputationError& e) {
        return Result<decltype(func(args...))>(std::string("Computation Error: ") + e.what());
    }
    catch (const MemoryAllocationError& e) {
        return Result<decltype(func(args...))>(std::string("Memory Allocation Error: ") + e.what());
    }
    catch (const ThreadSafetyError& e) {
        return Result<decltype(func(args...))>(std::string("Thread Safety Error: ") + e.what());
    }
    catch (const std::bad_alloc& e) {
        return Result<decltype(func(args...))>("Memory allocation failed");
    }
    catch (const std::out_of_range& e) {
        return Result<decltype(func(args...))>(std::string("Out of range error: ") + e.what());
    }
    catch (const std::runtime_error& e) {
        return Result<decltype(func(args...))>(std::string("Runtime error: ") + e.what());
    }
    catch (const std::exception& e) {
        return Result<decltype(func(args...))>(std::string("Standard exception: ") + e.what());
    }
    catch (...) {
        return Result<decltype(func(args...))>("Unknown exception occurred");
    }
}

/**
 * Thread-safe error logging
 */
class ErrorLogger {
public:
    static ErrorLogger& instance() {
        static ErrorLogger instance_;
        return instance_;
    }
    
    void log_error(const std::string& function_name, const std::string& error_message) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // Increment error count
        error_count_.fetch_add(1, std::memory_order_relaxed);
        
        // Log to stderr (could be enhanced to use proper logging)
        fprintf(stderr, "[ERROR] %s: %s\n", function_name.c_str(), error_message.c_str());
    }
    
    std::size_t get_error_count() const {
        return error_count_.load(std::memory_order_relaxed);
    }
    
    void reset_error_count() {
        error_count_.store(0, std::memory_order_relaxed);
    }

private:
    ErrorLogger() = default;
    std::mutex mutex_;
    std::atomic<std::size_t> error_count_{0};
};

/**
 * RAII guard for exception-safe resource management
 */
template<typename Resource, typename Deleter>
class ResourceGuard {
public:
    ResourceGuard(Resource* resource, Deleter deleter)
        : resource_(resource), deleter_(deleter) {}
    
    ~ResourceGuard() {
        if (resource_) {
            try {
                deleter_(resource_);
            } catch (...) {
                // Swallow exceptions in destructor
                ErrorLogger::instance().log_error("ResourceGuard", "Exception in destructor");
            }
        }
    }
    
    // Non-copyable
    ResourceGuard(const ResourceGuard&) = delete;
    ResourceGuard& operator=(const ResourceGuard&) = delete;
    
    // Movable
    ResourceGuard(ResourceGuard&& other) noexcept
        : resource_(other.resource_), deleter_(std::move(other.deleter_)) {
        other.resource_ = nullptr;
    }
    
    ResourceGuard& operator=(ResourceGuard&& other) noexcept {
        if (this != &other) {
            if (resource_) {
                deleter_(resource_);
            }
            resource_ = other.resource_;
            deleter_ = std::move(other.deleter_);
            other.resource_ = nullptr;
        }
        return *this;
    }
    
    Resource* get() const { return resource_; }
    Resource* release() {
        Resource* temp = resource_;
        resource_ = nullptr;
        return temp;
    }

private:
    Resource* resource_;
    Deleter deleter_;
};

/**
 * Helper macros for safe function wrapping
 */
#define SAFE_CALL_WITH_RESULT(func, ...) \
    exception_handling::safe_call([&]() { return func(__VA_ARGS__); })

#define SAFE_CALL_VOID(func, ...) \
    exception_handling::safe_call([&]() -> bool { \
        func(__VA_ARGS__); \
        return true; \
    })

#define LOG_AND_RETHROW(func_name, exception_type) \
    catch (const exception_type& e) { \
        exception_handling::ErrorLogger::instance().log_error(func_name, e.what()); \
        throw; \
    }

/**
 * Parameter validation helpers
 */
inline void validate_array_size(size_t size, size_t min_size = 1, size_t max_size = std::numeric_limits<size_t>::max()) {
    if (size < min_size) {
        throw InvalidParameterError("Array size " + std::to_string(size) +
                                  " is less than minimum required " + std::to_string(min_size));
    }
    if (size > max_size) {
        throw InvalidParameterError("Array size " + std::to_string(size) +
                                  " exceeds maximum safe size " + std::to_string(max_size) +
                                  " to prevent memory exhaustion");
    }
}

inline void validate_window_size(int window, int max_window) {
    if (window <= 0) {
        throw InvalidParameterError("Window size must be positive, got " + std::to_string(window));
    }
    if (window > max_window) {
        throw InvalidParameterError("Window size " + std::to_string(window) + 
                                  " exceeds maximum " + std::to_string(max_window));
    }
}

inline void validate_pointer(const void* ptr, const std::string& name) {
    if (!ptr) {
        throw InvalidParameterError("Null pointer provided for " + name);
    }
}

} // namespace exception_handling

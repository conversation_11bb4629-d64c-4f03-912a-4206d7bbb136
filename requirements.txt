# Core dependencies
PyQt6
pyqtgraph
yfinance
numpy
pandas
pybind11

# Testing frameworks
pytest
pytest-cov
pytest-asyncio
pytest-qt
pytest-mock
pytest-benchmark
pytest-html
pytest-json-report
pytest-xdist

# Code quality tools
black
isort
flake8
mypy
pylint
bandit
safety
pre-commit

# Documentation tools
sphinx
sphinx-rtd-theme
sphinx-autodoc-typehints
myst-parser

# Build tools
build
wheel
setuptools
cibuildwheel
twine

# Protocol Buffers
protobuf
grpcio-tools

# Performance profiling
py-spy
memory-profiler
line-profiler
scalene

# Monitoring and observability
prometheus-client
aiohttp
psutil

# Development utilities
ipython
jupyter
notebook
ipdb
rich
click

# Type checking stubs
types-requests
types-PyYAML
types-setuptools

# Linting plugins
flake8-docstrings
flake8-import-order
flake8-bugbear
flake8-comprehensions
flake8-simplify

# Security scanning
semgrep
pip-audit

# Git hooks and automation
gitpython
commitizen

# Cross-platform compatibility
colorama; sys_platform == "win32"

# Optional: GUI testing on Linux
xvfb-run; sys_platform == "linux"

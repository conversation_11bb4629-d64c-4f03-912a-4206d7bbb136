"""
Production-grade Prometheus metrics server for enterprise observability.
Exposes comprehensive application metrics for monitoring and alerting.
"""

import asyncio
import time
import psutil
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from aiohttp import web, ClientSession
import json
import os
import logging

from .observability import metrics_collector, get_structured_logger


@dataclass
class MetricDefinition:
    """Definition of a Prometheus metric."""

    name: str
    metric_type: str  # counter, gauge, histogram, summary
    help_text: str
    labels: List[str] = None

    def __post_init__(self):
        if self.labels is None:
            self.labels = []


class PrometheusMetricsExporter:
    """
    Enterprise-grade Prometheus metrics exporter.
    Converts internal metrics to Prometheus format with proper labeling.
    """

    def __init__(self):
        self.logger = get_structured_logger("PrometheusMetricsExporter")
        self._system_metrics_enabled = True
        self._custom_metrics: Dict[str, MetricDefinition] = {}

        # Register standard application metrics
        self._register_standard_metrics()

    def _register_standard_metrics(self):
        """Register standard application metrics."""
        standard_metrics = [
            MetricDefinition(
                name="datadriven_requests_total",
                metric_type="counter",
                help_text="Total number of requests processed",
                labels=["operation", "status"],
            ),
            MetricDefinition(
                name="datadriven_request_duration_seconds",
                metric_type="histogram",
                help_text="Request duration in seconds",
                labels=["operation"],
            ),
            MetricDefinition(
                name="datadriven_errors_total",
                metric_type="counter",
                help_text="Total number of errors",
                labels=["operation", "error_type"],
            ),
            MetricDefinition(
                name="datadriven_shared_buffers_active",
                metric_type="gauge",
                help_text="Number of active shared memory buffers",
            ),
            MetricDefinition(
                name="datadriven_shared_buffers_total_size_bytes",
                metric_type="gauge",
                help_text="Total size of shared memory buffers in bytes",
            ),
            MetricDefinition(
                name="datadriven_memory_usage_bytes",
                metric_type="gauge",
                help_text="Application memory usage in bytes",
            ),
            MetricDefinition(
                name="datadriven_cpu_usage_percent",
                metric_type="gauge",
                help_text="Application CPU usage percentage",
            ),
            MetricDefinition(
                name="datadriven_uptime_seconds",
                metric_type="gauge",
                help_text="Application uptime in seconds",
            ),
            MetricDefinition(
                name="datadriven_active_connections",
                metric_type="gauge",
                help_text="Number of active connections",
            ),
            MetricDefinition(
                name="datadriven_thread_count",
                metric_type="gauge",
                help_text="Number of active threads",
            ),
        ]

        for metric in standard_metrics:
            self._custom_metrics[metric.name] = metric

    def register_custom_metric(self, metric: MetricDefinition):
        """Register a custom metric definition."""
        self._custom_metrics[metric.name] = metric
        self.logger.info(f"Registered custom metric: {metric.name}")

    def _format_metric_name(self, name: str, labels: Dict[str, str] = None) -> str:
        """Format metric name with labels for Prometheus."""
        if not labels:
            return name

        label_str = ",".join(f'{k}="{v}"' for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"

    def _get_system_metrics(self) -> Dict[str, float]:
        """Get system-level metrics."""
        try:
            process = psutil.Process()

            return {
                "datadriven_memory_usage_bytes": process.memory_info().rss,
                "datadriven_cpu_usage_percent": process.cpu_percent(),
                "datadriven_thread_count": process.num_threads(),
                "datadriven_uptime_seconds": time.time() - process.create_time(),
            }
        except Exception as e:
            self.logger.error(f"Failed to get system metrics: {e}")
            return {}

    def _get_shared_memory_metrics(self) -> Dict[str, float]:
        """Get shared memory metrics."""
        try:
            from .shared_memory_manager import shared_memory_manager

            stats = shared_memory_manager.get_buffer_stats()

            return {
                "datadriven_shared_buffers_active": stats.get("active_buffers", 0),
                "datadriven_shared_buffers_total_size_bytes": stats.get(
                    "total_size_bytes", 0
                ),
            }
        except Exception as e:
            self.logger.error(f"Failed to get shared memory metrics: {e}")
            return {}

    def export_metrics(self) -> str:
        """Export all metrics in Prometheus format."""
        lines = []

        # Add metric definitions (HELP and TYPE)
        for metric_name, metric_def in self._custom_metrics.items():
            lines.append(f"# HELP {metric_name} {metric_def.help_text}")
            lines.append(f"# TYPE {metric_name} {metric_def.metric_type}")

        # Get application metrics
        app_metrics = metrics_collector.get_metrics_summary()

        # Export counters
        for name, value in app_metrics.get("counters", {}).items():
            # Parse labels from metric name
            if "{" in name and "}" in name:
                base_name = name.split("{")[0]
                prometheus_name = f"datadriven_{base_name}"
                lines.append(f"{name.replace(base_name, prometheus_name)} {value}")
            else:
                lines.append(f"datadriven_{name} {value}")

        # Export gauges
        for name, value in app_metrics.get("gauges", {}).items():
            lines.append(f"datadriven_{name} {value}")

        # Export request metrics as histograms
        for operation, metrics in app_metrics.get("request_metrics", {}).items():
            base_name = f"datadriven_request_duration_seconds"

            # Add histogram buckets (simplified)
            buckets = [
                0.001,
                0.005,
                0.01,
                0.025,
                0.05,
                0.1,
                0.25,
                0.5,
                1.0,
                2.5,
                5.0,
                10.0,
            ]
            count = metrics["count"]

            for bucket in buckets:
                # Estimate bucket count (simplified)
                bucket_count = int(count * (bucket / 10.0)) if bucket < 10.0 else count
                lines.append(
                    f'{base_name}_bucket{{operation="{operation}",le="{bucket}"}} {bucket_count}'
                )

            lines.append(
                f'{base_name}_bucket{{operation="{operation}",le="+Inf"}} {count}'
            )
            lines.append(f'{base_name}_count{{operation="{operation}"}} {count}')
            lines.append(
                f'{base_name}_sum{{operation="{operation}"}} {metrics["avg_duration_ms"] * count / 1000}'
            )

        # Add system metrics
        if self._system_metrics_enabled:
            system_metrics = self._get_system_metrics()
            for name, value in system_metrics.items():
                lines.append(f"{name} {value}")

        # Add shared memory metrics
        shared_memory_metrics = self._get_shared_memory_metrics()
        for name, value in shared_memory_metrics.items():
            lines.append(f"{name} {value}")

        # Add timestamp
        timestamp = int(time.time() * 1000)
        for i, line in enumerate(lines):
            if line.startswith("#"):
                continue
            lines[i] = f"{line} {timestamp}"

        return "\n".join(lines) + "\n"


class MetricsServer:
    """
    Production-grade metrics HTTP server.
    Serves Prometheus metrics endpoint with health checks.
    """

    def __init__(self, host: str = "0.0.0.0", port: int = 8080):
        self.host = host
        self.port = port
        self.logger = get_structured_logger("MetricsServer")
        self.exporter = PrometheusMetricsExporter()
        self.app = web.Application()
        self.runner = None
        self.site = None

        # Server statistics
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0

        # Setup routes
        self._setup_routes()

    def _setup_routes(self):
        """Setup HTTP routes."""
        self.app.router.add_get("/metrics", self._metrics_handler)
        self.app.router.add_get("/health", self._health_handler)
        self.app.router.add_get("/ready", self._readiness_handler)
        self.app.router.add_get("/info", self._info_handler)

        # Add middleware
        self.app.middlewares.append(self._logging_middleware)
        self.app.middlewares.append(self._error_middleware)

    async def _logging_middleware(self, request, handler):
        """Logging middleware for request tracking."""
        start_time = time.time()

        try:
            response = await handler(request)
            duration = time.time() - start_time

            self.logger.info(
                f"HTTP {request.method} {request.path}",
                status_code=response.status,
                duration_ms=duration * 1000,
                remote_addr=request.remote,
                user_agent=request.headers.get("User-Agent", "Unknown"),
            )

            self.request_count += 1
            return response

        except Exception as e:
            duration = time.time() - start_time
            self.error_count += 1

            self.logger.error(
                f"HTTP {request.method} {request.path} failed",
                error=str(e),
                duration_ms=duration * 1000,
                remote_addr=request.remote,
            )
            raise

    async def _error_middleware(self, request, handler):
        """Error handling middleware."""
        try:
            return await handler(request)
        except web.HTTPException:
            raise
        except Exception as e:
            self.logger.error(f"Unhandled error in {request.path}: {e}")
            return web.json_response(
                {"error": "Internal server error", "message": str(e)}, status=500
            )

    async def _metrics_handler(self, request):
        """Handle /metrics endpoint."""
        try:
            metrics_data = self.exporter.export_metrics()
            return web.Response(
                text=metrics_data,
                content_type="text/plain; version=0.0.4; charset=utf-8",
            )
        except Exception as e:
            self.logger.error(f"Failed to export metrics: {e}")
            return web.json_response(
                {"error": "Failed to export metrics", "message": str(e)}, status=500
            )

    async def _health_handler(self, request):
        """Handle /health endpoint."""
        health_data = {
            "status": "healthy",
            "timestamp": time.time(),
            "uptime_seconds": time.time() - self.start_time,
            "version": "1.0.0",
        }

        return web.json_response(health_data)

    async def _readiness_handler(self, request):
        """Handle /ready endpoint."""
        # Check if application is ready to serve traffic
        try:
            # Add readiness checks here (database connections, etc.)
            ready = True

            if ready:
                return web.json_response({"status": "ready"})
            else:
                return web.json_response({"status": "not ready"}, status=503)

        except Exception as e:
            return web.json_response(
                {"status": "not ready", "error": str(e)}, status=503
            )

    async def _info_handler(self, request):
        """Handle /info endpoint."""
        info_data = {
            "application": "DataDriven Trading Platform",
            "version": "1.0.0",
            "build_time": "2025-08-07T15:42:32Z",
            "python_version": f"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}",
            "metrics": {
                "requests_served": self.request_count,
                "errors": self.error_count,
                "uptime_seconds": time.time() - self.start_time,
            },
        }

        return web.json_response(info_data)

    async def start(self):
        """Start the metrics server."""
        try:
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()

            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()

            self.logger.info(
                f"Metrics server started on http://{self.host}:{self.port}"
            )
            self.logger.info(
                f"Metrics endpoint: http://{self.host}:{self.port}/metrics"
            )
            self.logger.info(f"Health endpoint: http://{self.host}:{self.port}/health")

        except Exception as e:
            self.logger.error(f"Failed to start metrics server: {e}")
            raise

    async def stop(self):
        """Stop the metrics server."""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()

            self.logger.info("Metrics server stopped")

        except Exception as e:
            self.logger.error(f"Error stopping metrics server: {e}")


# Global metrics server instance
metrics_server = None


async def start_metrics_server(host: str = "0.0.0.0", port: int = 8080):
    """Start the global metrics server."""
    global metrics_server

    if metrics_server is None:
        metrics_server = MetricsServer(host, port)
        await metrics_server.start()

    return metrics_server


async def stop_metrics_server():
    """Stop the global metrics server."""
    global metrics_server

    if metrics_server:
        await metrics_server.stop()
        metrics_server = None

/**
 * Continuous benchmarking for C++ compute kernels
 * Implements Google Benchmark integration for performance regression detection
 */

#include <benchmark/benchmark.h>
#include <vector>
#include <random>
#include <algorithm>
#include <numeric>
#include <chrono>

// Include our indicators header (assuming it exists)
// #include "indicators.h"

// Mock implementations for benchmarking (replace with actual when available)
namespace mock_indicators {
    
    struct Result {
        bool success;
        std::vector<double> data;
        std::string error_message;
        
        Result(std::vector<double> d) : success(true), data(std::move(d)) {}
        Result(const std::string& err) : success(false), error_message(err) {}
    };
    
    // Mock Donchian midpoint calculation
    Result calculate_donchian_midpoint(const std::vector<double>& closes, int length) {
        if (closes.empty() || length <= 0) {
            return Result("Invalid parameters");
        }
        
        std::vector<double> result(closes.size(), std::numeric_limits<double>::quiet_NaN());
        
        for (size_t i = length - 1; i < closes.size(); ++i) {
            auto begin = closes.begin() + i - length + 1;
            auto end = closes.begin() + i + 1;
            
            auto [min_it, max_it] = std::minmax_element(begin, end);
            result[i] = (*min_it + *max_it) * 0.5;
        }
        
        return Result(std::move(result));
    }
    
    // Mock simple moving average
    Result simple_moving_average(const std::vector<double>& data, int window) {
        if (data.empty() || window <= 0) {
            return Result("Invalid parameters");
        }
        
        std::vector<double> result(data.size(), std::numeric_limits<double>::quiet_NaN());
        
        if (data.size() >= static_cast<size_t>(window)) {
            double sum = std::accumulate(data.begin(), data.begin() + window, 0.0);
            result[window - 1] = sum / window;
            
            for (size_t i = window; i < data.size(); ++i) {
                sum = sum - data[i - window] + data[i];
                result[i] = sum / window;
            }
        }
        
        return Result(std::move(result));
    }
    
    // Mock SIMD-optimized min/max finding
    void find_min_max_simd(const double* data, size_t length, double& min_val, double& max_val) {
        if (length == 0) {
            min_val = max_val = 0.0;
            return;
        }
        
        min_val = max_val = data[0];
        for (size_t i = 1; i < length; ++i) {
            if (data[i] < min_val) min_val = data[i];
            if (data[i] > max_val) max_val = data[i];
        }
    }
}

// Benchmark data generator
class BenchmarkDataGenerator {
private:
    std::mt19937 gen;
    std::uniform_real_distribution<double> price_dist;
    
public:
    BenchmarkDataGenerator() : gen(42), price_dist(100.0, 200.0) {}
    
    std::vector<double> generate_price_data(size_t size) {
        std::vector<double> data;
        data.reserve(size);
        
        double price = 150.0;
        for (size_t i = 0; i < size; ++i) {
            // Simple random walk
            price += (price_dist(gen) - 150.0) * 0.01;
            data.push_back(price);
        }
        
        return data;
    }
    
    std::vector<double> generate_random_data(size_t size, double min_val = 0.0, double max_val = 100.0) {
        std::uniform_real_distribution<double> dist(min_val, max_val);
        std::vector<double> data;
        data.reserve(size);
        
        for (size_t i = 0; i < size; ++i) {
            data.push_back(dist(gen));
        }
        
        return data;
    }
};

// Global data generator
BenchmarkDataGenerator data_generator;

// Benchmark: Donchian Midpoint Calculation
static void BM_DonchianMidpoint_Small(benchmark::State& state) {
    auto data = data_generator.generate_price_data(100);
    int length = 20;
    
    for (auto _ : state) {
        auto result = mock_indicators::calculate_donchian_midpoint(data, length);
        benchmark::DoNotOptimize(result);
    }
    
    state.SetItemsProcessed(state.iterations() * data.size());
    state.SetBytesProcessed(state.iterations() * data.size() * sizeof(double));
}
BENCHMARK(BM_DonchianMidpoint_Small);

static void BM_DonchianMidpoint_Medium(benchmark::State& state) {
    auto data = data_generator.generate_price_data(1000);
    int length = 50;
    
    for (auto _ : state) {
        auto result = mock_indicators::calculate_donchian_midpoint(data, length);
        benchmark::DoNotOptimize(result);
    }
    
    state.SetItemsProcessed(state.iterations() * data.size());
    state.SetBytesProcessed(state.iterations() * data.size() * sizeof(double));
}
BENCHMARK(BM_DonchianMidpoint_Medium);

static void BM_DonchianMidpoint_Large(benchmark::State& state) {
    auto data = data_generator.generate_price_data(10000);
    int length = 100;
    
    for (auto _ : state) {
        auto result = mock_indicators::calculate_donchian_midpoint(data, length);
        benchmark::DoNotOptimize(result);
    }
    
    state.SetItemsProcessed(state.iterations() * data.size());
    state.SetBytesProcessed(state.iterations() * data.size() * sizeof(double));
}
BENCHMARK(BM_DonchianMidpoint_Large);

// Benchmark: Simple Moving Average
static void BM_SimpleMovingAverage(benchmark::State& state) {
    size_t data_size = state.range(0);
    int window = state.range(1);
    
    auto data = data_generator.generate_price_data(data_size);
    
    for (auto _ : state) {
        auto result = mock_indicators::simple_moving_average(data, window);
        benchmark::DoNotOptimize(result);
    }
    
    state.SetItemsProcessed(state.iterations() * data.size());
    state.SetComplexityN(data_size);
}
BENCHMARK(BM_SimpleMovingAverage)
    ->Args({100, 10})
    ->Args({1000, 20})
    ->Args({10000, 50})
    ->Args({100000, 100})
    ->Complexity();

// Benchmark: SIMD Min/Max Finding
static void BM_FindMinMaxSIMD(benchmark::State& state) {
    size_t data_size = state.range(0);
    auto data = data_generator.generate_random_data(data_size);
    
    double min_val, max_val;
    
    for (auto _ : state) {
        mock_indicators::find_min_max_simd(data.data(), data.size(), min_val, max_val);
        benchmark::DoNotOptimize(min_val);
        benchmark::DoNotOptimize(max_val);
    }
    
    state.SetItemsProcessed(state.iterations() * data.size());
    state.SetBytesProcessed(state.iterations() * data.size() * sizeof(double));
    state.SetComplexityN(data_size);
}
BENCHMARK(BM_FindMinMaxSIMD)
    ->Range(64, 1024*1024)
    ->Complexity();

// Benchmark: Memory allocation patterns
static void BM_VectorAllocation(benchmark::State& state) {
    size_t size = state.range(0);
    
    for (auto _ : state) {
        std::vector<double> vec(size);
        std::iota(vec.begin(), vec.end(), 0.0);
        benchmark::DoNotOptimize(vec);
    }
    
    state.SetItemsProcessed(state.iterations() * size);
    state.SetBytesProcessed(state.iterations() * size * sizeof(double));
}
BENCHMARK(BM_VectorAllocation)
    ->Range(1024, 1024*1024);

// Benchmark: Cache-friendly vs cache-unfriendly access patterns
static void BM_SequentialAccess(benchmark::State& state) {
    size_t size = state.range(0);
    auto data = data_generator.generate_random_data(size);
    
    double sum = 0.0;
    for (auto _ : state) {
        for (size_t i = 0; i < data.size(); ++i) {
            sum += data[i];
        }
        benchmark::DoNotOptimize(sum);
    }
    
    state.SetItemsProcessed(state.iterations() * size);
    state.SetBytesProcessed(state.iterations() * size * sizeof(double));
}
BENCHMARK(BM_SequentialAccess)
    ->Range(1024, 1024*1024);

static void BM_RandomAccess(benchmark::State& state) {
    size_t size = state.range(0);
    auto data = data_generator.generate_random_data(size);
    
    // Generate random indices
    std::vector<size_t> indices(size);
    std::iota(indices.begin(), indices.end(), 0);
    std::shuffle(indices.begin(), indices.end(), std::mt19937{42});
    
    double sum = 0.0;
    for (auto _ : state) {
        for (size_t idx : indices) {
            sum += data[idx];
        }
        benchmark::DoNotOptimize(sum);
    }
    
    state.SetItemsProcessed(state.iterations() * size);
    state.SetBytesProcessed(state.iterations() * size * sizeof(double));
}
BENCHMARK(BM_RandomAccess)
    ->Range(1024, 1024*1024);

// Custom main function for benchmark configuration
int main(int argc, char** argv) {
    // Configure benchmark
    benchmark::Initialize(&argc, argv);
    
    if (benchmark::ReportUnrecognizedArguments(argc, argv)) {
        return 1;
    }
    
    // Set benchmark configuration
    benchmark::RegisterBenchmark("BM_CustomTest", [](benchmark::State& state) {
        for (auto _ : state) {
            // Custom benchmark code
            std::vector<double> data(1000);
            std::iota(data.begin(), data.end(), 1.0);
            benchmark::DoNotOptimize(data);
        }
    });
    
    // Run benchmarks
    benchmark::RunSpecifiedBenchmarks();
    benchmark::Shutdown();
    
    return 0;
}

# Build artifacts
build/
*.pyd
*.so
*.dll
*.exe
*.obj
*.lib
*.exp

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.egg-info/
dist/
.pytest_cache/

# IDE files
.vscode/
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo

# CMake
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake

# Personal test files
test_*.py
clean_*.py

# Logs
*.log
logs/

# OS files
.DS_Store
Thumbs.db

# Personal paths (add your specific patterns here)
**/Users/<USER>/
**/AppData/*/
C:/Users/<USER>/

"""
Computation offloader for moving all mathematical operations out of UI thread.
Implements proper thread discipline with zero blocking operations in UI.
"""

from PyQt6.QtCore import QObject, QThread, QThreadPool, QRunnable, pyqtSignal, pyqtSlot
from typing import Dict, Any, List, Optional, Callable, Union
import logging
import time
from dataclasses import dataclass
from enum import Enum


class ComputationType(Enum):
    """Types of computations that can be offloaded."""

    BOUNDS_CALCULATION = "bounds_calculation"
    DISTANCE_CALCULATION = "distance_calculation"
    RANGE_DETECTION = "range_detection"
    POSITION_MAPPING = "position_mapping"
    COLOR_DETERMINATION = "color_determination"
    VISIBILITY_CHECK = "visibility_check"


@dataclass
class ComputationTask:
    """Immutable computation task definition."""

    task_id: str
    computation_type: ComputationType
    input_data: Dict[str, Any]
    callback: Optional[Callable] = None
    priority: int = 0  # Higher number = higher priority


class ComputationWorker(QRunnable):
    """
    Worker for offloaded computations.
    Runs in thread pool to avoid blocking UI.
    """

    def __init__(self, task: ComputationTask, signals: "ComputationSignals"):
        super().__init__()
        self.task = task
        self.signals = signals
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for computation worker."""
        logger = logging.getLogger(f"ComputationWorker.{self.task.task_id}")
        logger.setLevel(logging.DEBUG)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s", "task_id": "%(task_id)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def run(self):
        """Execute the computation task."""
        try:
            start_time = time.perf_counter()

            self.logger.debug(
                f"Starting computation: {self.task.computation_type.value}"
            )
            self.signals.computation_started.emit(
                self.task.task_id, self.task.computation_type.value
            )

            # Perform computation based on type
            result = self._perform_computation()

            # Calculate execution time
            execution_time = (time.perf_counter() - start_time) * 1000

            # Emit success signal
            self.signals.computation_completed.emit(
                self.task.task_id, result, execution_time
            )
            self.logger.debug(f"Computation completed in {execution_time:.2f}ms")

        except Exception as e:
            error_msg = f"Computation failed: {str(e)}"
            self.logger.error(error_msg)
            self.signals.computation_failed.emit(self.task.task_id, error_msg)

    def _perform_computation(self) -> Dict[str, Any]:
        """Perform the actual computation based on task type."""
        if self.task.computation_type == ComputationType.BOUNDS_CALCULATION:
            return self._calculate_bounds()
        elif self.task.computation_type == ComputationType.DISTANCE_CALCULATION:
            return self._calculate_distance()
        elif self.task.computation_type == ComputationType.RANGE_DETECTION:
            return self._detect_range()
        elif self.task.computation_type == ComputationType.POSITION_MAPPING:
            return self._map_position()
        elif self.task.computation_type == ComputationType.COLOR_DETERMINATION:
            return self._determine_color()
        elif self.task.computation_type == ComputationType.VISIBILITY_CHECK:
            return self._check_visibility()
        else:
            raise ValueError(f"Unknown computation type: {self.task.computation_type}")

    def _calculate_bounds(self) -> Dict[str, Any]:
        """Calculate bounding rectangle for data points."""
        data_points = self.task.input_data.get("data_points", [])

        if not data_points:
            return {"x_min": 0, "x_max": 1, "y_min": 0, "y_max": 1}

        # Extract coordinates
        x_coords = []
        y_coords = []

        for point in data_points:
            if isinstance(point, dict):
                x_coords.append(point.get("x", 0))
                y_coords.append(point.get("y", 0))
            elif isinstance(point, (list, tuple)) and len(point) >= 2:
                x_coords.append(point[0])
                y_coords.append(point[1])

        if not x_coords or not y_coords:
            return {"x_min": 0, "x_max": 1, "y_min": 0, "y_max": 1}

        return {
            "x_min": min(x_coords),
            "x_max": max(x_coords),
            "y_min": min(y_coords),
            "y_max": max(y_coords),
        }

    def _calculate_distance(self) -> Dict[str, Any]:
        """Calculate distance between points."""
        point1 = self.task.input_data.get("point1", {})
        point2 = self.task.input_data.get("point2", {})

        x1, y1 = point1.get("x", 0), point1.get("y", 0)
        x2, y2 = point2.get("x", 0), point2.get("y", 0)

        # Euclidean distance
        distance = ((x2 - x1) ** 2 + (y2 - y1) ** 2) ** 0.5

        return {"distance": distance, "dx": x2 - x1, "dy": y2 - y1}

    def _detect_range(self) -> Dict[str, Any]:
        """Detect if values are within specified range."""
        values = self.task.input_data.get("values", [])
        min_val = self.task.input_data.get("min_value", 0)
        max_val = self.task.input_data.get("max_value", 1)

        in_range = []
        out_of_range = []

        for i, value in enumerate(values):
            if min_val <= value <= max_val:
                in_range.append(i)
            else:
                out_of_range.append(i)

        return {
            "in_range_indices": in_range,
            "out_of_range_indices": out_of_range,
            "in_range_count": len(in_range),
            "out_of_range_count": len(out_of_range),
        }

    def _map_position(self) -> Dict[str, Any]:
        """Map screen coordinates to data coordinates."""
        screen_pos = self.task.input_data.get("screen_position", {})
        viewport_bounds = self.task.input_data.get("viewport_bounds", {})
        data_bounds = self.task.input_data.get("data_bounds", {})

        screen_x = screen_pos.get("x", 0)
        screen_y = screen_pos.get("y", 0)

        # Viewport bounds
        vp_x_min = viewport_bounds.get("x_min", 0)
        vp_x_max = viewport_bounds.get("x_max", 1)
        vp_y_min = viewport_bounds.get("y_min", 0)
        vp_y_max = viewport_bounds.get("y_max", 1)

        # Data bounds
        data_x_min = data_bounds.get("x_min", 0)
        data_x_max = data_bounds.get("x_max", 1)
        data_y_min = data_bounds.get("y_min", 0)
        data_y_max = data_bounds.get("y_max", 1)

        # Map coordinates
        if vp_x_max != vp_x_min:
            data_x = data_x_min + (screen_x - vp_x_min) * (data_x_max - data_x_min) / (
                vp_x_max - vp_x_min
            )
        else:
            data_x = data_x_min

        if vp_y_max != vp_y_min:
            data_y = data_y_min + (screen_y - vp_y_min) * (data_y_max - data_y_min) / (
                vp_y_max - vp_y_min
            )
        else:
            data_y = data_y_min

        return {
            "data_x": data_x,
            "data_y": data_y,
            "screen_x": screen_x,
            "screen_y": screen_y,
        }

    def _determine_color(self) -> Dict[str, Any]:
        """Determine color based on value and conditions."""
        value = self.task.input_data.get("value", 0)
        conditions = self.task.input_data.get("conditions", {})

        # Default colors
        default_color = conditions.get("default", "#ffffff")
        positive_color = conditions.get("positive", "#00ff00")
        negative_color = conditions.get("negative", "#ff0000")
        zero_color = conditions.get("zero", "#cccccc")

        # Determine color
        if abs(value) < 1e-10:  # Close to zero
            color = zero_color
        elif value > 0:
            color = positive_color
        elif value < 0:
            color = negative_color
        else:
            color = default_color

        return {
            "color": color,
            "value": value,
            "category": (
                "zero"
                if abs(value) < 1e-10
                else ("positive" if value > 0 else "negative")
            ),
        }

    def _check_visibility(self) -> Dict[str, Any]:
        """Check if elements are visible within viewport."""
        elements = self.task.input_data.get("elements", [])
        viewport = self.task.input_data.get("viewport", {})

        vp_x_min = viewport.get("x_min", 0)
        vp_x_max = viewport.get("x_max", 1)
        vp_y_min = viewport.get("y_min", 0)
        vp_y_max = viewport.get("y_max", 1)

        visible_elements = []
        hidden_elements = []

        for i, element in enumerate(elements):
            bounds = element.get("bounds", {})
            elem_x_min = bounds.get("x_min", 0)
            elem_x_max = bounds.get("x_max", 1)
            elem_y_min = bounds.get("y_min", 0)
            elem_y_max = bounds.get("y_max", 1)

            # Check intersection with viewport
            if (
                elem_x_max >= vp_x_min
                and elem_x_min <= vp_x_max
                and elem_y_max >= vp_y_min
                and elem_y_min <= vp_y_max
            ):
                visible_elements.append(i)
            else:
                hidden_elements.append(i)

        return {
            "visible_indices": visible_elements,
            "hidden_indices": hidden_elements,
            "visible_count": len(visible_elements),
            "hidden_count": len(hidden_elements),
        }


class ComputationSignals(QObject):
    """Signals for computation events."""

    computation_started = pyqtSignal(str, str)  # task_id, computation_type
    computation_completed = pyqtSignal(
        str, dict, float
    )  # task_id, result, execution_time_ms
    computation_failed = pyqtSignal(str, str)  # task_id, error_message


class ComputationOffloader(QObject):
    """
    Main computation offloader that manages thread pool and task scheduling.
    Ensures zero blocking operations in UI thread.
    """

    def __init__(self, max_threads: int = 4, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()

        # Thread pool for computations
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(max_threads)

        # Signals for communication
        self.signals = ComputationSignals()

        # Task tracking
        self._active_tasks: Dict[str, ComputationTask] = {}
        self._task_counter = 0

        # Performance monitoring
        self._stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "avg_execution_time": 0.0,
        }

        # Connect signals
        self.signals.computation_completed.connect(self._on_computation_completed)
        self.signals.computation_failed.connect(self._on_computation_failed)

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for computation offloader."""
        logger = logging.getLogger("ComputationOffloader")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def submit_computation(
        self,
        computation_type: ComputationType,
        input_data: Dict[str, Any],
        callback: Optional[Callable] = None,
        priority: int = 0,
    ) -> str:
        """
        Submit computation task to thread pool.
        Returns task ID for tracking.
        """
        # Generate unique task ID
        self._task_counter += 1
        task_id = (
            f"{computation_type.value}_{self._task_counter}_{int(time.time() * 1000)}"
        )

        # Create task
        task = ComputationTask(
            task_id=task_id,
            computation_type=computation_type,
            input_data=input_data,
            callback=callback,
            priority=priority,
        )

        # Track task
        self._active_tasks[task_id] = task

        # Create worker
        worker = ComputationWorker(task, self.signals)

        # Submit to thread pool
        self.thread_pool.start(worker, priority)

        # Update stats
        self._stats["total_tasks"] += 1

        self.logger.debug(f"Submitted computation task: {task_id}")
        return task_id

    @pyqtSlot(str, dict, float)
    def _on_computation_completed(
        self, task_id: str, result: Dict[str, Any], execution_time: float
    ):
        """Handle computation completion."""
        if task_id in self._active_tasks:
            task = self._active_tasks[task_id]

            # Call callback if provided
            if task.callback:
                try:
                    task.callback(result)
                except Exception as e:
                    self.logger.error(f"Callback error for task {task_id}: {e}")

            # Remove from active tasks
            del self._active_tasks[task_id]

            # Update stats
            self._stats["completed_tasks"] += 1
            alpha = 0.1  # Smoothing factor
            self._stats["avg_execution_time"] = (
                alpha * execution_time + (1 - alpha) * self._stats["avg_execution_time"]
            )

            self.logger.debug(
                f"Computation completed: {task_id} in {execution_time:.2f}ms"
            )

    @pyqtSlot(str, str)
    def _on_computation_failed(self, task_id: str, error_message: str):
        """Handle computation failure."""
        if task_id in self._active_tasks:
            del self._active_tasks[task_id]

        # Update stats
        self._stats["failed_tasks"] += 1

        self.logger.error(f"Computation failed: {task_id} - {error_message}")

    def get_stats(self) -> Dict[str, Any]:
        """Get computation statistics."""
        return {
            **self._stats,
            "active_tasks": len(self._active_tasks),
            "thread_pool_active": self.thread_pool.activeThreadCount(),
            "thread_pool_max": self.thread_pool.maxThreadCount(),
        }

    def shutdown(self):
        """Gracefully shutdown the computation offloader."""
        self.logger.info("Shutting down computation offloader...")
        self.thread_pool.waitForDone(5000)  # Wait up to 5 seconds
        self.logger.info("Computation offloader shutdown complete")


# Global computation offloader instance
computation_offloader = ComputationOffloader()

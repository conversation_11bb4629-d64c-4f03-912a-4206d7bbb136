[build-system]
requires = [
    "scikit-build-core>=0.7.0",
    "pybind11>=2.11.0",
]
build-backend = "scikit_build_core.build"

[project]
name = "datadriven-trading"
version = "1.0.0"
description = "Enterprise-grade trading analysis platform with PyQt6 frontend and C++ compute kernels"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "DataDriven Team", email = "<EMAIL>"},
]
maintainers = [
    {name = "DataDriven Team", email = "<EMAIL>"},
]
keywords = [
    "trading", "finance", "technical-analysis", "qt", "cpp", "performance",
    "real-time", "market-data", "indicators", "charting"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: C++",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Information Analysis",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Environment :: X11 Applications :: Qt",
]
requires-python = ">=3.9"
dependencies = [
    "PyQt6>=6.5.0",
    "PyQtGraph>=0.13.0",
    "numpy>=1.21.0",
    "pandas>=1.5.0",
    "yfinance>=0.2.0",
    "aiohttp>=3.8.0",
    "asyncio-mqtt>=0.13.0",
    "psutil>=5.9.0",
    "structlog>=23.1.0",
    "prometheus-client>=0.17.0",
    "protobuf>=4.24.0",
    "pydantic>=2.0.0",
    "click>=8.1.0",
    "rich>=13.5.0",
    "uvloop>=0.17.0; platform_system!='Windows'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.0",
    "pytest-qt>=4.2.0",
    "pytest-mock>=3.11.0",
    "pytest-benchmark>=4.0.0",
    "pytest-html>=3.2.0",
    "pytest-json-report>=1.5.0",
    "pytest-xdist>=3.3.0",
    "black>=23.7.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pylint>=2.17.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
    "pre-commit>=3.3.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
    "sphinx-autodoc-typehints>=1.24.0",
    "myst-parser>=2.0.0",
]
performance = [
    "py-spy>=0.3.14",
    "memory-profiler>=0.61.0",
    "line-profiler>=4.1.0",
    "scalene>=1.5.0",
]
all = [
    "datadriven-trading[dev,docs,performance]"
]

[project.urls]
Homepage = "https://github.com/your-org/datadriven"
Documentation = "https://datadriven.readthedocs.io"
Repository = "https://github.com/your-org/datadriven.git"
Issues = "https://github.com/your-org/datadriven/issues"
Changelog = "https://github.com/your-org/datadriven/blob/main/CHANGELOG.md"

[project.scripts]
datadriven = "main:main"
datadriven-benchmark = "scripts.benchmark:main"
datadriven-test = "scripts.test_runner:main"

[project.gui-scripts]
datadriven-gui = "main:main"

# ============================================================================
# Scikit-Build-Core Configuration (PEP 517 Compliant)
# ============================================================================
[tool.scikit-build]
# Build configuration
build-dir = "build/{wheel_tag}"
cmake.version = ">=3.18"
cmake.build-type = "Release"
cmake.source-dir = "."

# Installation settings
install.components = ["Python"]
install.strip = false  # Keep debug symbols in development

# Wheel configuration
wheel.expand-macos-universal-tags = true
wheel.install-dir = "src/datadriven"
wheel.py-api = "cp39"  # Minimum Python API

# Metadata configuration
metadata.version.provider = "scikit_build_core.metadata.setuptools_scm"

# Logging and debugging
logging.level = "INFO"
build.verbose = true

# Parallel builds (Windows MSBuild doesn't support -j flag)
# build.tool-args = ["-j"]

# Platform-specific settings
[tool.scikit-build.cmake.define]
# Core build settings
BUILD_PYTHON_BINDINGS = "ON"
CMAKE_BUILD_TYPE = {env="CMAKE_BUILD_TYPE", default="Release"}
CMAKE_POSITION_INDEPENDENT_CODE = "ON"

# Optional features (controlled by environment)
BUILD_TESTS = {env="SKBUILD_BUILD_TESTS", default="OFF"}
BUILD_BENCHMARKS = {env="SKBUILD_BUILD_BENCHMARKS", default="OFF"}
ENABLE_SIMD = {env="SKBUILD_ENABLE_SIMD", default="ON"}
ENABLE_OPENMP = {env="SKBUILD_ENABLE_OPENMP", default="OFF"}

# Optimization settings
CMAKE_INTERPROCEDURAL_OPTIMIZATION = {env="SKBUILD_LTO", default="ON"}
CMAKE_CXX_STANDARD = "17"
CMAKE_CXX_STANDARD_REQUIRED = "ON"

# Platform-specific optimizations (simplified)
CMAKE_CXX_FLAGS_RELEASE = "-O3 -DNDEBUG"

# ============================================================================
# Tool Configurations
# ============================================================================

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | resources/.*_rc\.py
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["backend", "frontend", "compute"]
known_third_party = ["PyQt6", "numpy", "pandas", "pytest"]
skip_glob = ["resources/*_rc.py", "build/*", "dist/*"]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    "*.egg-info",
    "resources/*_rc.py",
]
per-file-ignores = [
    "__init__.py:F401",
    "tests/*:S101",  # Allow assert in tests
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "PyQt6.*",
    "pyqtgraph.*",
    "yfinance.*",
    "uvloop.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=backend",
    "--cov=frontend",
    "--cov=compute",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tools/tests"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "gui: marks tests as GUI tests requiring display",
    "performance: marks tests as performance benchmarks",
    "network: marks tests requiring network access",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["backend", "frontend", "compute"]
omit = [
    "*/tests/*",
    "*/test_*.py",
    "*/*_test.py",
    "setup.py",
    "setup_development.py",
    "compile_*.py",
    "resources/*_rc.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests", "build", "dist"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process

[tool.pylint.messages_control]
disable = [
    "C0103",  # Invalid name
    "C0111",  # Missing docstring
    "R0903",  # Too few public methods
    "R0913",  # Too many arguments
    "W0613",  # Unused argument
]

[tool.pylint.format]
max-line-length = 88

# ============================================================================
# Setuptools SCM (for version management)
# ============================================================================
[tool.setuptools_scm]
write_to = "src/datadriven/_version.py"
version_scheme = "release-branch-semver"

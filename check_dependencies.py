#!/usr/bin/env python3
"""
Check what dependencies are available for the DataDriven Trading Platform
"""

import sys
import os
import importlib.util

def check_package(package_name, import_name=None):
    """Check if a package is available."""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            # Try to actually import it
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'Unknown')
            return True, version
        else:
            return False, None
    except Exception as e:
        return False, str(e)

def main():
    """Check all dependencies."""
    print(f"🐍 Python Version: {sys.version}")
    print("=" * 60)
    
    # Add paths
    debug_path = os.path.join(os.getcwd(), 'build', 'Debug')
    src_path = os.path.join(os.getcwd(), 'src')
    sys.path.insert(0, debug_path)
    sys.path.insert(0, src_path)
    
    packages_to_check = [
        ("PyQt6", "PyQt6"),
        ("PyQtGraph", "pyqtgraph"),
        ("NumPy", "numpy"),
        ("Pandas", "pandas"),
        ("YFinance", "yfinance"),
        ("AIOHttp", "aiohttp"),
        ("AsyncIO-MQTT", "asyncio_mqtt"),
        ("PSUtil", "psutil"),
        ("StructLog", "structlog"),
        ("Prometheus Client", "prometheus_client"),
        ("Protobuf", "google.protobuf"),
        ("Pydantic", "pydantic"),
        ("Click", "click"),
        ("Rich", "rich"),
        ("Indicators (C++)", "indicators"),
    ]
    
    available = []
    missing = []
    
    print("📦 Dependency Check:")
    for display_name, import_name in packages_to_check:
        is_available, version = check_package(display_name, import_name)
        if is_available:
            print(f"  ✅ {display_name:<20} - {version}")
            available.append(display_name)
        else:
            print(f"  ❌ {display_name:<20} - Not available")
            missing.append(display_name)
    
    print(f"\n📊 Summary:")
    print(f"  ✅ Available: {len(available)}")
    print(f"  ❌ Missing: {len(missing)}")
    
    if missing:
        print(f"\n📋 Missing packages:")
        for pkg in missing:
            print(f"    - {pkg}")
        print(f"\n💡 Run 'launch_platform.py' to install missing dependencies")
    else:
        print(f"\n🎉 All dependencies are available!")
        print(f"🚀 Ready to launch the platform!")

if __name__ == "__main__":
    main()

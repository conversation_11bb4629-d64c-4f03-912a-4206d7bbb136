"""
Pytest configuration and fixtures for comprehensive testing.
Provides shared fixtures for backend, frontend, and integration tests.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, MagicMock
import numpy as np
import pandas as pd
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
import sys
import os

# Add project root and src directory to path
project_root = Path(__file__).parent.parent.parent
src_dir = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_dir))

# Import project modules
from backend.data_service import DataService
from backend.shared_memory_manager import SharedMemoryManager
from backend.observability import CorrelationManager, MetricsCollector


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def qapp():
    """Create QApplication instance for GUI tests."""
    if not QApplication.instance():
        app = QApplication([])
    else:
        app = QApplication.instance()
    yield app
    app.quit()


@pytest.fixture
def temp_dir():
    """Create temporary directory for test files."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_ohlc_data():
    """Generate sample OHLC data for testing."""
    np.random.seed(42)  # Reproducible data

    dates = pd.date_range("2023-01-01", periods=100, freq="D")
    base_price = 100.0

    data = []
    current_price = base_price

    for date in dates:
        # Simple random walk
        change = np.random.normal(0, 0.02)
        current_price *= 1 + change

        # Generate OHLC from current price
        open_price = current_price * (1 + np.random.normal(0, 0.005))
        high_price = max(open_price, current_price) * (
            1 + abs(np.random.normal(0, 0.01))
        )
        low_price = min(open_price, current_price) * (
            1 - abs(np.random.normal(0, 0.01))
        )
        close_price = current_price
        volume = int(np.random.uniform(1000000, 5000000))

        data.append(
            {
                "Date": date,
                "Open": open_price,
                "High": high_price,
                "Low": low_price,
                "Close": close_price,
                "Volume": volume,
            }
        )

    return pd.DataFrame(data)


@pytest.fixture
def sample_numpy_arrays():
    """Generate sample NumPy arrays for testing."""
    np.random.seed(42)
    return {
        "small": np.random.random(100),
        "medium": np.random.random(1000),
        "large": np.random.random(10000),
        "int_array": np.random.randint(0, 100, 500),
        "float32": np.random.random(500).astype(np.float32),
        "float64": np.random.random(500).astype(np.float64),
    }


@pytest.fixture
async def data_service():
    """Create DataService instance for testing."""
    service = DataService()
    await service.initialize()
    yield service
    await service.shutdown()


@pytest.fixture
def shared_memory_manager():
    """Create SharedMemoryManager instance for testing."""
    manager = SharedMemoryManager()
    yield manager
    manager.cleanup_all_buffers()


@pytest.fixture
def correlation_manager():
    """Create CorrelationManager instance for testing."""
    manager = CorrelationManager()
    yield manager
    # Cleanup any remaining contexts
    manager._contexts.clear()


@pytest.fixture
def metrics_collector():
    """Create MetricsCollector instance for testing."""
    collector = MetricsCollector()
    yield collector
    # Reset metrics
    collector._counters.clear()
    collector._histograms.clear()
    collector._gauges.clear()


@pytest.fixture
def mock_yfinance():
    """Mock yfinance for testing without network calls."""
    with pytest.mock.patch("yfinance.download") as mock_download:
        # Return sample data
        mock_data = pd.DataFrame(
            {
                "Open": [100, 101, 102, 103, 104],
                "High": [105, 106, 107, 108, 109],
                "Low": [95, 96, 97, 98, 99],
                "Close": [102, 103, 104, 105, 106],
                "Volume": [1000000, 1100000, 1200000, 1300000, 1400000],
            },
            index=pd.date_range("2023-01-01", periods=5),
        )

        mock_download.return_value = mock_data
        yield mock_download


@pytest.fixture
def mock_cpp_kernels():
    """Mock C++ kernels for testing without compiled extensions."""

    class MockResult:
        def __init__(self, data=None, success=True, error_message=""):
            self.data = data
            self.success = success
            self.error_message = error_message

    def mock_donchian_midpoint(closes, length):
        """Mock Donchian midpoint calculation."""
        if not closes or length <= 0:
            return MockResult(success=False, error_message="Invalid parameters")

        result = []
        for i in range(len(closes)):
            if i >= length - 1:
                window = closes[i - length + 1 : i + 1]
                midpoint = (max(window) + min(window)) / 2
                result.append(midpoint)
            else:
                result.append(float("nan"))

        return MockResult(data=result)

    def mock_simple_moving_average(data, window):
        """Mock simple moving average calculation."""
        if not data or window <= 0:
            return MockResult(success=False, error_message="Invalid parameters")

        result = []
        for i in range(len(data)):
            if i >= window - 1:
                window_data = data[i - window + 1 : i + 1]
                avg = sum(window_data) / len(window_data)
                result.append(avg)
            else:
                result.append(float("nan"))

        return MockResult(data=result)

    # Mock the indicators module
    mock_indicators = Mock()
    mock_indicators.calculate_donchian_midpoint = mock_donchian_midpoint
    mock_indicators.simple_moving_average = mock_simple_moving_average

    with pytest.mock.patch.dict("sys.modules", {"indicators": mock_indicators}):
        yield mock_indicators


@pytest.fixture
def performance_test_data():
    """Generate data for performance testing."""
    sizes = [100, 1000, 10000, 100000]
    data = {}

    for size in sizes:
        np.random.seed(42)  # Reproducible
        data[f"array_{size}"] = np.random.random(size)
        data[f"prices_{size}"] = np.random.uniform(50, 150, size)

    return data


@pytest.fixture
def gui_test_timeout():
    """Timeout for GUI tests to prevent hanging."""
    return 5000  # 5 seconds in milliseconds


@pytest.fixture
def qt_test_helper(qapp, gui_test_timeout):
    """Helper for Qt GUI testing."""

    class QtTestHelper:
        def __init__(self, app, timeout):
            self.app = app
            self.timeout = timeout

        def process_events(self, timeout=None):
            """Process Qt events with timeout."""
            if timeout is None:
                timeout = self.timeout

            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(self.app.quit)
            timer.start(timeout)

            self.app.exec()

        def wait_for_signal(self, signal, timeout=None):
            """Wait for a signal to be emitted."""
            if timeout is None:
                timeout = self.timeout

            received = []

            def on_signal(*args):
                received.extend(args)
                self.app.quit()

            signal.connect(on_signal)

            timer = QTimer()
            timer.setSingleShot(True)
            timer.timeout.connect(self.app.quit)
            timer.start(timeout)

            self.app.exec()

            return received

    return QtTestHelper(qapp, gui_test_timeout)


# Test markers
def pytest_configure(config):
    """Configure custom pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line("markers", "integration: marks tests as integration tests")
    config.addinivalue_line(
        "markers", "gui: marks tests as GUI tests requiring display"
    )
    config.addinivalue_line(
        "markers", "performance: marks tests as performance benchmarks"
    )
    config.addinivalue_line("markers", "network: marks tests requiring network access")


# Skip GUI tests if no display available
def pytest_collection_modifyitems(config, items):
    """Modify test collection based on environment."""
    if not os.environ.get("DISPLAY") and sys.platform.startswith("linux"):
        skip_gui = pytest.mark.skip(reason="No display available for GUI tests")
        for item in items:
            if "gui" in item.keywords:
                item.add_marker(skip_gui)


# Cleanup after tests
@pytest.fixture(autouse=True)
def cleanup_after_test():
    """Automatic cleanup after each test."""
    yield

    # Clean up any remaining shared memory
    try:
        from backend.shared_memory_manager import shared_memory_manager

        shared_memory_manager.cleanup_all_buffers()
    except:
        pass

    # Reset global state
    try:
        from backend.observability import correlation_manager, metrics_collector

        correlation_manager._contexts.clear()
        metrics_collector._counters.clear()
        metrics_collector._histograms.clear()
        metrics_collector._gauges.clear()
    except:
        pass

#!/usr/bin/env python3
"""
Market Odds Application
Ultra-low-latency trading software with PyQt6 frontend, Python backend, and C++ compute kernels.
Enterprise-grade architecture with proper separation of concerns.
"""

import sys
import os
import asyncio
import signal
import logging
from typing import Optional
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer, QObject, pyqtSignal
from PyQt6.QtGui import QPalette, QColor, QFont

# Add src directory to Python path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from frontend.main_window import MainWindow
from backend.resource_manager import resource_manager
from backend.data_service import DataService


class AsyncBackendManager(QObject):
    """
    Enterprise-grade backend manager that handles async operations
    without blocking Qt's main event loop.
    """

    # Signals for thread-safe communication
    backend_initialized = pyqtSignal()
    backend_error = pyqtSignal(str)
    shutdown_complete = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_service: Optional[DataService] = None
        self.logger = self._setup_logging()
        self._initialization_timer: Optional[QTimer] = None

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for backend management."""
        logger = logging.getLogger("AsyncBackendManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def initialize_backend(self):
        """Initialize backend using Qt timer to avoid blocking."""
        try:
            self.logger.info("Starting backend initialization...")

            # Use QTimer.singleShot for non-blocking initialization
            self._initialization_timer = QTimer()
            self._initialization_timer.setSingleShot(True)
            self._initialization_timer.timeout.connect(self._perform_initialization)
            self._initialization_timer.start(100)  # Start after 100ms

        except Exception as e:
            self.logger.error(f"Failed to start backend initialization: {e}")
            self.backend_error.emit(str(e))

    def _perform_initialization(self):
        """Perform the actual backend initialization."""
        try:
            # Create event loop for backend operations
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # Initialize data service
            self.data_service = DataService()
            loop.run_until_complete(self.data_service.initialize())

            # Start metrics server for production monitoring
            self.metrics_server = None
            try:
                from backend.metrics_server import start_metrics_server
                import os

                metrics_host = os.getenv("METRICS_HOST", "0.0.0.0")
                metrics_port = int(os.getenv("METRICS_PORT", "8080"))

                self.metrics_server = loop.run_until_complete(
                    start_metrics_server(metrics_host, metrics_port)
                )
                self.logger.info(
                    f"Metrics server started on {metrics_host}:{metrics_port}"
                )
                self.logger.info(
                    f"Metrics endpoint: http://{metrics_host}:{metrics_port}/metrics"
                )
                self.logger.info(
                    f"Health endpoint: http://{metrics_host}:{metrics_port}/health"
                )

            except Exception as metrics_error:
                self.logger.warning(f"Failed to start metrics server: {metrics_error}")
                self.metrics_server = None

            self.logger.info("Backend initialization completed successfully")
            self.backend_initialized.emit()

        except Exception as e:
            self.logger.error(f"Backend initialization failed: {e}")
            self.backend_error.emit(str(e))
        finally:
            # Clean up the initialization timer
            if self._initialization_timer:
                self._initialization_timer.deleteLater()
                self._initialization_timer = None

    def shutdown_backend(self):
        """Shutdown backend services gracefully."""
        try:
            if self.data_service:
                self.logger.info("Shutting down backend services...")

                # Create event loop for shutdown operations
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                loop.run_until_complete(self.data_service.shutdown())
                loop.close()

                self.data_service = None
                self.logger.info("Backend shutdown completed")

            self.shutdown_complete.emit()

        except Exception as e:
            self.logger.error(f"Error during backend shutdown: {e}")
            self.shutdown_complete.emit()

    def get_data_service(self) -> Optional[DataService]:
        """Get the initialized data service."""
        return self.data_service

    def stop_event_loop(self):
        """Gracefully stop the event loop integration."""
        if not self.is_running:
            return

        self.is_running = False
        self.logger.info("Stopping Qt-asyncio event loop integration...")

        if self.timer:
            self.timer.stop()
            self.timer = None

        if self.data_service:
            # Schedule shutdown task
            asyncio.create_task(self._shutdown_backend())
        else:
            self._complete_shutdown()

    async def _shutdown_backend(self):
        """Shutdown backend services gracefully."""
        try:
            # Stop metrics server first
            if hasattr(self, "metrics_server") and self.metrics_server:
                try:
                    from backend.metrics_server import stop_metrics_server

                    await stop_metrics_server()
                    self.logger.info("Metrics server stopped")
                except Exception as e:
                    self.logger.error(f"Error stopping metrics server: {e}")

            # Stop data service
            if self.data_service:
                await self.data_service.shutdown()
                self.data_service = None

            self.logger.info("Backend shutdown complete")
        except Exception as e:
            self.logger.error(f"Error during backend shutdown: {e}")
        finally:
            self._complete_shutdown()

    def _complete_shutdown(self):
        """Complete the shutdown process."""
        if self.asyncio_loop and not self.asyncio_loop.is_closed():
            self.asyncio_loop.close()
        self.asyncio_loop = None
        self.logger.info("Event loop integration shutdown complete")
        self.shutdown_complete.emit()

    def get_data_service(self) -> Optional[DataService]:
        """Get the initialized data service."""
        return self.data_service


class TradingApplication:
    """
    Enterprise-grade main application controller with proper backend management.
    Manages frontend and backend coordination without blocking Qt's event loop.
    """

    def __init__(self):
        self.app: Optional[QApplication] = None
        self.main_window: Optional[MainWindow] = None
        self.backend_manager: Optional[AsyncBackendManager] = None
        self.logger = self._setup_logging()
        self._initialization_complete = False

    def _setup_logging(self) -> logging.Logger:
        """Setup application-level structured logging."""
        logger = logging.getLogger("TradingApplication")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def setup_dark_theme(self):
        """Setup enterprise-grade dark theme using resource manager."""
        try:
            # Load stylesheet from resources
            stylesheet = resource_manager.get_stylesheet(":/styles/dark_theme.qss")

            if stylesheet:
                self.app.setStyleSheet(stylesheet)
                self.logger.info("Dark theme loaded from resources")
            else:
                # Fallback to programmatic palette setup
                self._setup_fallback_dark_theme()
                self.logger.info("Using fallback dark theme")

        except Exception as e:
            self.logger.error(f"Error setting up dark theme: {e}")
            self._setup_fallback_dark_theme()

    def _setup_fallback_dark_theme(self):
        """Setup fallback dark theme using QPalette."""
        palette = QPalette()

        # Dark greyscale color scheme
        palette.setColor(QPalette.ColorRole.Window, QColor(43, 43, 43))  # #2b2b2b
        palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.Base, QColor(30, 30, 30))  # #1e1e1e
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(0, 0, 0))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(0, 0, 0))

        self.app.setPalette(palette)

    def setup_signal_handlers(self):
        """Setup enterprise-grade graceful shutdown signal handlers."""

        def signal_handler(signum, frame):
            self.logger.info(
                f"Received signal {signum}, initiating graceful shutdown..."
            )
            self._initiate_shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def _initiate_shutdown(self):
        """Initiate graceful application shutdown."""
        self.logger.info("Starting application shutdown sequence...")

        if self.main_window:
            self.main_window.close()

        if self.backend_manager:
            self.backend_manager.shutdown_backend()
        else:
            QApplication.quit()

    def _on_backend_initialized(self):
        """Handle backend initialization completion."""
        self.logger.info("Backend initialization completed, creating main window...")

        data_service = self.backend_manager.get_data_service()
        if data_service:
            self.main_window = MainWindow(data_service)
            self.main_window.show()
            self._initialization_complete = True
            self.logger.info("Application initialization complete")
        else:
            self.logger.error(
                "Backend initialization failed - no data service available"
            )
            self._initiate_shutdown()

    def _on_backend_error(self, error_message: str):
        """Handle backend initialization errors."""
        self.logger.error(f"Backend initialization error: {error_message}")
        self._initiate_shutdown()

    def _on_shutdown_complete(self):
        """Handle shutdown completion."""
        self.logger.info("Shutdown sequence complete")
        QApplication.quit()

    def run(self):
        """
        Enterprise-grade main application entry point with proper backend management.
        Ensures non-blocking initialization and graceful error handling.
        """
        try:
            self.logger.info("Starting Market Odds application...")

            # Use uvloop for better async performance on Unix systems
            try:
                if sys.platform != "win32":
                    import uvloop

                    uvloop.install()
                    self.logger.info("uvloop installed for enhanced async performance")
            except ImportError:
                self.logger.info(
                    "uvloop not available, using default asyncio event loop"
                )

            # Create Qt application
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Market Odds")
            self.app.setApplicationVersion("1.0.0")

            # Initialize enterprise-grade resource management
            if not resource_manager.initialize_resources():
                self.logger.warning(
                    "Resource manager initialization failed, using fallback"
                )

            # Set application font using resource manager
            app_font = resource_manager.load_font(":/fonts/SegoeUI.ttf", 9)
            if app_font:
                self.app.setFont(app_font)
                self.logger.info("Application font loaded from resources")
            else:
                # Fallback to system font
                font = QFont("Segoe UI", 9)
                self.app.setFont(font)
                self.logger.info("Using fallback system font")

            # Setup dark theme using resource manager
            self.setup_dark_theme()

            # Setup signal handlers
            self.setup_signal_handlers()

            # Create and configure backend manager
            self.backend_manager = AsyncBackendManager()
            self.backend_manager.backend_initialized.connect(
                self._on_backend_initialized
            )
            self.backend_manager.backend_error.connect(self._on_backend_error)
            self.backend_manager.shutdown_complete.connect(self._on_shutdown_complete)

            # Start backend initialization (non-blocking)
            self.backend_manager.initialize_backend()

            self.logger.info("Qt application starting...")

            # Start Qt event loop
            exit_code = self.app.exec()

            self.logger.info(f"Qt application exited with code: {exit_code}")
            return exit_code

        except Exception as e:
            self.logger.error(f"Critical application error: {e}")
            return 1
        finally:
            # Ensure cleanup
            if self.backend_manager:
                self.backend_manager.shutdown_backend()
            self.logger.info("Application cleanup complete")


if __name__ == "__main__":
    app = TradingApplication()
    sys.exit(app.run())

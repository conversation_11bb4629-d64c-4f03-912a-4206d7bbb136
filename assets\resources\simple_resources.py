# -*- coding: utf-8 -*-
"""
Simple resource loader for DataDriven Trading Platform.
Provides fallback resource loading when Qt resources are not available.
"""

from PyQt6.QtCore import QFile, QIODevice
from PyQt6.QtGui import QPixmap, QFont, QFontDatabase
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

def load_stylesheet(name: str = "dark_theme.qss") -> str:
    """Load stylesheet from resources directory."""
    try:
        stylesheet_path = Path(__file__).parent / name
        if stylesheet_path.exists():
            with open(stylesheet_path, 'r', encoding='utf-8') as f:
                return f.read()
        else:
            logger.warning(f"Stylesheet not found: {stylesheet_path}")
            return ""
    except Exception as e:
        logger.error(f"Failed to load stylesheet {name}: {e}")
        return ""

def load_icon(name: str) -> QPixmap:
    """Load icon from resources directory."""
    try:
        icon_path = Path(__file__).parent / "icons" / name
        if icon_path.exists():
            return QPixmap(str(icon_path))
        else:
            logger.warning(f"Icon not found: {icon_path}")
            return QPixmap()
    except Exception as e:
        logger.error(f"Failed to load icon {name}: {e}")
        return QPixmap()

def get_fallback_font(size: int = 9) -> QFont:
    """Get fallback system font."""
    # Try preferred fonts in order
    preferred_fonts = ["Segoe UI", "Source Sans Pro", "Inter", "Arial", "Helvetica"]
    
    for font_name in preferred_fonts:
        if QFontDatabase().hasFamily(font_name):
            logger.debug(f"Using system font: {font_name}")
            return QFont(font_name, size)
    
    # Final fallback
    logger.debug("Using default system font")
    return QFont("", size)

# Initialize simple resources
def initialize_simple_resources():
    """Initialize simple resource system."""
    logger.info("Simple resource system initialized")
    return True

# Auto-initialize
initialize_simple_resources()

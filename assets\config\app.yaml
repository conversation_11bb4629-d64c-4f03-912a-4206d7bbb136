# DataDriven Trading Platform Configuration
# Enterprise-grade application settings

# Application Settings
app:
  name: "DataDriven Trading Platform"
  version: "1.0.0"
  environment: "${ENVIRONMENT:development}"  # development, staging, production
  debug: "${DEBUG:false}"
  
# Logging Configuration
logging:
  level: "${LOG_LEVEL:INFO}"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  format: "structured"  # structured, simple
  correlation_enabled: true
  file_logging: "${FILE_LOGGING:false}"
  log_file: "logs/datadriven.log"
  max_file_size: "100MB"
  backup_count: 5

# Metrics and Monitoring
metrics:
  enabled: "${METRICS_ENABLED:true}"
  host: "${METRICS_HOST:0.0.0.0}"
  port: "${METRICS_PORT:8080}"
  path: "/metrics"
  collection_interval: 30  # seconds
  
  # Health check endpoints
  health:
    enabled: true
    path: "/health"
    timeout: 5  # seconds
  
  readiness:
    enabled: true
    path: "/ready"
    timeout: 5  # seconds
  
  info:
    enabled: true
    path: "/info"

# Performance Settings
performance:
  # Shared memory settings
  shared_memory:
    threshold_mb: "${SHARED_MEMORY_THRESHOLD:10}"
    cleanup_interval: 300  # seconds
    max_buffers: 1000
  
  # Thread pool settings
  thread_pools:
    cpu_workers: "${CPU_WORKERS:auto}"  # auto = CPU count
    io_workers: "${IO_WORKERS:auto}"    # auto = CPU count * 4
    max_queue_size: 10000
  
  # Cache settings
  cache:
    market_data_ttl: 300  # seconds
    indicator_cache_size: 1000
    chart_cache_size: 100

# Data Sources
data:
  # Market data provider
  provider: "yfinance"
  
  # Rate limiting
  rate_limit:
    requests_per_minute: 60
    burst_size: 10
  
  # Caching
  cache:
    enabled: true
    ttl: 300  # seconds
    max_size: "100MB"
  
  # Retry settings
  retry:
    max_attempts: 3
    backoff_factor: 2.0
    max_delay: 60  # seconds

# GUI Settings
gui:
  # Theme settings
  theme: "dark"
  font_family: "Segoe UI"
  font_size: 9
  
  # Window settings
  window:
    width: 1200
    height: 800
    resizable: true
    center_on_screen: true
  
  # Chart settings
  charts:
    fps_limit: 60
    max_data_points: 10000
    lazy_loading: true
    dirty_region_optimization: true
  
  # Tab settings
  tabs:
    lazy_loading: true
    cache_inactive: true
    max_cached: 5

# Security Settings
security:
  # API security
  api:
    rate_limiting: true
    cors_enabled: false
    allowed_origins: []
  
  # Data validation
  validation:
    strict_mode: true
    sanitize_inputs: true
    max_request_size: "10MB"

# Development Settings (only in development environment)
development:
  # Hot reload
  hot_reload: "${HOT_RELOAD:false}"
  
  # Debug features
  debug:
    show_performance_overlay: false
    log_sql_queries: false
    enable_profiling: false
  
  # Testing
  testing:
    mock_data_sources: false
    fast_startup: false

# Production Settings (only in production environment)
production:
  # Optimization
  optimization:
    precompile_templates: true
    enable_compression: true
    minify_assets: true
  
  # Monitoring
  monitoring:
    error_reporting: true
    performance_tracking: true
    user_analytics: false
  
  # Backup
  backup:
    enabled: true
    interval: "24h"
    retention: "30d"

# Feature Flags
features:
  # Experimental features
  experimental:
    new_chart_engine: false
    advanced_indicators: true
    real_time_streaming: false
  
  # Beta features
  beta:
    portfolio_tracking: false
    options_analysis: true
    backtesting: false
  
  # Premium features
  premium:
    advanced_analytics: false
    custom_indicators: false
    api_access: false

# Integration Settings
integrations:
  # External services
  services:
    prometheus:
      enabled: "${PROMETHEUS_ENABLED:true}"
      pushgateway_url: "${PROMETHEUS_PUSHGATEWAY_URL:}"
    
    sentry:
      enabled: "${SENTRY_ENABLED:false}"
      dsn: "${SENTRY_DSN:}"
    
    datadog:
      enabled: "${DATADOG_ENABLED:false}"
      api_key: "${DATADOG_API_KEY:}"

# Resource Limits
limits:
  # Memory limits
  memory:
    max_heap_size: "2GB"
    max_shared_memory: "1GB"
    gc_threshold: "1.5GB"
  
  # CPU limits
  cpu:
    max_cpu_percent: 80
    thread_limit: 100
  
  # Network limits
  network:
    max_connections: 1000
    timeout: 30  # seconds
    max_bandwidth: "100MB/s"

# Cleanup and Maintenance
maintenance:
  # Automatic cleanup
  cleanup:
    temp_files: true
    old_logs: true
    cache_files: true
    interval: "1h"
  
  # Health checks
  health_checks:
    interval: 60  # seconds
    timeout: 5   # seconds
    retries: 3
  
  # Graceful shutdown
  shutdown:
    timeout: 30  # seconds
    force_after: 60  # seconds

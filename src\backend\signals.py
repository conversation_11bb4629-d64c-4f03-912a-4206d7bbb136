"""
Enterprise-grade signal definitions with proper typing and thread safety.
Implements well-typed signals and queued connections for cross-thread communication.
"""

from PyQt6.QtCore import QObject, pyqtSignal, Qt
from typing import Dict, Any, List, Optional, Union
import logging


class MarketDataSignals(QObject):
    """
    Well-typed signals for market data operations.
    Ensures type safety and proper thread-safe communication.
    """

    # Data fetch signals with specific types
    data_fetch_started = pyqtSignal(str)  # ticker: str
    data_fetch_progress = pyqtSignal(str, str)  # ticker: str, status: str
    data_fetch_completed = pyqtSignal(str, dict)  # ticker: str, data: Dict[str, Any]
    data_fetch_failed = pyqtSignal(str, str)  # ticker: str, error: str

    # Chart update signals
    chart_data_updated = pyqtSignal(dict)  # chart_data: Dict[str, Any]
    chart_bounds_changed = pyqtSignal(dict)  # bounds: Dict[str, float]
    chart_settings_changed = pyqtSignal(dict)  # settings: Dict[str, Any]

    # UI state signals
    drawing_tool_changed = pyqtSignal(str)  # tool_name: str
    crosshair_position_changed = pyqtSignal(float, float)  # x: float, y: float
    chart_range_changed = pyqtSignal(dict)  # range_info: Dict[str, float]

    # Performance monitoring signals
    render_time_measured = pyqtSignal(str, float)  # operation: str, time_ms: float
    memory_usage_updated = pyqtSignal(int)  # memory_mb: int


class ChartVisualizationSignals(QObject):
    """
    Well-typed signals for chart visualization operations.
    Handles rendering and display state changes.
    """

    # Rendering signals
    candlesticks_updated = pyqtSignal(list)  # candlestick_data: List[Dict]
    indicators_updated = pyqtSignal(dict)  # indicator_data: Dict[str, Any]
    annotations_updated = pyqtSignal(list)  # annotation_data: List[Dict]

    # Visibility signals
    element_visibility_changed = pyqtSignal(
        str, bool
    )  # element_name: str, visible: bool
    chart_visibility_changed = pyqtSignal(str, bool)  # chart_name: str, visible: bool

    # Interaction signals
    mouse_position_changed = pyqtSignal(
        float, float, str
    )  # x: float, y: float, chart: str
    selection_changed = pyqtSignal(list)  # selected_items: List[str]
    zoom_changed = pyqtSignal(dict)  # zoom_info: Dict[str, float]


class BackendServiceSignals(QObject):
    """
    Well-typed signals for backend service communication.
    Ensures proper async operation coordination.
    """

    # Service lifecycle signals
    service_initialized = pyqtSignal()
    service_shutdown_started = pyqtSignal()
    service_shutdown_completed = pyqtSignal()
    service_error = pyqtSignal(str, str)  # service_name: str, error: str

    # Computation signals
    computation_started = pyqtSignal(str, dict)  # operation: str, params: Dict
    computation_progress = pyqtSignal(str, float)  # operation: str, progress: float
    computation_completed = pyqtSignal(str, dict)  # operation: str, result: Dict
    computation_failed = pyqtSignal(str, str)  # operation: str, error: str

    # Health monitoring signals
    health_check_completed = pyqtSignal(dict)  # health_status: Dict[str, Any]
    performance_metrics_updated = pyqtSignal(dict)  # metrics: Dict[str, float]


class SignalManager(QObject):
    """
    Enterprise-grade signal manager that handles proper connection management
    and ensures thread-safe communication across the application.
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()

        # Initialize signal groups
        self.market_data = MarketDataSignals(self)
        self.chart_visualization = ChartVisualizationSignals(self)
        self.backend_service = BackendServiceSignals(self)

        # Connection tracking for debugging
        self._connections = []

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for signal management."""
        logger = logging.getLogger("SignalManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def connect_threadsafe(
        self, signal, slot, connection_type=Qt.ConnectionType.QueuedConnection
    ):
        """
        Create thread-safe signal-slot connections with proper typing.
        Uses queued connections by default for cross-thread safety.
        """
        try:
            connection = signal.connect(slot, connection_type)
            self._connections.append(
                {
                    "signal": signal,
                    "slot": slot,
                    "connection": connection,
                    "type": connection_type,
                }
            )

            self.logger.debug(
                f"Connected signal {signal} to slot {slot} with type {connection_type}"
            )
            return connection

        except Exception as e:
            self.logger.error(f"Failed to connect signal {signal} to slot {slot}: {e}")
            raise

    def disconnect_all(self):
        """Disconnect all managed signal-slot connections."""
        disconnected_count = 0

        for connection_info in self._connections:
            try:
                signal = connection_info["signal"]
                slot = connection_info["slot"]
                signal.disconnect(slot)
                disconnected_count += 1
            except Exception as e:
                self.logger.warning(f"Failed to disconnect signal: {e}")

        self._connections.clear()
        self.logger.info(f"Disconnected {disconnected_count} signal-slot connections")

    def get_connection_count(self) -> int:
        """Get the number of active signal-slot connections."""
        return len(self._connections)

    def emit_with_error_handling(self, signal, *args, **kwargs):
        """
        Emit signal with comprehensive error handling and logging.
        Prevents signal emission errors from crashing the application.
        """
        try:
            signal.emit(*args, **kwargs)
            self.logger.debug(f"Signal {signal} emitted successfully")
        except Exception as e:
            self.logger.error(f"Error emitting signal {signal}: {e}")
            # Don't re-raise to prevent application crashes


class TypedSlotDecorator:
    """
    Decorator for creating well-typed slots with automatic error handling.
    Ensures slots receive properly typed arguments and handle errors gracefully.
    """

    @staticmethod
    def typed_slot(*arg_types):
        """
        Decorator that validates slot argument types and provides error handling.

        Usage:
            @TypedSlotDecorator.typed_slot(str, dict)
            def on_data_received(self, ticker: str, data: Dict[str, Any]):
                pass
        """

        def decorator(func):
            def wrapper(self, *args, **kwargs):
                try:
                    # Type validation
                    if len(args) != len(arg_types):
                        raise TypeError(
                            f"Expected {len(arg_types)} arguments, got {len(args)}"
                        )

                    for i, (arg, expected_type) in enumerate(zip(args, arg_types)):
                        if not isinstance(arg, expected_type):
                            raise TypeError(
                                f"Argument {i} expected {expected_type}, got {type(arg)}"
                            )

                    # Execute the slot
                    return func(self, *args, **kwargs)

                except Exception as e:
                    logger = logging.getLogger(
                        f"{self.__class__.__name__}.{func.__name__}"
                    )
                    logger.error(f"Slot execution error: {e}")
                    # Don't re-raise to prevent application crashes

            return wrapper

        return decorator


# Global signal manager instance for application-wide use
signal_manager = SignalManager()

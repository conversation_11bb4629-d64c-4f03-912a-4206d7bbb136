"""
Enterprise-grade resource manager for efficient asset loading and caching.
Implements Qt Resource system with memory optimization and lazy loading.
"""

import logging
from typing import Dict, Optional, Any
from pathlib import Path
from PyQt6.QtCore import QResource, QFile, QIODevice
from PyQt6.QtGui import QPixmap, QIcon, QFont, QFontDatabase
from PyQt6.QtWidgets import QApplication
import weakref


class ResourceManager:
    """
    Enterprise-grade resource manager that handles efficient loading and caching
    of application assets using Qt's resource system.
    """

    def __init__(self):
        self.logger = self._setup_logging()
        self._pixmap_cache: Dict[str, QPixmap] = {}
        self._icon_cache: Dict[str, QIcon] = {}
        self._font_cache: Dict[str, QFont] = {}
        self._stylesheet_cache: Dict[str, str] = {}
        self._resource_loaded = False

        # Weak reference cache for automatic cleanup
        self._weak_cache: Dict[str, Any] = weakref.WeakValueDictionary()

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for resource management."""
        logger = logging.getLogger("ResourceManager")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def initialize_resources(self) -> bool:
        """
        Initialize Qt resource system and load resource files.
        Returns True if successful, False otherwise.
        """
        try:
            # Register resource files
            resource_path = Path(__file__).parent.parent / "resources"

            # Check if resource files exist
            qrc_files = list(resource_path.glob("*.qrc"))
            if not qrc_files:
                self.logger.warning(
                    "No .qrc resource files found, using fallback loading"
                )
                return self._initialize_fallback_resources()

            # Load compiled resource files (.rcc)
            rcc_files = list(resource_path.glob("*.rcc"))
            for rcc_file in rcc_files:
                if QResource.registerResource(str(rcc_file)):
                    self.logger.info(f"Loaded resource file: {rcc_file.name}")
                else:
                    self.logger.warning(
                        f"Failed to load resource file: {rcc_file.name}"
                    )

            self._resource_loaded = True
            self.logger.info("Qt resource system initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Qt resources: {e}")
            return self._initialize_fallback_resources()

    def _initialize_fallback_resources(self) -> bool:
        """Initialize fallback resource loading from filesystem."""
        try:
            self.logger.info("Using fallback filesystem resource loading")

            # Try to import compiled resources as fallback
            try:
                import resources.app_resources_rc

                self.logger.info("Compiled app resources loaded")
                self._resource_loaded = True
                return True
            except ImportError:
                self.logger.debug("Compiled app resources not available")

            try:
                import resources.simple_resources

                self.logger.info("Simple resource loader available")
            except ImportError:
                self.logger.debug("Simple resource loader not available")

            self._resource_loaded = False
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize fallback resources: {e}")
            return False

    def get_pixmap(
        self, resource_path: str, size: Optional[tuple] = None
    ) -> Optional[QPixmap]:
        """
        Get pixmap from resources with caching and size optimization.

        Args:
            resource_path: Resource path (e.g., ":/icons/settings.svg")
            size: Optional tuple (width, height) for scaling

        Returns:
            QPixmap or None if loading failed
        """
        cache_key = f"{resource_path}_{size}" if size else resource_path

        # Check cache first
        if cache_key in self._pixmap_cache:
            return self._pixmap_cache[cache_key]

        try:
            pixmap = None

            if self._resource_loaded and resource_path.startswith(":/"):
                # Load from Qt resources
                pixmap = QPixmap(resource_path)
            else:
                # Fallback to filesystem
                fallback_path = self._get_fallback_path(resource_path)
                if fallback_path and fallback_path.exists():
                    pixmap = QPixmap(str(fallback_path))

            if pixmap and not pixmap.isNull():
                # Scale if size specified
                if size:
                    pixmap = pixmap.scaled(
                        size[0],
                        size[1],
                        aspectRatioMode=1,  # KeepAspectRatio
                        transformMode=1,
                    )  # SmoothTransformation

                # Cache the pixmap
                self._pixmap_cache[cache_key] = pixmap
                self.logger.debug(f"Loaded and cached pixmap: {resource_path}")
                return pixmap
            else:
                self.logger.warning(f"Failed to load pixmap: {resource_path}")
                return None

        except Exception as e:
            self.logger.error(f"Error loading pixmap {resource_path}: {e}")
            return None

    def get_icon(
        self, resource_path: str, size: Optional[tuple] = None
    ) -> Optional[QIcon]:
        """
        Get icon from resources with caching.

        Args:
            resource_path: Resource path (e.g., ":/icons/settings.svg")
            size: Optional tuple (width, height) for preferred size

        Returns:
            QIcon or None if loading failed
        """
        cache_key = f"{resource_path}_{size}" if size else resource_path

        # Check cache first
        if cache_key in self._icon_cache:
            return self._icon_cache[cache_key]

        try:
            pixmap = self.get_pixmap(resource_path, size)
            if pixmap:
                icon = QIcon(pixmap)
                self._icon_cache[cache_key] = icon
                return icon
            else:
                return None

        except Exception as e:
            self.logger.error(f"Error creating icon from {resource_path}: {e}")
            return None

    def get_stylesheet(self, resource_path: str) -> str:
        """
        Get stylesheet content from resources with caching.

        Args:
            resource_path: Resource path (e.g., ":/styles/dark_theme.qss")

        Returns:
            Stylesheet content as string
        """
        # Check cache first
        if resource_path in self._stylesheet_cache:
            return self._stylesheet_cache[resource_path]

        try:
            content = ""

            if self._resource_loaded and resource_path.startswith(":/"):
                # Load from Qt resources
                file = QFile(resource_path)
                if file.open(QIODevice.OpenModeFlag.ReadOnly):
                    content = file.readAll().data().decode("utf-8")
                    file.close()
            else:
                # Fallback to filesystem
                fallback_path = self._get_fallback_path(resource_path)
                if fallback_path and fallback_path.exists():
                    content = fallback_path.read_text(encoding="utf-8")

            if content:
                # Cache the stylesheet
                self._stylesheet_cache[resource_path] = content
                self.logger.debug(f"Loaded and cached stylesheet: {resource_path}")
                return content
            else:
                self.logger.warning(f"Failed to load stylesheet: {resource_path}")
                return ""

        except Exception as e:
            self.logger.error(f"Error loading stylesheet {resource_path}: {e}")
            return ""

    def load_font(self, resource_path: str, point_size: int = 9) -> Optional[QFont]:
        """
        Load font from resources with caching.

        Args:
            resource_path: Resource path (e.g., ":/fonts/SegoeUI.ttf")
            point_size: Font size in points

        Returns:
            QFont or None if loading failed
        """
        cache_key = f"{resource_path}_{point_size}"

        # Check cache first
        if cache_key in self._font_cache:
            return self._font_cache[cache_key]

        try:
            font_id = -1
            self.logger.debug(f"Attempting to load font: {resource_path}")

            if self._resource_loaded and resource_path.startswith(":/"):
                # Load from Qt resources
                self.logger.debug("Loading from Qt resources")
                font_id = QFontDatabase().addApplicationFont(resource_path)
            else:
                # Fallback to filesystem
                self.logger.debug("Loading from filesystem fallback")
                fallback_path = self._get_fallback_path(resource_path)
                if fallback_path and fallback_path.exists():
                    self.logger.debug(f"Fallback path exists: {fallback_path}")
                    font_id = QFontDatabase().addApplicationFont(str(fallback_path))
                else:
                    self.logger.debug(f"Fallback path not found: {fallback_path}")

            if font_id != -1:
                self.logger.debug(f"Font loaded successfully with ID: {font_id}")
                font_families = QFontDatabase().applicationFontFamilies(font_id)
                if font_families:
                    font = QFont(font_families[0], point_size)
                    self._font_cache[cache_key] = font
                    self.logger.debug(f"Loaded and cached font: {resource_path}")
                    return font

            # Graceful fallback to system fonts
            self.logger.debug(
                f"Font resource not found: {resource_path}, using system fallback"
            )

            # Try preferred system fonts
            preferred_fonts = [
                "Segoe UI",
                "Source Sans Pro",
                "Inter",
                "Arial",
                "Helvetica",
                "DejaVu Sans",
            ]
            for font_name in preferred_fonts:
                if QFontDatabase().hasFamily(font_name):
                    font = QFont(font_name, point_size)
                    self._font_cache[cache_key] = font
                    self.logger.debug(f"Using system font fallback: {font_name}")
                    return font

            # Final fallback to default system font
            font = QFont("", point_size)  # Empty string uses system default
            self._font_cache[cache_key] = font
            self.logger.debug("Using default system font")
            return font

        except Exception as e:
            self.logger.error(f"Error loading font {resource_path}: {e}")
            return None

    def _get_fallback_path(self, resource_path: str) -> Optional[Path]:
        """Convert Qt resource path to filesystem path for fallback loading."""
        if resource_path.startswith(":/"):
            # Remove :/ prefix and convert to filesystem path
            relative_path = resource_path[2:]
            base_path = Path(__file__).parent.parent / "resources"
            return base_path / relative_path
        else:
            return Path(resource_path)

    def clear_cache(self):
        """Clear all cached resources to free memory."""
        self._pixmap_cache.clear()
        self._icon_cache.clear()
        self._font_cache.clear()
        self._stylesheet_cache.clear()
        self._weak_cache.clear()
        self.logger.info("Resource cache cleared")

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics for monitoring."""
        return {
            "pixmaps": len(self._pixmap_cache),
            "icons": len(self._icon_cache),
            "fonts": len(self._font_cache),
            "stylesheets": len(self._stylesheet_cache),
            "weak_refs": len(self._weak_cache),
        }


# Global resource manager instance
resource_manager = ResourceManager()

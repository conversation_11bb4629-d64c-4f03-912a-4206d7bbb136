"""
Volatility Calculations Service - Backend service for volatility chart calculations
Moved from frontend VolatilityChart.update_data to separate calculation logic from visualization
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import indicators


class VolatilityCalculationsService:
    """Service for performing volatility chart calculations."""

    def __init__(self, data_service=None):
        self.logger = logging.getLogger(__name__)
        self.data_service = data_service
        
    def calculate_volatility_data(self, filtered_high_data: List, filtered_low_data: List, 
                                market_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Calculate all volatility chart data including statistics and positioning.
        
        Args:
            filtered_high_data: List of filtered high data rows
            filtered_low_data: List of filtered low data rows
            market_data: Market data dictionary containing ticker, timeframe, etc.
            
        Returns:
            Dictionary containing all calculated values for visualization
        """
        try:
            result = {
                'title_info': {},
                'current_close': None,
                'max_high': None,
                'max_low': None,
                'apex': None,
                'apex_high_mean': None,
                'apex_low_mean': None,
                'highs_stats': {},
                'lows_stats': {},
                'all_highs': [],
                'all_lows': [],
                'arrow_sizes': (1.5, 0.05),  # Default fallback
                'axis_limits': {'x_min': -4, 'x_max': 4, 'y_min': 0, 'y_max': 100},
                'title_position': {'x': -3.75, 'y': 0}
            }
            
            # Calculate title information
            if market_data:
                result['title_info'] = self._calculate_title_info(
                    filtered_high_data, filtered_low_data, market_data
                )
            
            # Get current close price
            result['current_close'] = self._extract_current_close(market_data)
            
            # Extract and calculate high/low statistics using C++ kernels
            all_highs, max_high = self._extract_highs_data(filtered_high_data)
            all_lows, max_low = self._extract_lows_data(filtered_low_data)
            
            result['all_highs'] = all_highs
            result['all_lows'] = all_lows
            result['max_high'] = max_high
            result['max_low'] = max_low
            
            # Calculate apex and derived values using C++ kernels
            if max_high is not None and max_low is not None:
                apex_values = self._calculate_apex_values(max_high, max_low)
                result.update(apex_values)
            
            # Calculate statistical values
            result['highs_stats'] = self._calculate_highs_statistics(all_highs)
            result['lows_stats'] = self._calculate_lows_statistics(all_lows)
            
            # Calculate dynamic arrow sizes
            result['arrow_sizes'] = self._calculate_arrow_sizes(all_highs, all_lows)
            
            # Calculate axis limits and positioning using C++ kernels
            if max_high is not None and max_low is not None:
                result['axis_limits'] = self._calculate_axis_limits(max_high, max_low)
                result['title_position'] = self._calculate_title_position(max_high, max_low)
            
            self.logger.debug(f"Volatility calculations completed successfully")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in volatility calculations: {e}")
            import traceback
            traceback.print_exc()
            return result
    
    def _calculate_title_info(self, filtered_high_data: List, filtered_low_data: List, 
                            market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate title information including occurrences."""
        try:
            ticker = market_data.get("ticker", "Unknown")
            timeframe = market_data.get("timeframe", "Unknown")
            dtl = market_data.get("dtl", 0)
            length = market_data.get("length", 0)
            
            # Calculate occurrences using the formula: (occurrences / 2) - 1
            total_occurrences = 0
            if filtered_high_data:
                total_occurrences += len(filtered_high_data)
            if filtered_low_data:
                total_occurrences += len(filtered_low_data)
            
            occurrences = int((total_occurrences / 2) - 1) if total_occurrences > 0 else 0
            
            return {
                'ticker': ticker,
                'timeframe': timeframe,
                'dtl': dtl,
                'length': length,
                'occurrences': occurrences,
                'title_text': f'{ticker} Length: [{length}] {timeframe} Aggregations: {dtl}\noccurrence: {occurrences}'
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating title info: {e}")
            return {}
    
    def _extract_current_close(self, market_data: Optional[Dict[str, Any]]) -> Optional[float]:
        """Extract current close price from market data."""
        try:
            if not market_data:
                return None
                
            close_prices = market_data.get("close", [])
            if close_prices is not None and len(close_prices) > 0:
                if hasattr(close_prices, '__len__') and len(close_prices) > 0:
                    return float(close_prices[-1])
            return None
            
        except Exception as e:
            self.logger.error(f"Error extracting current close: {e}")
            return None
    
    def _extract_highs_data(self, filtered_high_data: List) -> Tuple[List[float], Optional[float]]:
        """Extract all highs and find max high from filtered data using C++ computation only."""
        cpp_result = indicators.extract_highs_data_cpp(filtered_high_data)
        if cpp_result.get("success", False):
            data = cpp_result["data"]
            all_highs = data["all_highs"]
            max_high = data["max_high"] if data["has_max"] else None
            return all_highs, max_high
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ highs extraction failed: {error_msg}")
            raise RuntimeError(f"Failed to extract highs data: {error_msg}")
    
    def _extract_lows_data(self, filtered_low_data: List) -> Tuple[List[float], Optional[float]]:
        """Extract all lows and find max low (lowest value) from filtered data using C++ computation only."""
        cpp_result = indicators.extract_lows_data_cpp(filtered_low_data)
        if cpp_result.get("success", False):
            data = cpp_result["data"]
            all_lows = data["all_lows"]
            max_low = data["max_low"] if data["has_max"] else None
            return all_lows, max_low
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ lows extraction failed: {error_msg}")
            raise RuntimeError(f"Failed to extract lows data: {error_msg}")

    def _calculate_apex_values(self, max_high: float, max_low: float) -> Dict[str, float]:
        """Calculate apex and derived values using C++ computation only."""
        cpp_result = indicators.calculate_apex_values_cpp(max_high, max_low)
        if cpp_result.get("success", False):
            return cpp_result["data"]
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ apex calculation failed: {error_msg}")
            raise RuntimeError(f"Failed to calculate apex values: {error_msg}")

    def _calculate_highs_statistics(self, all_highs: List[float]) -> Dict[str, float]:
        """Calculate statistical values for highs using C++ computation only."""
        cpp_result = indicators.calculate_statistics_cpp(all_highs)
        if cpp_result.get("success", False):
            return cpp_result["data"]
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ highs statistics failed: {error_msg}")
            raise RuntimeError(f"Failed to calculate highs statistics: {error_msg}")

    def _calculate_lows_statistics(self, all_lows: List[float]) -> Dict[str, float]:
        """Calculate statistical values for lows using C++ computation only."""
        cpp_result = indicators.calculate_statistics_cpp(all_lows)
        if cpp_result.get("success", False):
            return cpp_result["data"]
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ lows statistics failed: {error_msg}")
            raise RuntimeError(f"Failed to calculate lows statistics: {error_msg}")

    def _calculate_arrow_sizes(self, all_highs: List[float], all_lows: List[float]) -> Tuple[float, float]:
        """Calculate dynamic arrow sizes based on projected high/low range."""
        if self.data_service:
            return self.data_service.calculate_arrow_sizes(all_highs, all_lows)
        else:
            self.logger.error("DataService not available for arrow size calculation")
            return 1.5, 0.05  # Default fallback

    def _calculate_axis_limits(self, max_high: float, max_low: float) -> Dict[str, float]:
        """Calculate axis limits with padding using C++ computation only."""
        cpp_result = indicators.calculate_axis_limits_cpp(max_high, max_low)
        if cpp_result.get("success", False):
            return cpp_result["data"]
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ axis limits calculation failed: {error_msg}")
            raise RuntimeError(f"Failed to calculate axis limits: {error_msg}")

    def _calculate_title_position(self, max_high: float, max_low: float) -> Dict[str, float]:
        """Calculate title position at 7.5% above max_high using C++ computation only."""
        cpp_result = indicators.calculate_title_position_cpp(max_high, max_low)
        if cpp_result.get("success", False):
            return cpp_result["data"]
        else:
            error_msg = cpp_result.get('error_message', 'Unknown error')
            self.logger.error(f"C++ title position calculation failed: {error_msg}")
            raise RuntimeError(f"Failed to calculate title position: {error_msg}")

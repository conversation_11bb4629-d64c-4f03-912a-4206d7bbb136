<!DOCTYPE RCC>
<RCC version="1.0">
    <qresource prefix="/icons">
        <!-- Chart tool icons -->
        <file>mouse.svg</file>
        <file>line.svg</file>
        <file>vertical_line.svg</file>
        <file>horizontal_line.svg</file>
        <file>rectangle.svg</file>
        <file>text.svg</file>
        <file>eraser.svg</file>
        <file>undo.svg</file>
        <file>redo.svg</file>
        <file>clear.svg</file>
        <file>settings.svg</file>
        
        <!-- Chart state icons -->
        <file>visible.svg</file>
        <file>hidden.svg</file>
        <file>loading.svg</file>
        <file>error.svg</file>
        <file>success.svg</file>
        
        <!-- Application icons -->
        <file>app_icon.svg</file>
        <file>logo.svg</file>
    </qresource>
    
    <qresource prefix="/styles">
        <!-- Stylesheet files -->
        <file>dark_theme.qss</file>
        <file>chart_styles.qss</file>
        <file>button_styles.qss</file>
    </qresource>
    
    <qresource prefix="/fonts">
        <!-- Custom fonts -->
        <file>SegoeUI.ttf</file>
        <file>SegoeUI-Bold.ttf</file>
        <file>SegoeUI-Light.ttf</file>
    </qresource>
</RCC>

/* Enterprise-grade dark theme stylesheet for Market Odds application */

/* Main application styling */
QMainWindow {
    background-color: #1e1e1e;
    color: #ffffff;
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

QWidget {
    background-color: #1e1e1e;
    color: #ffffff;
    border: none;
}

/* Tab widget styling */
QTabWidget::pane {
    border: 1px solid #3c3c3c;
    background-color: #2d2d2d;
}

QTabWidget::tab-bar {
    alignment: left;
}

QTabBar::tab {
    background-color: #3c3c3c;
    color: #ffffff;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

QTabBar::tab:selected {
    background-color: #0078d4;
    color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #4c4c4c;
}

/* Button styling */
QPushButton {
    background-color: #0078d4;
    color: #ffffff;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #106ebe;
}

QPushButton:pressed {
    background-color: #005a9e;
}

QPushButton:disabled {
    background-color: #3c3c3c;
    color: #8c8c8c;
}

/* Specialized button styles */
QPushButton.drawing-tool {
    background-color: #2d2d2d;
    border: 1px solid #5c5c5c;
    min-width: 60px;
    padding: 6px 12px;
}

QPushButton.drawing-tool:checked {
    background-color: #0078d4;
    border-color: #0078d4;
}

QPushButton.drawing-tool:hover {
    background-color: #3c3c3c;
    border-color: #6c6c6c;
}

QPushButton.settings-button {
    background-color: #2d2d2d;
    border: 1px solid #5c5c5c;
    border-radius: 6px;
    padding: 10px;
}

QPushButton.settings-button:hover {
    background-color: #3c3c3c;
    border-color: #0078d4;
}

/* ComboBox styling */
QComboBox {
    background-color: #2d2d2d;
    border: 1px solid #5c5c5c;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(:/icons/dropdown.svg);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    border: 1px solid #5c5c5c;
    selection-background-color: #0078d4;
    color: #ffffff;
}

/* SpinBox styling */
QSpinBox {
    background-color: #2d2d2d;
    border: 1px solid #5c5c5c;
    border-radius: 4px;
    padding: 6px;
    min-width: 80px;
}

QSpinBox:hover {
    border-color: #0078d4;
}

QSpinBox::up-button, QSpinBox::down-button {
    background-color: #3c3c3c;
    border: none;
    width: 16px;
}

QSpinBox::up-button:hover, QSpinBox::down-button:hover {
    background-color: #4c4c4c;
}

/* CheckBox styling */
QCheckBox {
    color: #ffffff;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #5c5c5c;
    border-radius: 3px;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #0078d4;
    border-color: #0078d4;
    image: url(:/icons/check.svg);
}

QCheckBox::indicator:hover {
    border-color: #0078d4;
}

/* Label styling */
QLabel {
    color: #ffffff;
    background-color: transparent;
}

QLabel.section-header {
    font-weight: 600;
    font-size: 10pt;
    color: #ffffff;
    padding: 8px 0px 4px 0px;
}

QLabel.info-label {
    color: #cccccc;
    font-size: 8pt;
}

/* Dialog styling */
QDialog {
    background-color: #1e1e1e;
    border: 1px solid #3c3c3c;
}

/* Scroll area styling */
QScrollArea {
    background-color: #1e1e1e;
    border: none;
}

QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #5c5c5c;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #6c6c6c;
}

/* Splitter styling */
QSplitter::handle {
    background-color: #3c3c3c;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

/* Frame styling */
QFrame {
    background-color: #2d2d2d;
    border: 1px solid #3c3c3c;
    border-radius: 4px;
}

QFrame.control-panel {
    background-color: #252525;
    border: 1px solid #3c3c3c;
    border-radius: 6px;
    padding: 8px;
}

/* Status and progress indicators */
.status-indicator {
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 8pt;
    font-weight: 500;
}

.status-success {
    background-color: #107c10;
    color: #ffffff;
}

.status-error {
    background-color: #d13438;
    color: #ffffff;
}

.status-warning {
    background-color: #ff8c00;
    color: #ffffff;
}

.status-info {
    background-color: #0078d4;
    color: #ffffff;
}

"""
Qt Model/View architecture for chart data.
Implements proper separation of concerns with zero UI computation.
"""

from PyQt6.QtCore import (
    QAbstractTableModel,
    QAbstractListModel,
    QModelIndex,
    Qt,
    pyqtSignal,
    QObject,
    QTimer,
)
from PyQt6.QtGui import QColor, QPen, QBrush
from typing import Dict, Any, List, Optional, Tuple, Union
import logging
from dataclasses import dataclass
from enum import Enum


class ChartElementType(Enum):
    """Enumeration of chart element types for type-safe operations."""

    CANDLESTICK = "candlestick"
    INDICATOR_LINE = "indicator_line"
    ANNOTATION = "annotation"
    INFINITY_LINE = "infinity_line"
    PEAK_TROUGH = "peak_trough"


@dataclass
class ChartElement:
    """
    Immutable data structure for chart elements.
    All rendering data pre-computed in backend.
    """

    element_id: str
    element_type: ChartElementType
    render_data: Dict[str, Any]
    bounds: Dict[str, float]
    visible: bool = True
    z_order: int = 0


class ChartDataModel(QAbstractTableModel):
    """
    Chart data model implementing Qt's Model/View framework.
    Eliminates all manual widget updates and UI-side computations.
    """

    # Well-typed signals for data changes
    element_added = pyqtSignal(str, ChartElementType)  # element_id, type
    element_removed = pyqtSignal(str)  # element_id
    element_updated = pyqtSignal(str, dict)  # element_id, render_data
    bounds_changed = pyqtSignal(dict)  # new_bounds
    dirty_region_changed = pyqtSignal(dict)  # dirty_region

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = self._setup_logging()

        # Core data storage - immutable elements
        self._elements: Dict[str, ChartElement] = {}
        self._element_order: List[str] = []

        # Cached bounds and dirty regions for efficient rendering
        self._cached_bounds: Dict[str, float] = {}
        self._dirty_regions: List[Dict[str, float]] = []

        # Performance optimization: batch updates
        self._batch_update_timer = QTimer()
        self._batch_update_timer.setSingleShot(True)
        self._batch_update_timer.timeout.connect(self._process_batch_updates)
        self._pending_updates: List[str] = []

    def _setup_logging(self) -> logging.Logger:
        """Setup structured logging for model operations."""
        logger = logging.getLogger("ChartDataModel")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "module": "%(name)s", '
                '"level": "%(levelname)s", "message": "%(message)s"}'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def rowCount(self, parent=QModelIndex()) -> int:
        """Return number of chart elements."""
        return len(self._elements)

    def columnCount(self, parent=QModelIndex()) -> int:
        """Return number of data columns (fixed for chart elements)."""
        return 4  # id, type, visible, z_order

    def data(self, index: QModelIndex, role: int = Qt.ItemDataRole.DisplayRole) -> Any:
        """Return data for given index and role."""
        if not index.isValid() or index.row() >= len(self._element_order):
            return None

        element_id = self._element_order[index.row()]
        element = self._elements[element_id]

        if role == Qt.ItemDataRole.DisplayRole:
            column = index.column()
            if column == 0:
                return element.element_id
            elif column == 1:
                return element.element_type.value
            elif column == 2:
                return element.visible
            elif column == 3:
                return element.z_order

        elif role == Qt.ItemDataRole.UserRole:
            # Return full element for custom rendering
            return element

        return None

    def headerData(
        self,
        section: int,
        orientation: Qt.Orientation,
        role: int = Qt.ItemDataRole.DisplayRole,
    ) -> Any:
        """Return header data."""
        if (
            role == Qt.ItemDataRole.DisplayRole
            and orientation == Qt.Orientation.Horizontal
        ):
            headers = ["ID", "Type", "Visible", "Z-Order"]
            if 0 <= section < len(headers):
                return headers[section]
        return None

    def add_element(self, element: ChartElement) -> bool:
        """
        Add chart element to model with proper change notifications.
        Returns True if successful, False if element already exists.
        """
        if element.element_id in self._elements:
            self.logger.warning(f"Element {element.element_id} already exists")
            return False

        try:
            # Begin model update
            self.beginInsertRows(
                QModelIndex(), len(self._elements), len(self._elements)
            )

            # Add element
            self._elements[element.element_id] = element
            self._element_order.append(element.element_id)

            # Update cached bounds
            self._update_bounds_cache(element)

            # End model update
            self.endInsertRows()

            # Emit signals for view updates
            self.element_added.emit(element.element_id, element.element_type)
            self._schedule_batch_update(element.element_id)

            self.logger.debug(f"Added element: {element.element_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to add element {element.element_id}: {e}")
            return False

    def remove_element(self, element_id: str) -> bool:
        """
        Remove chart element from model with proper change notifications.
        Returns True if successful, False if element doesn't exist.
        """
        if element_id not in self._elements:
            self.logger.warning(f"Element {element_id} not found")
            return False

        try:
            # Find row index
            row = self._element_order.index(element_id)

            # Begin model update
            self.beginRemoveRows(QModelIndex(), row, row)

            # Remove element
            del self._elements[element_id]
            self._element_order.remove(element_id)

            # Update cached bounds
            self._recalculate_bounds_cache()

            # End model update
            self.endRemoveRows()

            # Emit signals for view updates
            self.element_removed.emit(element_id)

            self.logger.debug(f"Removed element: {element_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to remove element {element_id}: {e}")
            return False

    def update_element(self, element_id: str, render_data: Dict[str, Any]) -> bool:
        """
        Update element render data with efficient change notifications.
        Returns True if successful, False if element doesn't exist.
        """
        if element_id not in self._elements:
            self.logger.warning(f"Element {element_id} not found for update")
            return False

        try:
            # Update element data
            element = self._elements[element_id]
            updated_element = ChartElement(
                element_id=element.element_id,
                element_type=element.element_type,
                render_data=render_data,
                bounds=render_data.get("bounds", element.bounds),
                visible=element.visible,
                z_order=element.z_order,
            )

            self._elements[element_id] = updated_element

            # Update cached bounds
            self._update_bounds_cache(updated_element)

            # Emit change signal
            row = self._element_order.index(element_id)
            top_left = self.index(row, 0)
            bottom_right = self.index(row, self.columnCount() - 1)
            self.dataChanged.emit(top_left, bottom_right)

            # Schedule batch update for efficient rendering
            self._schedule_batch_update(element_id)

            self.logger.debug(f"Updated element: {element_id}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to update element {element_id}: {e}")
            return False

    def get_element(self, element_id: str) -> Optional[ChartElement]:
        """Get element by ID."""
        return self._elements.get(element_id)

    def get_elements_by_type(
        self, element_type: ChartElementType
    ) -> List[ChartElement]:
        """Get all elements of specified type."""
        return [
            elem
            for elem in self._elements.values()
            if elem.element_type == element_type
        ]

    def get_visible_elements(self) -> List[ChartElement]:
        """Get all visible elements sorted by z-order."""
        visible = [elem for elem in self._elements.values() if elem.visible]
        return sorted(visible, key=lambda x: x.z_order)

    def get_bounds(self) -> Dict[str, float]:
        """Get cached chart bounds."""
        return self._cached_bounds.copy()

    def get_dirty_regions(self) -> List[Dict[str, float]]:
        """Get dirty regions for efficient repainting."""
        return self._dirty_regions.copy()

    def clear_dirty_regions(self):
        """Clear dirty regions after rendering."""
        self._dirty_regions.clear()

    def _update_bounds_cache(self, element: ChartElement):
        """Update cached bounds with new element."""
        element_bounds = element.bounds

        if not self._cached_bounds:
            self._cached_bounds = element_bounds.copy()
        else:
            self._cached_bounds["x_min"] = min(
                self._cached_bounds.get("x_min", 0), element_bounds.get("x_min", 0)
            )
            self._cached_bounds["x_max"] = max(
                self._cached_bounds.get("x_max", 1), element_bounds.get("x_max", 1)
            )
            self._cached_bounds["y_min"] = min(
                self._cached_bounds.get("y_min", 0), element_bounds.get("y_min", 0)
            )
            self._cached_bounds["y_max"] = max(
                self._cached_bounds.get("y_max", 1), element_bounds.get("y_max", 1)
            )

        self.bounds_changed.emit(self._cached_bounds)

    def _recalculate_bounds_cache(self):
        """Recalculate bounds cache from all elements."""
        if not self._elements:
            self._cached_bounds.clear()
            return

        bounds_list = [elem.bounds for elem in self._elements.values() if elem.bounds]
        if not bounds_list:
            return

        self._cached_bounds = {
            "x_min": min(b.get("x_min", 0) for b in bounds_list),
            "x_max": max(b.get("x_max", 1) for b in bounds_list),
            "y_min": min(b.get("y_min", 0) for b in bounds_list),
            "y_max": max(b.get("y_max", 1) for b in bounds_list),
        }

        self.bounds_changed.emit(self._cached_bounds)

    def _schedule_batch_update(self, element_id: str):
        """Schedule element for batch update to improve performance."""
        if element_id not in self._pending_updates:
            self._pending_updates.append(element_id)

        # Restart timer for batching
        self._batch_update_timer.start(16)  # ~60 FPS update rate

    def _process_batch_updates(self):
        """Process all pending updates in a batch."""
        if not self._pending_updates:
            return

        try:
            # Calculate dirty regions for efficient repainting
            for element_id in self._pending_updates:
                element = self._elements.get(element_id)
                if element and element.bounds:
                    self._dirty_regions.append(element.bounds)

            # Emit batch update signals
            self.dirty_region_changed.emit(
                {"regions": self._dirty_regions, "count": len(self._pending_updates)}
            )

            # Clear pending updates
            self._pending_updates.clear()

            self.logger.debug(
                f"Processed batch update for {len(self._dirty_regions)} regions"
            )

        except Exception as e:
            self.logger.error(f"Error processing batch updates: {e}")

    def clear_all(self):
        """Clear all elements from the model."""
        if not self._elements:
            return

        self.beginResetModel()
        self._elements.clear()
        self._element_order.clear()
        self._cached_bounds.clear()
        self._dirty_regions.clear()
        self._pending_updates.clear()
        self.endResetModel()

        self.logger.info("Cleared all chart elements")

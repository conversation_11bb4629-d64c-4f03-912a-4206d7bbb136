"""
Enterprise-grade chart rendering items with zero computation in UI layer.
All data is pre-computed in backend - UI only renders.
"""

import pyqtgraph as pg
from PyQt6.QtCore import QRectF, QPointF
from PyQt6.QtGui import QP<PERSON>ter, QPen, QBrush, QColor
from typing import List, Dict, Any, Tuple
import logging


class PreComputedCandlestickItem(pg.GraphicsObject):
    """
    Enterprise-grade candlestick item that renders pre-computed data.
    Zero mathematical operations - only rendering pre-computed values.
    """

    def __init__(self, render_data: List[Dict[str, Any]], bounds: Dict[str, float]):
        super().__init__()
        self.render_data = render_data
        self.bounds = bounds
        self.logger = self._setup_logging()

        # Pre-render the candlesticks using backend data
        self.picture = pg.QtGui.QPicture()
        self._render_candlesticks()

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for candlestick rendering."""
        logger = logging.getLogger("PreComputedCandlestickItem")
        logger.setLevel(logging.WARNING)  # Only log warnings/errors to avoid spam
        return logger

    def _render_candlesticks(self):
        """Render candlesticks using pre-computed data from backend."""
        if not self.render_data:
            return

        painter = QPainter(self.picture)
        try:
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)

            # Render each pre-computed candlestick
            for candle_data in self.render_data:
                if not candle_data.get("is_valid", True):
                    continue

                self._render_single_candle(painter, candle_data)

        except Exception as e:
            self.logger.error(f"Error rendering candlesticks: {e}")
        finally:
            if painter.isActive():
                painter.end()

    def _render_single_candle(self, painter: QPainter, candle_data: Dict[str, Any]):
        """Render a single candlestick using pre-computed data."""
        try:
            x = float(candle_data["x"])
            open_val = float(candle_data["open"])
            high_val = float(candle_data["high"])
            low_val = float(candle_data["low"])
            close_val = float(candle_data["close"])

            body_color = candle_data.get("body_color", "#00ff00")
            wick_color = candle_data.get("wick_color", "#00ff00")

            # Use pre-computed width
            width = 0.6

            # Draw wick (high-low line)
            painter.setPen(QPen(QColor(wick_color), 1))
            painter.drawLine(QPointF(x, low_val), QPointF(x, high_val))

            # Draw body (open-close rectangle)
            body_top = max(open_val, close_val)
            body_bottom = min(open_val, close_val)
            body_height = body_top - body_bottom

            if body_height > 0:
                painter.setPen(QPen(QColor(body_color), 1))
                painter.setBrush(QBrush(QColor(body_color)))
                painter.drawRect(QRectF(x - width / 2, body_bottom, width, body_height))
            else:
                # Doji - draw a line
                painter.setPen(QPen(QColor(body_color), 2))
                painter.drawLine(
                    QPointF(x - width / 2, open_val), QPointF(x + width / 2, open_val)
                )

        except Exception as e:
            self.logger.warning(f"Error rendering single candle: {e}")

    def paint(self, painter, option, widget):
        """Paint the pre-rendered candlesticks."""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self) -> QRectF:
        """Return pre-computed bounding rectangle."""
        return QRectF(
            self.bounds["x_min"],
            self.bounds["y_min"],
            self.bounds["x_max"] - self.bounds["x_min"],
            self.bounds["y_max"] - self.bounds["y_min"],
        )


class PreComputedStairstepItem(pg.GraphicsObject):
    """
    Enterprise-grade stairstep line item using pre-computed data.
    Zero mathematical operations in UI layer.
    """

    def __init__(
        self, stairstep_data: Dict[str, Any], color: str = "#831483", width: int = 2
    ):
        super().__init__()
        self.stairstep_data = stairstep_data
        self.color = color
        self.width = width
        self.logger = self._setup_logging()

        # Pre-render the stairstep line
        self.picture = pg.QtGui.QPicture()
        self._render_stairstep()

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for stairstep rendering."""
        logger = logging.getLogger("PreComputedStairstepItem")
        logger.setLevel(logging.WARNING)
        return logger

    def _render_stairstep(self):
        """Render stairstep line using pre-computed data."""
        if not self.stairstep_data.get("is_valid", False):
            return

        x_values = self.stairstep_data.get("x_values", [])
        y_values = self.stairstep_data.get("y_values", [])

        if len(x_values) != len(y_values) or len(x_values) < 2:
            return

        painter = QPainter(self.picture)
        try:
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            painter.setPen(QPen(QColor(self.color), self.width))

            # Draw stairstep line using pre-computed points
            for i in range(len(x_values) - 1):
                x1, y1 = float(x_values[i]), float(y_values[i])
                x2, y2 = float(x_values[i + 1]), float(y_values[i + 1])
                painter.drawLine(QPointF(x1, y1), QPointF(x2, y2))

        except Exception as e:
            self.logger.error(f"Error rendering stairstep: {e}")
        finally:
            if painter.isActive():
                painter.end()

    def paint(self, painter, option, widget):
        """Paint the pre-rendered stairstep line."""
        painter.drawPicture(0, 0, self.picture)

    def boundingRect(self) -> QRectF:
        """Return bounding rectangle from pre-computed data."""
        if not self.stairstep_data.get("is_valid", False):
            return QRectF()

        x_values = self.stairstep_data.get("x_values", [])
        y_values = self.stairstep_data.get("y_values", [])

        if not x_values or not y_values:
            return QRectF()

        # Use pre-computed bounds if available
        if "bounds" in self.stairstep_data:
            bounds = self.stairstep_data["bounds"]
            return QRectF(
                bounds["x_min"],
                bounds["y_min"],
                bounds["x_max"] - bounds["x_min"],
                bounds["y_max"] - bounds["y_min"],
            )

        # Fallback: extract bounds from data (should be avoided)
        x_min, x_max = min(x_values), max(x_values)
        y_min, y_max = min(y_values), max(y_values)
        return QRectF(x_min, y_min, x_max - x_min, y_max - y_min)


class PreComputedAnnotationManager:
    """
    Enterprise-grade annotation manager using pre-computed display data.
    No mathematical operations - only rendering pre-computed annotations.
    """

    def __init__(self, plot_widget):
        self.plot_widget = plot_widget
        self.annotation_items = []
        self.logger = self._setup_logging()

        # Pre-create pen cache for performance
        self._pen_cache = {}

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for annotation manager."""
        logger = logging.getLogger("PreComputedAnnotationManager")
        logger.setLevel(logging.WARNING)
        return logger

    def clear_annotations(self):
        """Clear all annotations efficiently."""
        for item in self.annotation_items:
            try:
                self.plot_widget.removeItem(item)
            except Exception as e:
                self.logger.warning(f"Error removing annotation item: {e}")
        self.annotation_items.clear()

    def _get_cached_pen(self, color: str, width: int, style: str) -> QPen:
        """Get cached pen for performance."""
        cache_key = (color, width, style)
        if cache_key not in self._pen_cache:
            qt_style = (
                pg.QtCore.Qt.PenStyle.DashLine
                if style == "dash"
                else pg.QtCore.Qt.PenStyle.SolidLine
            )
            self._pen_cache[cache_key] = pg.mkPen(
                color=color, width=width, style=qt_style
            )
        return self._pen_cache[cache_key]

    def render_annotations(self, display_data: List[Dict[str, Any]]):
        """Render annotations from pre-computed display data."""
        self.clear_annotations()

        if not display_data:
            return

        try:
            for item_data in display_data:
                self._render_single_annotation(item_data)
        except Exception as e:
            self.logger.error(f"Error rendering annotations: {e}")

    def _render_single_annotation(self, item_data: Dict[str, Any]):
        """Render a single annotation item."""
        try:
            item_type = item_data.get("type")

            if item_type == "ray":
                # Render ray line
                pen = self._get_cached_pen(
                    item_data["color"], item_data["width"], item_data["style"]
                )

                ray_line = self.plot_widget.plot(
                    [item_data["start_x"], item_data["end_x"]],
                    [item_data["start_y"], item_data["end_y"]],
                    pen=pen,
                )

                # Store annotation type for future reference
                ray_line.annotation_type = item_data.get("annotation_type", "unknown")
                self.annotation_items.append(ray_line)

            elif item_type == "label":
                # Render text label
                text_item = pg.TextItem(
                    text=item_data["text"],
                    color=item_data.get("color", "#ffffff"),
                    anchor=item_data.get("anchor", (0.5, 0.5)),
                )

                text_item.setPos(item_data["x"], item_data["y"])

                if "font_size" in item_data:
                    font = text_item.textItem.font()
                    font.setPointSize(item_data["font_size"])
                    text_item.setFont(font)

                self.plot_widget.addItem(text_item)
                self.annotation_items.append(text_item)

        except Exception as e:
            self.logger.warning(f"Error rendering annotation item: {e}")

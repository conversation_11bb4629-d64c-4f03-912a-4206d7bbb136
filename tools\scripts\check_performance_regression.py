#!/usr/bin/env python3
"""
Performance regression detection for CI/CD pipeline.
Compares current benchmark results against baseline and fails if regression exceeds threshold.
"""

import json
import argparse
import sys
import statistics
from pathlib import Path
from typing import Dict, Any, List, Tuple
import logging


class PerformanceRegression:
    """Detects performance regressions in benchmark results."""

    def __init__(self, threshold_percent: float = 5.0):
        self.threshold_percent = threshold_percent
        self.logger = self._setup_logging()

    def _setup_logging(self) -> logging.Logger:
        """Setup logging for regression checker."""
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        return logging.getLogger(__name__)

    def load_benchmark_results(self, file_path: str) -> Dict[str, Any]:
        """Load benchmark results from JSON file."""
        try:
            with open(file_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.error(f"Benchmark file not found: {file_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in benchmark file {file_path}: {e}")
            return {}

    def extract_python_benchmarks(self, results: Dict[str, Any]) -> Dict[str, float]:
        """Extract benchmark metrics from pytest-benchmark results."""
        benchmarks = {}

        if "benchmarks" in results:
            for benchmark in results["benchmarks"]:
                name = benchmark.get("name", "unknown")
                stats = benchmark.get("stats", {})

                # Use mean time as primary metric
                mean_time = stats.get("mean", 0.0)
                if mean_time > 0:
                    benchmarks[name] = mean_time

        return benchmarks

    def extract_cpp_benchmarks(self, results: Dict[str, Any]) -> Dict[str, float]:
        """Extract benchmark metrics from Google Benchmark results."""
        benchmarks = {}

        if "benchmarks" in results:
            for benchmark in results["benchmarks"]:
                name = benchmark.get("name", "unknown")
                real_time = benchmark.get("real_time", 0.0)

                if real_time > 0:
                    benchmarks[name] = real_time

        return benchmarks

    def compare_benchmarks(
        self, current: Dict[str, float], baseline: Dict[str, float]
    ) -> List[Tuple[str, float, float, float]]:
        """
        Compare current benchmarks against baseline.

        Returns:
            List of (benchmark_name, current_time, baseline_time, regression_percent)
        """
        regressions = []

        for name, current_time in current.items():
            if name in baseline:
                baseline_time = baseline[name]

                # Calculate percentage change (positive = regression)
                if baseline_time > 0:
                    change_percent = (
                        (current_time - baseline_time) / baseline_time
                    ) * 100

                    if change_percent > self.threshold_percent:
                        regressions.append(
                            (name, current_time, baseline_time, change_percent)
                        )

        return regressions

    def check_regression(self, current_file: str, baseline_file: str = None) -> bool:
        """
        Check for performance regressions.

        Args:
            current_file: Path to current benchmark results
            baseline_file: Path to baseline benchmark results (optional)

        Returns:
            True if no significant regressions found, False otherwise
        """
        current_results = self.load_benchmark_results(current_file)
        if not current_results:
            self.logger.error("No current benchmark results found")
            return False

        # Extract benchmarks based on file format
        if "benchmarks" in current_results and isinstance(
            current_results["benchmarks"], list
        ):
            if any("stats" in b for b in current_results["benchmarks"]):
                current_benchmarks = self.extract_python_benchmarks(current_results)
            else:
                current_benchmarks = self.extract_cpp_benchmarks(current_results)
        else:
            self.logger.error("Unrecognized benchmark format")
            return False

        if not current_benchmarks:
            self.logger.warning("No benchmarks found in current results")
            return True

        self.logger.info(
            f"Found {len(current_benchmarks)} benchmarks in current results"
        )

        # If no baseline provided, try to find historical data
        if baseline_file is None:
            baseline_file = self._find_baseline_file(current_file)

        if baseline_file and Path(baseline_file).exists():
            baseline_results = self.load_benchmark_results(baseline_file)

            if baseline_results:
                if "benchmarks" in baseline_results and isinstance(
                    baseline_results["benchmarks"], list
                ):
                    if any("stats" in b for b in baseline_results["benchmarks"]):
                        baseline_benchmarks = self.extract_python_benchmarks(
                            baseline_results
                        )
                    else:
                        baseline_benchmarks = self.extract_cpp_benchmarks(
                            baseline_results
                        )

                    regressions = self.compare_benchmarks(
                        current_benchmarks, baseline_benchmarks
                    )

                    if regressions:
                        self.logger.error(f"Performance regressions detected:")
                        for name, current, baseline, change in regressions:
                            self.logger.error(
                                f"  {name}: {current:.6f}s vs {baseline:.6f}s "
                                f"({change:+.1f}% regression)"
                            )
                        return False
                    else:
                        self.logger.info(
                            "No significant performance regressions detected"
                        )
                        return True

        # No baseline available - just report current performance
        self.logger.info("No baseline available for comparison")
        self.logger.info("Current benchmark results:")
        for name, time in current_benchmarks.items():
            self.logger.info(f"  {name}: {time:.6f}s")

        return True

    def _find_baseline_file(self, current_file: str) -> str:
        """Try to find a baseline file for comparison."""
        current_path = Path(current_file)

        # Look for baseline in same directory
        baseline_candidates = [
            current_path.parent / "baseline-benchmark.json",
            current_path.parent / "previous-benchmark.json",
            current_path.parent / f"baseline-{current_path.name}",
        ]

        for candidate in baseline_candidates:
            if candidate.exists():
                return str(candidate)

        return None

    def generate_performance_report(self, current_file: str, output_file: str = None):
        """Generate a detailed performance report."""
        current_results = self.load_benchmark_results(current_file)

        if not current_results:
            return

        # Extract benchmarks
        if "benchmarks" in current_results and isinstance(
            current_results["benchmarks"], list
        ):
            if any("stats" in b for b in current_results["benchmarks"]):
                benchmarks = self.extract_python_benchmarks(current_results)
                report_type = "Python"
            else:
                benchmarks = self.extract_cpp_benchmarks(current_results)
                report_type = "C++"
        else:
            return

        # Generate report
        report = {
            "type": report_type,
            "timestamp": current_results.get("datetime", "unknown"),
            "total_benchmarks": len(benchmarks),
            "benchmarks": [],
        }

        for name, time in sorted(benchmarks.items()):
            report["benchmarks"].append(
                {
                    "name": name,
                    "time_seconds": time,
                    "time_ms": time * 1000,
                    "ops_per_second": 1.0 / time if time > 0 else 0,
                }
            )

        # Calculate statistics
        times = list(benchmarks.values())
        if times:
            report["statistics"] = {
                "mean_time": statistics.mean(times),
                "median_time": statistics.median(times),
                "min_time": min(times),
                "max_time": max(times),
                "std_dev": statistics.stdev(times) if len(times) > 1 else 0,
            }

        # Write report
        if output_file:
            with open(output_file, "w") as f:
                json.dump(report, f, indent=2)
            self.logger.info(f"Performance report written to {output_file}")
        else:
            print(json.dumps(report, indent=2))


def main():
    """Main entry point for performance regression checker."""
    parser = argparse.ArgumentParser(
        description="Check for performance regressions in benchmark results"
    )
    parser.add_argument(
        "--current", required=True, help="Path to current benchmark results JSON file"
    )
    parser.add_argument(
        "--baseline", help="Path to baseline benchmark results JSON file"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=5.0,
        help="Regression threshold percentage (default: 5.0)",
    )
    parser.add_argument(
        "--fail-on-regression",
        action="store_true",
        help="Exit with error code if regression detected",
    )
    parser.add_argument("--report", help="Generate detailed performance report to file")

    args = parser.parse_args()

    checker = PerformanceRegression(threshold_percent=args.threshold)

    # Generate report if requested
    if args.report:
        checker.generate_performance_report(args.current, args.report)

    # Check for regressions
    no_regression = checker.check_regression(args.current, args.baseline)

    if args.fail_on_regression and not no_regression:
        sys.exit(1)

    sys.exit(0)


if __name__ == "__main__":
    main()

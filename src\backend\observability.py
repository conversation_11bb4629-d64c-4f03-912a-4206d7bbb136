"""
Enterprise-grade structured observability with correlation IDs and metrics.
Implements comprehensive telemetry for request tracing and performance monitoring.
"""

import logging
import time
import uuid
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from contextlib import contextmanager
from collections import defaultdict
import json
from datetime import datetime


@dataclass
class CorrelationContext:
    """Correlation context for request tracing."""

    correlation_id: str
    request_id: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    operation: Optional[str] = None
    start_time: float = 0.0
    parent_span_id: Optional[str] = None

    def __post_init__(self):
        if self.start_time == 0.0:
            self.start_time = time.perf_counter()


class CorrelationManager:
    """Thread-safe correlation context manager."""

    def __init__(self):
        self._local = threading.local()
        self._contexts: Dict[str, CorrelationContext] = {}
        self._lock = threading.Lock()

    def create_context(
        self, operation: str, user_id: Optional[str] = None
    ) -> CorrelationContext:
        """Create new correlation context."""
        correlation_id = str(uuid.uuid4())
        request_id = str(uuid.uuid4())

        context = CorrelationContext(
            correlation_id=correlation_id,
            request_id=request_id,
            user_id=user_id,
            operation=operation,
            start_time=time.perf_counter(),
        )

        with self._lock:
            self._contexts[correlation_id] = context

        return context

    def set_current_context(self, context: CorrelationContext):
        """Set current thread's correlation context."""
        self._local.context = context

    def get_current_context(self) -> Optional[CorrelationContext]:
        """Get current thread's correlation context."""
        return getattr(self._local, "context", None)

    def clear_context(self, correlation_id: str):
        """Clear correlation context."""
        with self._lock:
            self._contexts.pop(correlation_id, None)

        if (
            hasattr(self._local, "context")
            and self._local.context.correlation_id == correlation_id
        ):
            delattr(self._local, "context")

    @contextmanager
    def trace_operation(self, operation: str, user_id: Optional[str] = None):
        """Context manager for tracing operations."""
        context = self.create_context(operation, user_id)
        old_context = self.get_current_context()

        try:
            self.set_current_context(context)
            yield context
        finally:
            self.clear_context(context.correlation_id)
            if old_context:
                self.set_current_context(old_context)


class MetricsCollector:
    """Enterprise-grade metrics collector with Prometheus-style metrics."""

    def __init__(self):
        self._counters: Dict[str, float] = defaultdict(float)
        self._histograms: Dict[str, List[float]] = defaultdict(list)
        self._gauges: Dict[str, float] = {}
        self._lock = threading.Lock()

        # Performance metrics
        self._request_durations: Dict[str, List[float]] = defaultdict(list)
        self._request_counts: Dict[str, int] = defaultdict(int)
        self._error_counts: Dict[str, int] = defaultdict(int)

    def increment_counter(
        self, name: str, value: float = 1.0, labels: Optional[Dict[str, str]] = None
    ):
        """Increment counter metric."""
        metric_name = self._format_metric_name(name, labels)
        with self._lock:
            self._counters[metric_name] += value

    def record_histogram(
        self, name: str, value: float, labels: Optional[Dict[str, str]] = None
    ):
        """Record histogram value."""
        metric_name = self._format_metric_name(name, labels)
        with self._lock:
            self._histograms[metric_name].append(value)

            # Keep only last 1000 values for memory efficiency
            if len(self._histograms[metric_name]) > 1000:
                self._histograms[metric_name] = self._histograms[metric_name][-1000:]

    def set_gauge(
        self, name: str, value: float, labels: Optional[Dict[str, str]] = None
    ):
        """Set gauge value."""
        metric_name = self._format_metric_name(name, labels)
        with self._lock:
            self._gauges[metric_name] = value

    def record_request_duration(
        self, operation: str, duration: float, success: bool = True
    ):
        """Record request duration and count."""
        with self._lock:
            self._request_durations[operation].append(duration)
            self._request_counts[operation] += 1

            if not success:
                self._error_counts[operation] += 1

    def get_percentile(
        self, name: str, percentile: float, labels: Optional[Dict[str, str]] = None
    ) -> float:
        """Get percentile value from histogram."""
        metric_name = self._format_metric_name(name, labels)
        with self._lock:
            values = self._histograms.get(metric_name, [])
            if not values:
                return 0.0

            sorted_values = sorted(values)
            index = int(len(sorted_values) * percentile / 100)
            return sorted_values[min(index, len(sorted_values) - 1)]

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        with self._lock:
            summary = {
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "request_metrics": {},
            }

            # Calculate request metrics
            for operation, durations in self._request_durations.items():
                if durations:
                    summary["request_metrics"][operation] = {
                        "count": self._request_counts[operation],
                        "error_count": self._error_counts[operation],
                        "avg_duration_ms": sum(durations) / len(durations) * 1000,
                        "p95_duration_ms": self._calculate_percentile(durations, 95)
                        * 1000,
                        "p99_duration_ms": self._calculate_percentile(durations, 99)
                        * 1000,
                        "error_rate": (
                            self._error_counts[operation]
                            / self._request_counts[operation]
                            if self._request_counts[operation] > 0
                            else 0
                        ),
                    }

            return summary

    def _format_metric_name(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Format metric name with labels."""
        if not labels:
            return name

        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"

    def _calculate_percentile(self, values: List[float], percentile: float) -> float:
        """Calculate percentile from list of values."""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]


class StructuredLogger:
    """Enterprise-grade structured logger with correlation ID support."""

    def __init__(
        self,
        name: str,
        correlation_manager: CorrelationManager,
        metrics_collector: MetricsCollector,
    ):
        self.name = name
        self.correlation_manager = correlation_manager
        self.metrics_collector = metrics_collector
        self.logger = logging.getLogger(name)

        # Setup structured formatter
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = StructuredFormatter(correlation_manager)
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

    def info(self, message: str, **kwargs):
        """Log info message with correlation context."""
        self._log(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """Log warning message with correlation context."""
        self._log(logging.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        """Log error message with correlation context."""
        self._log(logging.ERROR, message, **kwargs)
        self.metrics_collector.increment_counter(
            "errors_total", labels={"module": self.name}
        )

    def debug(self, message: str, **kwargs):
        """Log debug message with correlation context."""
        self._log(logging.DEBUG, message, **kwargs)

    def _log(self, level: int, message: str, **kwargs):
        """Internal logging method with structured data."""
        context = self.correlation_manager.get_current_context()

        extra_data = {
            "component": self.name,  # Use 'component' instead of 'module' to avoid conflict
            "timestamp_us": int(time.time() * 1_000_000),  # Microsecond precision
            **kwargs,
        }

        if context:
            extra_data.update(
                {
                    "correlation_id": context.correlation_id,
                    "request_id": context.request_id,
                    "operation": context.operation,
                    "user_id": context.user_id,
                    "session_id": context.session_id,
                }
            )

        self.logger.log(level, message, extra=extra_data)


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""

    def __init__(self, correlation_manager: CorrelationManager):
        super().__init__()
        self.correlation_manager = correlation_manager

    def format(self, record):
        """Format log record as structured JSON."""
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "component": getattr(record, "component", record.name),
            "message": record.getMessage(),
        }

        # Add correlation context if available
        if hasattr(record, "correlation_id"):
            log_data["correlation_id"] = record.correlation_id
        if hasattr(record, "request_id"):
            log_data["request_id"] = record.request_id
        if hasattr(record, "operation"):
            log_data["operation"] = record.operation
        if hasattr(record, "user_id") and record.user_id:
            log_data["user_id"] = record.user_id
        if hasattr(record, "timestamp_us"):
            log_data["timestamp_us"] = record.timestamp_us

        # Add any extra fields
        for key, value in record.__dict__.items():
            if key not in [
                "name",
                "msg",
                "args",
                "levelname",
                "levelno",
                "pathname",
                "filename",
                "module",
                "lineno",
                "funcName",
                "created",
                "msecs",
                "relativeCreated",
                "thread",
                "threadName",
                "processName",
                "process",
                "getMessage",
                "exc_info",
                "exc_text",
                "stack_info",
                "correlation_id",
                "request_id",
                "operation",
                "user_id",
                "timestamp_us",
                "component",
            ]:
                log_data[key] = value

        return json.dumps(log_data, default=str)


# Global instances
correlation_manager = CorrelationManager()
metrics_collector = MetricsCollector()


def get_structured_logger(name: str) -> StructuredLogger:
    """Get structured logger instance."""
    return StructuredLogger(name, correlation_manager, metrics_collector)


@contextmanager
def trace_operation(operation: str, user_id: Optional[str] = None):
    """Context manager for tracing operations with metrics."""
    start_time = time.perf_counter()
    success = True

    with correlation_manager.trace_operation(operation, user_id) as context:
        try:
            metrics_collector.increment_counter(
                "requests_total", labels={"operation": operation}
            )
            yield context
        except Exception as e:
            success = False
            metrics_collector.increment_counter(
                "errors_total", labels={"operation": operation}
            )
            raise
        finally:
            duration = time.perf_counter() - start_time
            metrics_collector.record_request_duration(operation, duration, success)
            metrics_collector.record_histogram(
                "request_duration_seconds", duration, labels={"operation": operation}
            )
